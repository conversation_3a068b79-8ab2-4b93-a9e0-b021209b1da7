<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx">
  <sheet id="factoryAuditTemplateView" position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factoryAuditTemplateView" label="Checklist Templates" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,12">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,14">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>FactoryAuditTemplate</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,15">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,entityName:docType:string,entityVersion:entityVersion:integer,refNo:refNo:string,version:version:integer</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,19">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,24">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,25">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,26">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,27">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,28">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,29">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,30">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,31">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,32">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,33">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,34">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,35">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,36">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,37">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,38">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,39">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,40">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,41">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,45">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,46">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>applyTo:applyTo.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,47">
          <id>table</id>
          <label>Table</label>
          <type>CodeList</type>
          <format>bookName=FACTORY_AUDIT_TABLE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TABLE_REQ_CHECKLIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>table:tableReqChecklist.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,48">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>type:type.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,49">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isAllowAddDeleteReq:isAllowAddDeleteReq:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,50">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,51">
          <id>auditCategory</id>
          <label>Audit Category/Skill</label>
          <type>CodeList</type>
          <format>bookName=AUDIT_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.audit_Category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,52">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,53">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,54">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,55">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,56">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,57">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,58">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,59">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,60">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,61">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,62">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:docRef:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,63">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,64">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>cpmDocId:cpmDoc.cpmId:string,refDocRefNo:cpmDoc.refDocRefNo:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,65">
          <id>createdOn</id>
          <label>Created On</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,66">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FAT.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factoryAuditTemplateView,67">
          <id>checklistMandatoryType</id>
          <label>Mandatory Checking on Details</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.CHECKLIST_MANDATORY_TYPE_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>checklistMandatoryType:checklistMandatoryTypeValue:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popFactoryAuditTemplateView" position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView">
    <ViewDefinition advancedSearchId="" description="Template Lookup" id="popFactoryAuditTemplateView" label="Template Lookup" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,1" queryId="listPopFactAuditTmplView" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,11">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (editingStatus =|STRING ''confirmed'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,13">
          <id>APPLY_TO_PARAMS</id>
          <label/>
          <value>templateId=id&amp;templateApplyToGridId=factoryAuditTemplateRules&amp;templateConditionFieldId=condition</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,14">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string,editingStatus=confirmed=string</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,15">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>FactoryAuditTemplate</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,16">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,entityName:docType:string,entityVersion:entityVersion:integer,refNo:refNo:string,version:version:integer,updateUserName:updatedBy:string</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,24">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,25">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>applyTo:applyTo.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,26">
          <id>table</id>
          <label>Table</label>
          <type>CodeList</type>
          <format>bookName=FACTORY_AUDIT_TABLE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.TABLE_REQ_CHECKLIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>table:tableReqChecklist.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,27">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>type:type.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,28">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isAllowAddDeleteReq:isAllowAddDeleteReq:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,29">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,30">
          <id>auditCategory</id>
          <label>Audit Category/Skill</label>
          <type>CodeList</type>
          <format>bookName=AUDIT_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.audit_Category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>auditCategory:auditCategory.code:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,31">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FATEMPLATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,32">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,33">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment/>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>FATEMPLATE.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,34">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>155px</width>
          <visibility>0</visibility>
          <mappedField>FATEMPLATE.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,35">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,36">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popFactoryAuditTemplateView,37">
          <id>checklistMandatoryType</id>
          <label>Mandatory Checking on Details</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>FATEMPLATE.CHECKLIST_MANDATORY_TYPE_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>checklistMandatoryType:checklistMandatoryTypeValue:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus01View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus01View" label="Checklist Templates - Custom Status 1" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus01'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus01View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus02View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus02View" label="Checklist Templates - Custom Status 2" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus02'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus02View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus03View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus03View" label="Checklist Templates - Custom Status 3" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus03'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus03View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus04View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus04View" label="Checklist Templates - Custom Status 4" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus04'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus04View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus05View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus05View" label="Checklist Templates - Custom Status 5" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus05View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus06View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus06View" label="Checklist Templates - Custom Status 6" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus06'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus06View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus07View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus07View" label="Checklist Templates - Custom Status 7" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus07'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus07View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus08View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus08View" label="Checklist Templates - Custom Status 8" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus08'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus08View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus09View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus09View" label="Checklist Templates - Custom Status 9" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus09'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus09View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="factAuditTemForCustStatus10View" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View">
    <ViewDefinition advancedSearchId="" description="Factory Audit Template View" id="factAuditTemForCustStatus10View" label="Checklist Templates - Custom Status 10" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,1" queryId="listFactoryAuditTemplate" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus10'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,23">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,28">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,29">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,30">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,31">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,32">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,33">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,34">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,35">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,36">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,37">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,38">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=factoryAuditTemplate&amp;entityName=FactoryAuditTemplate</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,39">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,43">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,44">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>CodeList</type>
          <format>bookName=CHECKLIST_APPLY_TO</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,45">
          <id>type</id>
          <label>Type</label>
          <type>CodeList</type>
          <format>bookName=Checklist_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,46">
          <id>isAllowAddDeleteReq</id>
          <label>Allow Realtime Add/Remove Entries</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>FAT.IS_ALLOW_ADD_DELETE_REQ</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,47">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,48">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,49">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.factoryAuditTemplate.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,50">
          <id>FactoryAuditTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>FAT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,51">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,52">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>FAT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,53">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,54">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,55">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,56">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>FAT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,57">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>FAT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,58">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FAT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,59">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>FAT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,factAuditTemForCustStatus10View,60">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>FAT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popStatusView" position="factoryAuditTemplate_view.xlsx,popStatusView">
    <ViewDefinition advancedSearchId="" description="Status Lookup" id="popStatusView" label="Status Lookup" moduleId="factoryAuditTemplate" position="factoryAuditTemplate_view.xlsx,popStatusView,1" queryId="getCbxLabelByLabelIdAndLocale" searchCriterion="">
      <elements id="options">
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,20">
          <id>code</id>
          <label>Status Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="factoryAuditTemplate_view.xlsx,popStatusView,21">
          <id>name</id>
          <label>Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>155px</width>
          <visibility>1</visibility>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
