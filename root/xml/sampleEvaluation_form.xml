<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form module="sampleEvaluation" position="sampleEvaluation_form.xlsx">
  <sheet id="system" position="sampleEvaluation_form.xlsx,system">
    <ProjectInfo client="cnt" position="sampleEvaluation_form.xlsx,system,1" project="base" releaseNo="1.0a"/>
    <ProductVersion position="sampleEvaluation_form.xlsx,system,7">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,system,10">
          <updatedOn>09-Apr-2015</updatedOn>
          <summary>Creation</summary>
          <releaseNo>01-Jan-1900</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="form" position="sampleEvaluation_form.xlsx,form">
    <Form id="sampleEvaluationForm" label="Sample Evaluation" module="sampleEvaluation" position="sampleEvaluation_form.xlsx,form,1" version="1">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,8">
          <id>sampleEvaluationTabGroup</id>
          <label/>
          <type>TabGroup</type>
        </element>
      </elements>
    </Form>
    <TabGroup id="sampleEvaluationTabGroup" label="" position="sampleEvaluation_form.xlsx,form,11">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,18">
          <id>tabHeader</id>
          <label/>
          <type>Tab</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,19">
          <id>tabEvaluation</id>
          <label/>
          <type>Tab</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,20">
          <id>tabFitAssessments</id>
          <label/>
          <type>Tab</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,21">
          <id>tabImage</id>
          <label/>
          <type>Tab</type>
        </element>
      </elements>
    </TabGroup>
    <Toolbar position="sampleEvaluation_form.xlsx,form,25">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,28">
          <id>sampleEvaluationMenubar</id>
          <label/>
          <type>Menubar</type>
        </element>
      </elements>
    </Toolbar>
    <Menubar align="left" cssClass="" id="sampleEvaluationMenubar" label="" position="sampleEvaluation_form.xlsx,form,31">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,38">
          <id>editDoc</id>
          <label>Edit</label>
          <type>MenuItem</type>
          <action>EditDocAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,39">
          <id>amendDoc</id>
          <label>Amend</label>
          <type>MenuItem</type>
          <action>AmendDocAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,40">
          <id>baseSaveDoc</id>
          <label>Save</label>
          <type>MenuItem</type>
          <action>BaseSaveDocAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,41">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>MenuItem</type>
          <action>SaveAndConfirmAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,42">
          <id>discardDoc</id>
          <label>Cancel</label>
          <type>MenuItem</type>
          <action>DiscardDocAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,43">
          <id>printGroup</id>
          <label>Print</label>
          <type>MenuGroup</type>
          <action>printGroup</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,44">
          <id>exportGroup</id>
          <label>Export</label>
          <type>MenuGroup</type>
          <action>exportGroup</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,45">
          <id>markAsGroup</id>
          <label>Mark as</label>
          <type>MenuGroup</type>
          <action>markAsGroup</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,46">
          <id>actionsGroup</id>
          <label>Actions</label>
          <type>MenuGroup</type>
          <action>actionsGroup</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,47">
          <id>initializeCpm</id>
          <label>Initialize CPM</label>
          <type>MenuItem</type>
          <action>InitializeCpmAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,48">
          <id>sampleEvaluationSendToVendor</id>
          <label>Send to Vendor</label>
          <type>MenuItem</type>
          <action>SampleEvaluationSendToVendorAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,49">
          <id>sampleEvaluationSendToBuyer</id>
          <label>Send to Buyer</label>
          <type>MenuItem</type>
          <action>SampleEvaluationSendToBuyerAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,50">
          <id>moreGroup</id>
          <label>More</label>
          <type>MenuGroup</type>
          <action>moreGroup</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,51">
          <id>sampleRequestSE</id>
          <label>Sample Request</label>
          <type>MenuItem</type>
          <action>SampleRequestSEAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,52">
          <id>sampleEvaluationViewCapa</id>
          <label>View CAPA</label>
          <type>MenuItem</type>
          <action>SampleEvaluationViewCapaAction</action>
          <actionParams>viewName=correctiveActionPlansActiveView&amp;naviId=quality&amp;field=itemRef_sourcingRecordNo_vendorRef_factoryRef:orNull&amp;colunmId=itemRef_sourcingRecord_vendorCode_factCode</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,53">
          <id>openApprovalApprove</id>
          <label>Approve</label>
          <type>MenuItem</type>
          <action>OpenApprovalApproveAction</action>
          <actionParams>winId=popupApprovalApprove</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,54">
          <id>openApprovalReject</id>
          <label>Reject</label>
          <type>MenuItem</type>
          <action>OpenApprovalRejectAction</action>
          <actionParams>winId=popupApprovalReject</actionParams>
        </element>
      </elements>
    </Menubar>
    <Linkbar align="right" cssClass="" id="sampleEvaluationLinkbar" label="" position="sampleEvaluation_form.xlsx,form,57">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,64">
          <id>duplicateWindow</id>
          <label>Duplicate Window</label>
          <action/>
          <actionParams/>
          <rendererClass/>
          <image>duplicateWindow.png</image>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,65">
          <id>followDoc</id>
          <label>Follow</label>
          <action>FollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>follow.png</image>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,66">
          <id>unfollowDoc</id>
          <label>Unfollow</label>
          <action>UnfollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>unfollow.png</image>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,67">
          <id>addToFavorites</id>
          <label>Add to Favorites</label>
          <action>AddDocToFavoriteAction</action>
          <actionParams/>
          <rendererClass/>
          <image>favorites.png</image>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,68">
          <id>approval</id>
          <label>Approval</label>
          <action>OpenApprovalByDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>approval.gif</image>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,69">
          <id>relatedActivities</id>
          <label>Related Activities</label>
          <action>ShowRelatedDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>activities.png</image>
        </element>
      </elements>
    </Linkbar>
    <Header position="sampleEvaluation_form.xlsx,form,72">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,75">
          <id>docStatus</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>inactive:(inactive),active:,canceled:(canceled)</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,76">
          <id>headerSampleEvaluationNo</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>{sampleId} - {sampleTypeName}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.SampleEvaluationPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength>150</maxLength>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,77">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,78">
          <id>version</id>
          <label>Version</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format>{version}({editingStatus})</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,79">
          <id>headerIntegration</id>
          <label/>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,80">
          <id>sampleEvaluationLinkbar</id>
          <label/>
          <type>Linkbar</type>
          <align/>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
      </elements>
    </Header>
    <DropdownStores position="sampleEvaluation_form.xlsx,form,84">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,87">
          <id>sizeDDS</id>
          <label/>
          <action>GetDocChildrenStoreAction</action>
          <actionParams>field=item.itemSize&amp;targetField=isInactive&amp;targetValue=false</actionParams>
          <lazy/>
          <reload/>
        </element>
      </elements>
    </DropdownStores>
    <MenuGroup id="actionsGroup" label="Actions" position="sampleEvaluation_form.xlsx,form,91">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,98">
          <id>copyDoc</id>
          <label>Copy</label>
          <type>MenuItem</type>
          <action>CopyDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,99">
          <id>activateDoc</id>
          <label>Active</label>
          <type>MenuItem</type>
          <action>ActivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,100">
          <id>deactivateDoc</id>
          <label>Inactive</label>
          <type>MenuItem</type>
          <action>DeactivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,101">
          <id>cancelDoc</id>
          <label>Canceled</label>
          <type>MenuItem</type>
          <action>CancelDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="markAsGroup" label="Mark as" position="sampleEvaluation_form.xlsx,form,104">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,111">
          <id>markAsSubmit</id>
          <label>Submitted</label>
          <type>MenuItem</type>
          <action>MarkAsSubmitAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,112">
          <id>markAsInProgress</id>
          <label>In-Progress</label>
          <type>MenuItem</type>
          <action>MarkAsInProgressAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,113">
          <id>markAsApproved</id>
          <label>Approved</label>
          <type>MenuItem</type>
          <action>MarkAsApprovedAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,114">
          <id>markAsRejected</id>
          <label>Rejected</label>
          <type>MenuItem</type>
          <action>MarkAsRejectedAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,115">
          <id>markAsWaive</id>
          <label>Waive</label>
          <type>MenuItem</type>
          <action>MarkAsWaiveAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,116">
          <id>markAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus01DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,117">
          <id>markAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus02DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,118">
          <id>markAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus03DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,119">
          <id>markAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus04DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,120">
          <id>markAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus05DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,121">
          <id>markAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus06DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,122">
          <id>markAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus07DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,123">
          <id>markAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus08DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,124">
          <id>markAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus09DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,125">
          <id>markAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus10DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="moreGroup" label="More" position="sampleEvaluation_form.xlsx,form,128">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,135">
          <id>customDocAction01</id>
          <label>Custom Action 1</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,136">
          <id>customDocAction02</id>
          <label>Custom Action 2</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,137">
          <id>customDocAction03</id>
          <label>Custom Action 3</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,138">
          <id>customDocAction04</id>
          <label>Custom Action 4</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,139">
          <id>customDocAction05</id>
          <label>Custom Action 5</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,140">
          <id>customDocAction06</id>
          <label>Custom Action 6</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,141">
          <id>customDocAction07</id>
          <label>Custom Action 7</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,142">
          <id>customDocAction08</id>
          <label>Custom Action 8</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,143">
          <id>customDocAction09</id>
          <label>Custom Action 9</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,144">
          <id>customDocAction10</id>
          <label>Custom Action 10</label>
          <type>MenuItem</type>
          <action>SampleEvaluationCustom10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="printGroup" label="Print" position="sampleEvaluation_form.xlsx,form,147">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,154">
          <id>customPrint01</id>
          <label>Custom Print 01</label>
          <type>MenuItem</type>
          <action>CustomPrint01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,155">
          <id>customPrint02</id>
          <label>Custom Print 02</label>
          <type>MenuItem</type>
          <action>CustomPrint02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,156">
          <id>customPrint03</id>
          <label>Custom Print 03</label>
          <type>MenuItem</type>
          <action>CustomPrint03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,157">
          <id>customPrint04</id>
          <label>Custom Print 04</label>
          <type>MenuItem</type>
          <action>CustomPrint04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,158">
          <id>customPrint05</id>
          <label>Custom Print 05</label>
          <type>MenuItem</type>
          <action>CustomPrint05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,159">
          <id>customPrint06</id>
          <label>Custom Print 06</label>
          <type>MenuItem</type>
          <action>CustomPrint06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,160">
          <id>customPrint07</id>
          <label>Custom Print 07</label>
          <type>MenuItem</type>
          <action>CustomPrint07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,161">
          <id>customPrint08</id>
          <label>Custom Print 08</label>
          <type>MenuItem</type>
          <action>CustomPrint08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,162">
          <id>customPrint09</id>
          <label>Custom Print 09</label>
          <type>MenuItem</type>
          <action>CustomPrint09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,163">
          <id>customPrint10</id>
          <label>Custom Print 10</label>
          <type>MenuItem</type>
          <action>CustomPrint10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="exportGroup" label="Export" position="sampleEvaluation_form.xlsx,form,166">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,form,173">
          <id>customExport01</id>
          <label>Custom Export 01</label>
          <type>MenuItem</type>
          <action>CustomExport01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,174">
          <id>customExport02</id>
          <label>Custom Export 02</label>
          <type>MenuItem</type>
          <action>CustomExport02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,175">
          <id>customExport03</id>
          <label>Custom Export 03</label>
          <type>MenuItem</type>
          <action>CustomExport03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,176">
          <id>customExport04</id>
          <label>Custom Export 04</label>
          <type>MenuItem</type>
          <action>CustomExport04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,177">
          <id>customExport05</id>
          <label>Custom Export 05</label>
          <type>MenuItem</type>
          <action>CustomExport05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,178">
          <id>customExport06</id>
          <label>Custom Export 06</label>
          <type>MenuItem</type>
          <action>CustomExport06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,179">
          <id>customExport07</id>
          <label>Custom Export 07</label>
          <type>MenuItem</type>
          <action>CustomExport07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,180">
          <id>customExport08</id>
          <label>Custom Export 08</label>
          <type>MenuItem</type>
          <action>CustomExport08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,181">
          <id>customExport09</id>
          <label>Custom Export 09</label>
          <type>MenuItem</type>
          <action>CustomExport09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,form,182">
          <id>customExport10</id>
          <label>Custom Export 10</label>
          <type>MenuItem</type>
          <action>CustomExport10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
  </sheet>
  <sheet id="tabHeader" position="sampleEvaluation_form.xlsx,tabHeader">
    <Tab id="tabHeader" label="Header" position="sampleEvaluation_form.xlsx,tabHeader,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,tabHeader,8">
          <id>generalInfoSection</id>
          <label>General Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,9">
          <id>productInfoSection</id>
          <label>Product Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,10">
          <id>vendorInfoSection</id>
          <label>Vendor Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,11">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,12">
          <id>sampleDetailsSection</id>
          <label>Sample Details</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,13">
          <id>custInfoSection</id>
          <label>Customer Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,14">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,15">
          <id>classificationSection</id>
          <label>Classification</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,16">
          <id>responseDetailsSection</id>
          <label>Response Details</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,17">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,18">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,19">
          <id>sysCustFields</id>
          <label>Additional Information</label>
          <type>SysCustGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,20">
          <id>sampleEvaluationShareFile</id>
          <label>File</label>
          <type>Grid</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,21">
          <id>relatedEvaluations</id>
          <label>Related Evaluations</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="generalInfoSection" label="General Information" position="sampleEvaluation_form.xlsx,tabHeader,24" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,34">
          <id>requestNo</id>
          <label>Initial Request No.</label>
          <type>Hyperlink</type>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleRequest&amp;fieldId=sampleRequest&amp;naviModule=quality</actionParams>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,35">
          <id>trackerNo</id>
          <label>Tracker No.</label>
          <type>Hyperlink</type>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleTracker&amp;fieldId=sampleTracker&amp;naviModule=quality</actionParams>
          <group/>
          <mapping>sampleTracker.trackerNo</mapping>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,36">
          <id>sampleId</id>
          <label>Sample ID</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,37">
          <id>sampleVersion</id>
          <label>Ver.</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,38">
          <id>evaluationType</id>
          <label>Evaluate for</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,39">
          <id>sampleType</id>
          <label>Sample Type</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,40">
          <id>materialSampleType</id>
          <label>Sample Type</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,41">
          <id>materialType</id>
          <label>Material Type</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,42">
          <id>materialSubType</id>
          <label>Material sub-type</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,43">
          <id>component</id>
          <label>Material No.</label>
          <type>Selection</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{componentNo}</format>
          <readonlyFormat>{componentNo}</readonlyFormat>
          <readonly/>
          <single>TRUE</single>
          <viewName>popComponentView</viewName>
          <winTitle>Materials Lookup</winTitle>
          <viewParams/>
          <popupFormat>{componentNo}</popupFormat>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,44">
          <id>materialName</id>
          <label>Material Name</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,45">
          <id>sampleRequest</id>
          <label>Request No.</label>
          <type>Hyperlink</type>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleRequest&amp;fieldId=sampleRequest</actionParams>
          <group/>
          <mapping>sampleRequest.sampleRequestNo</mapping>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,46">
          <id>requestedQuantity</id>
          <label>Requested Quantity</label>
          <type>Number</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,47">
          <id>uom</id>
          <label>Requested UOM</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,48">
          <id>colorAndPattern</id>
          <label>Color / Pattern</label>
          <type>Selection</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{shortName}</format>
          <readonlyFormat>{shortName}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName>popupItemColorView</viewName>
          <winTitle>Item Color Lookup</winTitle>
          <viewParams>itemRefNo={item.refNo}&amp;amp;itemVersion={item.version}</viewParams>
          <popupFormat>{shortName}</popupFormat>
          <disableAutoSearch>TRUE</disableAutoSearch>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,49">
          <id>altColorAndPattern</id>
          <label>Alt. Color / Pattern</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,50">
          <id>sizeCode</id>
          <label>Size</label>
          <type>Dropdown</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{sizeDisplayName}</format>
          <readonlyFormat>{sizeDisplayName}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey>sizeDisplayName</comboKey>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,51">
          <id>altSizeCode</id>
          <label>Alt.Size</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,52">
          <id>dimension</id>
          <label>Dimension</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,53">
          <id>yardage</id>
          <label>Yardage</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,54">
          <id>weight</id>
          <label>Weight</label>
          <type>Text</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,55">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>Dropdown</type>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <mapping/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,56">
          <id>requestedDescription</id>
          <label>Request Description</label>
          <type>TextArea</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,57">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Selection</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{userName}</format>
          <readonlyFormat>{userName}</readonlyFormat>
          <readonly/>
          <single>TRUE</single>
          <viewName>lookupUserView</viewName>
          <winTitle>User Lookup</winTitle>
          <viewParams/>
          <popupFormat>{userName}</popupFormat>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,58">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <disableAutoSearch/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,59">
          <id>tag</id>
          <label>Tag</label>
          <type>Selection</type>
          <action/>
          <actionParams/>
          <group/>
          <mapping/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <single/>
          <viewName>popCodelistView</viewName>
          <winTitle/>
          <viewParams>name=TAG</viewParams>
          <popupFormat>{name}</popupFormat>
          <disableAutoSearch/>
          <comboKey/>
        </element>
      </elements>
    </Section>
    <Section id="vendorInfoSection" label="Vendor Information" position="sampleEvaluation_form.xlsx,tabHeader,62" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,72">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{businessName}</format>
          <readonlyFormat>{businessName}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single>TRUE</single>
          <viewName>popVendorView</viewName>
          <winTitle>Vendor Lookup</winTitle>
          <viewParams/>
          <popupFormat>{businessName}</popupFormat>
          <readonly>TRUE</readonly>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,73">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vendor.vendorCode</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,74">
          <id>specialInstruction</id>
          <label>Special Instruction</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,75">
          <id>vendorEmail</id>
          <label>Email</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,76">
          <id>vendorRating</id>
          <label>Vendor Rating</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vendor.vendorRatingName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,77">
          <id>factory</id>
          <label>Factory Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{businessName}</format>
          <readonlyFormat>{businessName}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single>TRUE</single>
          <viewName>popFactView</viewName>
          <winTitle>Factory Lookup</winTitle>
          <viewParams>vendorId={vendor.id}</viewParams>
          <popupFormat>{businessName}</popupFormat>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,78">
          <id>factCountryOfOrigin</id>
          <label>Country of Origin</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>sampleTracker.countryName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,79">
          <id>factRank</id>
          <label>Factory Rank</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>factory.rankName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,80">
          <id>factAssessmentLevel</id>
          <label>Assessment Level</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>factory.assessmentLevelName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
      </elements>
    </Section>
    <Section id="custInfoSection" label="Customer Information" position="sampleEvaluation_form.xlsx,tabHeader,83" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,93">
          <id>custId</id>
          <label>Customer Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>custId</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{businessName}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single>TRUE</single>
          <viewName>popCustView</viewName>
          <winTitle>Customer Lookup</winTitle>
          <viewParams/>
          <popupFormat>{businessName}</popupFormat>
          <readonly>TRUE</readonly>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,94">
          <id>custCode</id>
          <label>Customer ID</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>custId.custCode</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <disableAutoSearch/>
        </element>
      </elements>
    </Section>
    <Section id="classificationSection" label="Classification" position="sampleEvaluation_form.xlsx,tabHeader,98" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,108">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>sampleTracker.productCategory</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <extraParams>Selection_preOpenPopupAction=ConstructViewParamsForClassificationAction</extraParams>
        </element>
      </elements>
    </Section>
    <Section id="productInfoSection" label="Product Information" position="sampleEvaluation_form.xlsx,tabHeader,111" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,121">
          <id>itemImage</id>
          <label>Image</label>
          <type>Image</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.fileId</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,122">
          <id>item</id>
          <label>Item No.</label>
          <type>Selection</type>
          <winTitle>Item Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{itemNo}</format>
          <readonlyFormat>{itemNo}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <viewName>popItemView</viewName>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single>TRUE</single>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat>{itemNo}</popupFormat>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,123">
          <id>itemVersion</id>
          <label>Item Ver.</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.version</mapping>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,124">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.itemName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,125">
          <id>shortDescription</id>
          <label>Short Description</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.shortDesc</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,126">
          <id>itemDescription</id>
          <label>Item Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.itemDesc</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,127">
          <id>itemType</id>
          <label>Item Type</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,128">
          <id>season</id>
          <label>Season</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,129">
          <id>year</id>
          <label>Year</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,130">
          <id>itemBrand</id>
          <label>Brand</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.itemBrandName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,131">
          <id>itemUom</id>
          <label>UOM</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.defaultUomName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,132">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <winTitle/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;fieldId=defaultSourcingRecord</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>defaultSourcingRecord.sourcingRecordNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{sourcingRecordNo}</format>
          <readonlyFormat>{sourcingRecordNo}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,133">
          <id>projectName</id>
          <label>Project Name</label>
          <type>Hyperlink</type>
          <winTitle/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=project&amp;fieldId=project</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>project.projectName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,134">
          <id>projectNo</id>
          <label>Project No.</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
      </elements>
    </Section>
    <Section id="sampleDetailsSection" label="Sample Details" position="sampleEvaluation_form.xlsx,tabHeader,137" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,147">
          <id>sampleDeliverTo</id>
          <label>Deliver To</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,148">
          <id>dueDate</id>
          <label>Due Date</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,149">
          <id>attachment1</id>
          <label>Attachment 1</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,150">
          <id>attachment2</id>
          <label>Attachment 2</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,151">
          <id>attachment3</id>
          <label>Attachment 3</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,152">
          <id>receivedDate</id>
          <label>Received Date</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,153">
          <id>receivedQuantity</id>
          <label>Received Quantity</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,154">
          <id>sampleStatus</id>
          <label>Status</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,155">
          <id>sampleResult</id>
          <label>Result</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,156">
          <id>expiryDate</id>
          <label>Expiry Date</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,157">
          <id>instructions</id>
          <label>Instructions / Comments</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,158">
          <id>additionalComments</id>
          <label>Additional Comments</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,159">
          <id>displaySeq</id>
          <label>Display Seq.</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,160">
          <id>fileId</id>
          <label>Attachment</label>
          <type>Attach</type>
          <winTitle>fileId</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
      </elements>
    </Section>
    <Section id="responseDetailsSection" label="Response Details" position="sampleEvaluation_form.xlsx,tabHeader,163" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabHeader,173">
          <id>sentDate</id>
          <label>Sent Date</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,174">
          <id>sentQuantity</id>
          <label>Sent Quantity</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,175">
          <id>courier</id>
          <label>Courier</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,176">
          <id>trackingNo</id>
          <label>Tracking No.</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,177">
          <id>vendorComments</id>
          <label>Vendor Comments</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <single/>
          <data/>
          <readonly/>
          <popupFormat/>
        </element>
      </elements>
    </Section>
    <SysCustGroup id="sysCustFields" label="Additional Information" position="sampleEvaluation_form.xlsx,tabHeader,181"/>
    <Grid enableDropUpload="Y" entityName="SampleEvaluationShareFile" id="sampleEvaluationShareFile" label="Files" position="sampleEvaluation_form.xlsx,tabHeader,187" ratio="1" rowRenderer="com.core.cbx.sharefile.form.SEShareFileRowRenderer" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabHeader,194">
          <id>addShareFile</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=SampleEvaluationShareFile&amp;upload=true</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,195">
          <id>selectShareFile</id>
          <label>Link Existing File...</label>
          <action>ShareFileOpenPopupWinAction</action>
          <actionParams>winId=popupSESelectShareFile&amp;replaceBtnAction=ok:SESelectShareFileOkAction&amp;keyProductCategory=productCategory&amp;messageLabelKey=lbl.popup.prepopupValidate.productCategory&amp;validateKey=sampleTracker.productCategory</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,196">
          <id>delShareFile</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabHeader,200">
          <id>shareFileId</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <size/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom>ShareFile</dataFrom>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,201">
          <id>shareFileType</id>
          <label>Type</label>
          <type>Selection</type>
          <data/>
          <size>M</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.fileTypeId</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass>com.core.cbx.sharefile.form.SEShareFileTypeCellRenderer</rendererClass>
          <viewName>popCodelistView</viewName>
          <viewParams>name=FILE_TYPE</viewParams>
          <winTitle>Attachment Type Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,202">
          <id>shareFileDescription</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <size>L</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.description</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass>com.core.cbx.sharefile.form.SEShareFileDescriptionCellRenderer</rendererClass>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,203">
          <id>file</id>
          <label>File</label>
          <type>Image</type>
          <data/>
          <size>XS</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass>com.core.cbx.sharefile.form.SEShareFileFileCellRenderer</rendererClass>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,204">
          <id>shareFileQcFile</id>
          <label>QC File</label>
          <type>CheckBox</type>
          <data/>
          <size>S</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.qcFile</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass>com.core.cbx.sharefile.form.SEShareFileQcFileCellRenderer</rendererClass>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,205">
          <id>shareFileNo</id>
          <label>File No.</label>
          <type>Hyperlink</type>
          <data/>
          <size>L</size>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=shareFile&amp;fieldId=shareFileId&amp;gridId=sampleEvaluationShareFile</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.fileNo</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,206">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <data/>
          <size>M</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.updateUserName</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
          <labelRenderer/>
          <rendererClass/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,207">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <data/>
          <size>S</size>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>shareFileId.updatedOn</mapping>
          <scale/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly>TRUE</readonly>
          <labelRenderer/>
          <rendererClass/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="SampleEvaluation" frozenColumns="2" id="relatedEvaluations" label="Related Evaluations" maxHeight="500px" pageSize="50" position="sampleEvaluation_form.xlsx,tabHeader,210" ratio="100%" rowRenderer="" selectionMode="Multiple" showHint="">
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabHeader,217">
          <id>itemNo</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <data/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;fieldId=item&amp;gridId=relatedEvaluations</actionParams>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <mapping/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,218">
          <id>itemDescription</id>
          <label>Item Description</label>
          <type>Text</type>
          <data/>
          <size>L</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <mapping/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,219">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <data/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <mapping/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,220">
          <id>sampleType</id>
          <label>Sample / Doc Type</label>
          <type>Dropdown</type>
          <data/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format>{name}</format>
          <labelRenderer/>
          <mapping>sampleType</mapping>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat>{name}</readonlyFormat>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,221">
          <id>sampleVersion</id>
          <label>Sample / Doc Ver.</label>
          <type>Label</type>
          <data/>
          <size>XS</size>
          <sorting>DESC</sorting>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <mapping/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex>3</sortingIndex>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,222">
          <id>colorAndPattern</id>
          <label>Color / Pattern</label>
          <type>Selection</type>
          <data/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format>{shortName}</format>
          <labelRenderer/>
          <mapping/>
          <viewName>popupItemColorView</viewName>
          <viewParams>itemRefNo={item.refNo}&amp;amp;itemVersion={item.version}</viewParams>
          <winTitle>Item Color Lookup</winTitle>
          <single/>
          <readonlyFormat>{shortName}</readonlyFormat>
          <popupFormat>{shortName}</popupFormat>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch>TRUE</disableAutoSearch>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabHeader,223">
          <id>sampleId</id>
          <label>Sample / Doc ID</label>
          <type>Hyperlink</type>
          <data/>
          <size>L</size>
          <sorting>TRUE</sorting>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleEvaluation&amp;fieldId=id&amp;gridId=relatedEvaluations</actionParams>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <mapping/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <single/>
          <readonlyFormat/>
          <popupFormat/>
          <readonly>TRUE</readonly>
          <rendererClass/>
          <comboKey/>
          <sortingIndex/>
          <disableAutoSearch/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabEvaluation" position="sampleEvaluation_form.xlsx,tabEvaluation">
    <Tab id="tabEvaluation" label="Evaluation" position="sampleEvaluation_form.xlsx,tabEvaluation,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,8">
          <id>evaluationSummarySection</id>
          <label>Evaluation Summary</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,9">
          <id>evaAdditionalInfoSection</id>
          <label>Evaluations Additional Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,10">
          <id>specAdditionalInformation</id>
          <label>Spec. Additional Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,11">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,12">
          <id>evaluationDtls</id>
          <label>Evaluation Details</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="evaluationSummarySection" label="Evaluation Summary" position="sampleEvaluation_form.xlsx,tabEvaluation,15" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,25">
          <id>evaluateBy</id>
          <label>Evaluated by</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format>{userName}</format>
          <readonlyFormat/>
          <readonly/>
          <viewName>lookupUserView</viewName>
          <winTitle>User Lookup</winTitle>
          <viewParams/>
          <popupFormat>{userName}</popupFormat>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,26">
          <id>evaluateDate</id>
          <label>Evaluation Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,27">
          <id>evaluateResult</id>
          <label>Evaluation Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <size>M</size>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,28">
          <id>overallExpiryDate</id>
          <label>Overall Expiry Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,29">
          <id>comments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
      </elements>
    </Section>
    <Section id="evaAdditionalInfoSection" label="Evaluations Additional Information" position="sampleEvaluation_form.xlsx,tabEvaluation,32" ratio="100%"/>
    <Grid entityName="EvaluationDtl" frozenColumns="2" hintLabel="This QA record contains the following evaluation requirement(s)" id="evaluationDtls" label="Evaluation Details" position="sampleEvaluation_form.xlsx,tabEvaluation,39" ratio="1" selectionMode="Multiple" showHint="TRUE">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,46">
          <id>addEvaluationDtl</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams>entityName=EvaluationDtl</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,47">
          <id>selectFromTemplate</id>
          <label>Select from Template...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupSelSampleEvlTmplWin&amp;replaceBtnAction=ok:PopupSelSampleEvlTmplOkAction;</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,48">
          <id>copyEvaluationDtl</id>
          <label>Copy</label>
          <action>CopyEvaluationDtlAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,49">
          <id>delEvaluationDtl</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,53">
          <id>seq</id>
          <label>Seq.</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting>ASC</sorting>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,54">
          <id>section</id>
          <label>Section</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,55">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,56">
          <id>evaluation</id>
          <label>Evaluation</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,57">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,58">
          <id>comments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,59">
          <id>correctionAction</id>
          <label>Correction Action</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,60">
          <id>expiryDate</id>
          <label>Expiry Date</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,61">
          <id>attachment1</id>
          <label>Files</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,62">
          <id>attachment2</id>
          <label>Attachment 2</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,63">
          <id>attachment3</id>
          <label>Attachment 3</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,64">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,65">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Date</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
          <sorting/>
        </element>
      </elements>
    </Grid>
    <Section id="specAdditionalInformation" label="Spec. Additional Information" position="sampleEvaluation_form.xlsx,tabEvaluation,68" ratio="100%">
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabEvaluation,75">
          <id>additionalDesc</id>
          <label>Additional Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
        </element>
      </elements>
    </Section>
  </sheet>
  <sheet id="tabFitAssessments" position="sampleEvaluation_form.xlsx,tabFitAssessments">
    <Tab id="tabFitAssessments" label="Fit Assessments" position="sampleEvaluation_form.xlsx,tabFitAssessments,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,8">
          <id>currentSampleSection</id>
          <label>Current Sample</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,9">
          <id>previousSampleSection</id>
          <label>Previous Sample</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,10">
          <id>imageSection</id>
          <label>Image</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,11">
          <id>fitAstAdditionalInfoSection</id>
          <label>Fit Assessments Additional Information</label>
          <type>Section</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,12">
          <id>fitAdditionalInfo</id>
          <label>Fit Additional Information</label>
          <type>Grid</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,13">
          <id>evalMeasurementFit</id>
          <label>Measurement Fit</label>
          <type>Grid</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,14">
          <id>evalAccessoriesMeasurementFit</id>
          <label>Accessories Measurement Fit</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="currentSampleSection" label="Current Sample" position="sampleEvaluation_form.xlsx,tabFitAssessments,17" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,27">
          <id>currentTrackerNo</id>
          <label>Tracker No.</label>
          <type>Hyperlink</type>
          <data/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleTracker&amp;fieldId=sampleTracker&amp;naviModule=quality</actionParams>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping>sampleTracker.trackerNo</mapping>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,28">
          <id>currentSampleId</id>
          <label>Sample ID</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping>sampleId</mapping>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,29">
          <id>currentSampleVersion</id>
          <label>Sample Version</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping>sampleVersion</mapping>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,30">
          <id>currentSampleType</id>
          <label>Sample Type</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping>sampleType</mapping>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,31">
          <id>itemSampleSize</id>
          <label>Item Sample Size</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,32">
          <id>currentFitSampleSize</id>
          <label>Fit Sample Size</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping>sizeCode</mapping>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,33">
          <id>currentFitQty</id>
          <label>Fit Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,34">
          <id>currentFitUOM</id>
          <label>Fit UOM</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <size>S</size>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,35">
          <id>currentFitUser</id>
          <label>Fit By</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format>{userName}</format>
          <readonlyFormat>{userName}</readonlyFormat>
          <readonly/>
          <viewName>lookupUserView</viewName>
          <winTitle>User Lookup</winTitle>
          <viewParams/>
          <popupFormat>{userName}</popupFormat>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,36">
          <id>currentFitDate</id>
          <label>Fit Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,37">
          <id>currentFitResult</id>
          <label>Fit Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <size>S</size>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,38">
          <id>currentComments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,39">
          <id>currentVendorQualityComment</id>
          <label>Vendor Quality Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <comboKey/>
        </element>
      </elements>
    </Section>
    <Section id="previousSampleSection" label="Previous Sample" position="sampleEvaluation_form.xlsx,tabFitAssessments,42" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,52">
          <id>previousSampleTracker</id>
          <label>Previous Tracker No.</label>
          <type>Selection</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format>{trackerNo}</format>
          <readonlyFormat>{trackerNo}</readonlyFormat>
          <readonly/>
          <viewName>lookupSampleEvaluationView</viewName>
          <winTitle>Sample Evaluation Lookup</winTitle>
          <viewParams>itemNo={itemNo}&amp;vendorCode={vendorCode}&amp;factCode={factCode}&amp;sampleId={sampleDetail.sampleId}&amp;sampleVer={sampleDetail.sampleVersion}</viewParams>
          <popupFormat>{trackerNo}</popupFormat>
          <mapping/>
          <single>TRUE</single>
          <extraParams>Selection_singleSelAction=com.core.cbx.sampleevaluation.action.SelectPreSampleTrackerAction</extraParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,53">
          <id>previousSampleId</id>
          <label>Previous Sample ID</label>
          <type>Hyperlink</type>
          <mapping/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sampleEvaluation&amp;fieldId=previousSampleEvaluation&amp;naviModule=quality</actionParams>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,54">
          <id>previousSampleVersion</id>
          <label>Previous Sample Version</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,55">
          <id>previousSampleTypeName</id>
          <label>Previous Sample Type</label>
          <type>Label</type>
          <mapping>previousSampleType.name</mapping>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,56">
          <id>previousItemSampleSize</id>
          <label>Previous Item Sample Size</label>
          <type>Label</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,57">
          <id>previousFitSampleSize</id>
          <label>Previous Fit Sample Size</label>
          <type>Label</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,58">
          <id>previousFitQty</id>
          <label>Previous Fit Quantity</label>
          <type>Number</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,59">
          <id>previousFitUOMName</id>
          <label>Previous Fit UOM</label>
          <type>Label</type>
          <mapping>previousFitUOM.name</mapping>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,60">
          <id>previousFitUser</id>
          <label>Previous Fit By</label>
          <type>Selection</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format>{userName}</format>
          <readonlyFormat>{userName}</readonlyFormat>
          <readonly>TRUE</readonly>
          <viewName>lookupUserView</viewName>
          <winTitle>User Lookup</winTitle>
          <viewParams/>
          <popupFormat>{userName}</popupFormat>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,61">
          <id>previousFitDate</id>
          <label>Previous Fit Date</label>
          <type>Date</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,62">
          <id>previousFitResultName</id>
          <label>Previous Fit Result</label>
          <type>Label</type>
          <mapping>previousFitResult.name</mapping>
          <action/>
          <actionParams/>
          <size>S</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,63">
          <id>previousComments</id>
          <label>Previous Comments</label>
          <type>TextArea</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,64">
          <id>preVendorQualityComment</id>
          <label>Previous Vendor Quality Comments</label>
          <type>TextArea</type>
          <mapping/>
          <action/>
          <actionParams/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly>TRUE</readonly>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <mapping/>
          <single/>
          <extraParams/>
        </element>
      </elements>
    </Section>
    <Section id="fitAstAdditionalInfoSection" label="Fit Assessments Additional Information" position="sampleEvaluation_form.xlsx,tabFitAssessments,67" ratio="100%"/>
    <Section id="imageSection" label="Image" position="sampleEvaluation_form.xlsx,tabFitAssessments,73" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,83">
          <id>image</id>
          <label>Image</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>item.pomImage</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <extraParams/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <Grid entityName="FitAdditionalInfo" id="fitAdditionalInfo" label="Fit Additional Information" position="sampleEvaluation_form.xlsx,tabFitAssessments,86" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,93">
          <id>addFitAdditionalInfo</id>
          <label>Add</label>
          <action>SampleEvaluationAddItemAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,94">
          <id>copyFitAdditionalInfo</id>
          <label>Copy</label>
          <action>CopyFitAdditionalInfoAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,95">
          <id>delFitAdditionalInfo</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,99">
          <id>seq</id>
          <label>Seq.</label>
          <type>Number</type>
          <mapping/>
          <size>XS</size>
          <readonly/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,100">
          <id>subItemNo</id>
          <label>Sub-Item No.</label>
          <type>Text</type>
          <mapping>subItemEntity.itemNo</mapping>
          <size>M</size>
          <readonly>TRUE</readonly>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,101">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <mapping/>
          <size>L</size>
          <readonly/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="EvalMeasurementFit" frozenColumns="2" hintLabel="This sample contains the following point of measure" id="evalMeasurementFit" label="Measurement Fits" maxHeight="500px" pageSize="50" position="sampleEvaluation_form.xlsx,tabFitAssessments,104" ratio="1" selectionMode="Multiple" showHint="TRUE">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,111">
          <id>mfRefreshFromItem</id>
          <label>Refresh from Item</label>
          <action>RefreshFromItemAction</action>
          <actionParams>specCollectionId=specMeasurement&amp;evalCollectionId=evalMeasurementFit&amp;childEntityId=specMeasurement&amp;entityName=EvalMeasurementFit</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,112">
          <id>mfUpdateToItem</id>
          <label>Update to Item</label>
          <action>UpdateToItemAction</action>
          <actionParams>specCollectionId=specGradingRule&amp;evalCollectionId=evalMeasurementFit&amp;reviseMeasFieldId=curRevisedMeasurement&amp;measFieldId=revisedMeasurement&amp;isNeedMatchSampleSize=true</actionParams>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,116">
          <id>seq</id>
          <label>Seq.</label>
          <type>Number</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>XS</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,117">
          <id>subItemNo</id>
          <label>Sub-Item No.</label>
          <type>Text</type>
          <mapping>subItemEntity.itemNo</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>M</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,118">
          <id>imageId</id>
          <label>Image</label>
          <type>Image</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>M</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,119">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,120">
          <id>position</id>
          <label>Position</label>
          <type>Label</type>
          <mapping>position.name</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,121">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,122">
          <id>remarks</id>
          <label>Remarks</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,123">
          <id>tolerancePositive</id>
          <label>Tol (+)</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,124">
          <id>toleranceNegative</id>
          <label>Tol (-)</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,125">
          <id>sampleMeasurement</id>
          <label>Sample Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,126">
          <id>revisedMeasurement</id>
          <label>Revised Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,127">
          <id>vendorMeasurement</id>
          <label>Current Vendor Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.EvalMeasurementCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,128">
          <id>qaMeasurement</id>
          <label>Current QA Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.EvalMeasurementCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,129">
          <id>vendorVariance</id>
          <label>Current Vendor Variance</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,130">
          <id>qaVariance</id>
          <label>Current QA Variance</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,131">
          <id>curRevisedMeasurement</id>
          <label>Current Revised Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,132">
          <id>qaResult</id>
          <label>Current QA Result</label>
          <type>Dropdown</type>
          <mapping/>
          <action>GetCodelistDataSourceAction</action>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,133">
          <id>qaComments</id>
          <label>Current QA Comments</label>
          <type>TextArea</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,134">
          <id>preVendorMeasurement</id>
          <label>Previous Vendor Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.vendorMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,135">
          <id>preQaMeasurement</id>
          <label>Previous QA Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.qaMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,136">
          <id>preVendorVariance</id>
          <label>Previous Vendor Variance</label>
          <type>Decimal</type>
          <mapping>preEntity.vendorVariance</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,137">
          <id>preQaVariance</id>
          <label>Previous QA Variance</label>
          <type>Decimal</type>
          <mapping>preEntity.qaVariance</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,138">
          <id>preRevisedMeasurement</id>
          <label>Previous Revised Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.curRevisedMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,139">
          <id>preQaResult</id>
          <label>Previous QA Result</label>
          <type>Label</type>
          <mapping>preEntity.qaResultName</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,140">
          <id>preQaComments</id>
          <label>Previous QA Comments</label>
          <type>TextArea</type>
          <mapping>preEntity.qaComments</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,141">
          <id>refKey</id>
          <label>Ref. Key</label>
          <type>Label</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="EvalAccessoriesMeasurementFit" frozenColumns="2" hintLabel="This sample contains the following point of measure" id="evalAccessoriesMeasurementFit" label="Accessories Measurement Fits" maxHeight="500px" pageSize="50" position="sampleEvaluation_form.xlsx,tabFitAssessments,144" ratio="1" selectionMode="Multiple" showHint="TRUE">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,151">
          <id>amfRefreshFromItem</id>
          <label>Refresh from Item</label>
          <action>RefreshFromItemAction</action>
          <actionParams>specCollectionId=specAccessoriesMeasurement&amp;evalCollectionId=evalAccessoriesMeasurementFit&amp;childEntityId=specAccessoriesMeasurement&amp;entityName=EvalAccessoriesMeasurementFit</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,152">
          <id>amfUpdateToItem</id>
          <label>Update to Item</label>
          <action>UpdateToItemAction</action>
          <actionParams>specCollectionId=specAccessoriesMeasurement&amp;evalCollectionId=evalAccessoriesMeasurementFit&amp;reviseMeasFieldId=curRevisedMeasurement&amp;measFieldId=revisedMeasurement&amp;isNeedMatchSampleSize=true</actionParams>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,156">
          <id>seq</id>
          <label>Seq.</label>
          <type>Number</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>XS</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,157">
          <id>subItemNo</id>
          <label>Sub-Item No.</label>
          <type>Text</type>
          <mapping>subItemEntity.itemNo</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>M</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,158">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,159">
          <id>position</id>
          <label>Position</label>
          <type>Label</type>
          <mapping>position.name</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,160">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,161">
          <id>remarks</id>
          <label>Remarks</label>
          <type>Text</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,162">
          <id>tolerancePositive</id>
          <label>Tol (+)</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,163">
          <id>toleranceNegative</id>
          <label>Tol (-)</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,164">
          <id>sampleMeasurement</id>
          <label>Sample Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,165">
          <id>revisedMeasurement</id>
          <label>Revised Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,166">
          <id>vendorMeasurement</id>
          <label>Current Vendor Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.EvalMeasurementCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,167">
          <id>qaMeasurement</id>
          <label>Current QA Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.EvalMeasurementCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,168">
          <id>vendorVariance</id>
          <label>Current Vendor Variance</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,169">
          <id>qaVariance</id>
          <label>Current QA Variance</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,170">
          <id>curRevisedMeasurement</id>
          <label>Current Revised Meas.</label>
          <type>Decimal</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.MeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,171">
          <id>qaResult</id>
          <label>Current QA Result</label>
          <type>Dropdown</type>
          <mapping/>
          <action>GetCodelistDataSourceAction</action>
          <readonly/>
          <size>S</size>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat>{name}</readonlyFormat>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,172">
          <id>qaComments</id>
          <label>Current QA Comments</label>
          <type>TextArea</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,173">
          <id>preVendorMeasurement</id>
          <label>Previous Vendor Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.vendorMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,174">
          <id>preQaMeasurement</id>
          <label>Previous QA Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.qaMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,175">
          <id>preVendorVariance</id>
          <label>Previous Vendor Variance</label>
          <type>Decimal</type>
          <mapping>preEntity.vendorVariance</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,176">
          <id>preQaVariance</id>
          <label>Previous QA Variance</label>
          <type>Decimal</type>
          <mapping>preEntity.qaVariance</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,177">
          <id>preRevisedMeasurement</id>
          <label>Previous Revised Meas.</label>
          <type>Decimal</type>
          <mapping>preEntity.curRevisedMeasurement</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass>com.core.cbx.sampleevaluation.form.PreMeasurementDecimalFormatCellRenderer</rendererClass>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,178">
          <id>preQaResult</id>
          <label>Previous QA Result</label>
          <type>Label</type>
          <mapping>preEntity.qaResultName</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>S</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,179">
          <id>preQaComments</id>
          <label>Previous QA Comments</label>
          <type>TextArea</type>
          <mapping>preEntity.qaComments</mapping>
          <action/>
          <readonly>TRUE</readonly>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabFitAssessments,180">
          <id>refKey</id>
          <label>Ref. Key</label>
          <type>Label</type>
          <mapping/>
          <action/>
          <readonly/>
          <size>L</size>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <extraParams/>
          <rendererClass/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabImage" position="sampleEvaluation_form.xlsx,tabImage">
    <Tab id="tabImage" label="Images &amp; Attachments" position="sampleEvaluation_form.xlsx,tabImage,1" ratio="1">
      <elements id="default">
        <element position="sampleEvaluation_form.xlsx,tabImage,8">
          <id>sampleEvaluationImages</id>
          <label>Images</label>
          <type>Grid</type>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,9">
          <id>sampleEvaluationAttachs</id>
          <label>Attachments</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid enableDropUpload="Y" entityName="SampleEvaluationImage" id="sampleEvaluationImages" label="Images" position="sampleEvaluation_form.xlsx,tabImage,12" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabImage,19">
          <id>addImage</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=SampleEvaluationImage&amp;upload=true</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,20">
          <id>copyImage</id>
          <label>Copy</label>
          <action>CopySampleEvaluationImageAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,21">
          <id>delImage</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabImage,25">
          <id>imageTypeId</id>
          <label>Type</label>
          <type>Selection</type>
          <winTitle>Image Type Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popCodelistView</viewName>
          <labelRenderer/>
          <viewParams>name=IMAGE_TYPE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
          <readonlyFormat>{name}</readonlyFormat>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,26">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,27">
          <id>image</id>
          <label>File</label>
          <type>Image</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams>Image_tooltiptext=image.original.fileName</extraParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,28">
          <id>qcFile</id>
          <label>QC File</label>
          <type>CheckBox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,29">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,30">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <readonlyFormat/>
          <extraParams/>
        </element>
      </elements>
    </Grid>
    <Grid enableDropUpload="Y" entityName="SampleEvaluationAttach" id="sampleEvaluationAttachs" label="Attachments" position="sampleEvaluation_form.xlsx,tabImage,33" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="sampleEvaluation_form.xlsx,tabImage,40">
          <id>addAttachment</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=SampleEvaluationAttach&amp;upload=true</actionParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,41">
          <id>addURL</id>
          <label>Add URL</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,42">
          <id>copyAttachment</id>
          <label>Copy</label>
          <action>CopySampleEvaluationAttachmentAction</action>
          <actionParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,43">
          <id>delAttachment</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="sampleEvaluation_form.xlsx,tabImage,47">
          <id>attachTypeId</id>
          <label>Type</label>
          <type>Selection</type>
          <winTitle>Attachment Type Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popCodelistView</viewName>
          <labelRenderer/>
          <viewParams>name=ATTACHMENT_TYPE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
          <readonlyFormat>{name}</readonlyFormat>
          <rendererClass/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,48">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <rendererClass/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,49">
          <id>attachment</id>
          <label>File</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <rendererClass>com.core.cbx.ui.zk.cul.grid.renderer.FileLinkCellRenderer</rendererClass>
          <extraParams>fileAddress</extraParams>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,50">
          <id>qcFile</id>
          <label>QC File</label>
          <type>CheckBox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <rendererClass/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,51">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <readonlyFormat/>
          <rendererClass/>
          <extraParams/>
        </element>
        <element position="sampleEvaluation_form.xlsx,tabImage,52">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <readonlyFormat/>
          <rendererClass/>
          <extraParams/>
        </element>
      </elements>
    </Grid>
  </sheet>
</form>
