<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="patternPalette" position="patternPalette_view.xlsx">
  <sheet id="patternPaletteView" position="patternPalette_view.xlsx,patternPaletteView">
    <ViewDefinition advancedSearchId="" description="Pattern Palette View" id="patternPaletteView" label="Pattern Palettes - All" moduleId="patternPalette" position="patternPalette_view.xlsx,patternPaletteView,1" queryId="listPatternPalettes" searchCriterion="">
      <elements id="options">
        <element position="patternPalette_view.xlsx,patternPaletteView,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(Any)</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,16">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,17">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>PatternPalette</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,18">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>thumbnail:image.original.filePath:string,thumbnailUrl:image.original.thumbnail:string,thumbnailMockUpId:image.thumbnailMockUp:string,thumbnailFileId:image.thumbnailFileId:string,id:id:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="patternPalette_view.xlsx,patternPaletteView,22">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,23">
          <id>searchNewDoc</id>
          <label>New Pattern Palette</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,24">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,25">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,26">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,27">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,28">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,29">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,30">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,31">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,32">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="patternPalette_view.xlsx,patternPaletteView,37">
          <id>paletteCode</id>
          <label>Palette Code</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteCode:paletteCode:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,38">
          <id>paletteName</id>
          <label>Pattern Palette Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteName:paletteName:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,39">
          <id>paletteType</id>
          <label>Palette Type</label>
          <type>CodeList</type>
          <format>bookName=PATTERN_PALETTE_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteType:paletteType.code:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,40">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format>bookName=</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,41">
          <id>PatternPalette</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CPP</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,42">
          <id>PatternPalette</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,43">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,44">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,45">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,46">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>CPP.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,47">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,48">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,49">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,50">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,51">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,52">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,53">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,54">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,55">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteView,56">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="patternPaletteActiveView " position="patternPalette_view.xlsx,patternPaletteActiveView ">
    <ViewDefinition advancedSearchId="" description="Pattern Palette View" id="patternPaletteActiveView" label="Pattern Palettes - Active" moduleId="patternPalette" position="patternPalette_view.xlsx,patternPaletteActiveView ,1" queryId="listPatternPalettes" searchCriterion="">
      <elements id="options">
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,16">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,17">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,18">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>PatternPalette</value>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,19">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>thumbnail:image.original.filePath:string,thumbnailUrl:image.original.thumbnail:string,thumbnailMockUpId:image.thumbnailMockUp:string,thumbnailFileId:image.thumbnailFileId:string,id:id:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,23">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,24">
          <id>searchNewDoc</id>
          <label>New Pattern Palette</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,25">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,26">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,27">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,28">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,29">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,30">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=patternPalette&amp;entityName=PatternPalette</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,31">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,36">
          <id>paletteCode</id>
          <label>Palette Code</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteCode:paletteCode:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,37">
          <id>paletteName</id>
          <label>Pattern Palette Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteName:paletteName:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,38">
          <id>paletteType</id>
          <label>Palette Type</label>
          <type>CodeList</type>
          <format>bookName=PATTERN_PALETTE_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>paletteType:paletteType.code:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,39">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format>bookName=</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,40">
          <id>PatternPalette</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CPP</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,41">
          <id>PatternPalette</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,42">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,43">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,44">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,45">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPP.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,46">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,47">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,48">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,49">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPP.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,50">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPP.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,51">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,52">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPP.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,53">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,54">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPP.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="patternPalette_view.xlsx,patternPaletteActiveView ,55">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>CPP.PALETTE_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
