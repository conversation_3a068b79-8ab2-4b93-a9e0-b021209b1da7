<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="cpmTaskTempl" position="cpmTaskTempl_dataMappingRule.xlsx">
  <sheet id="cpmTaskTemplCopyDoc" position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc">
    <DataMappingRule description="Mapping from CpmTaskTempl to CpmTaskTempl" domain="/" dstEntityName="CpmTaskTempl" dstEntityVersion="1" effectiveDate="2011-12-12" id="cpmTaskTemplCopyDoc" position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,1" srcEntityName="CpmTaskTempl" srcEntityVersion="1" status="1" updatedDate="2012-12-13">
      <elements id="mappingRule">
        <element position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cpmTaskTempl_dataMappingRule.xlsx,cpmTaskTemplCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
