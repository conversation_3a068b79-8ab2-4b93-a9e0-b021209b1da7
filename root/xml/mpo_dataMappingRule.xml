<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="mpo" position="mpo_dataMappingRule.xlsx">
  <sheet id="mpoContactSelect" position="mpo_dataMappingRule.xlsx,mpoContactSelect">
    <DataMappingRule description="MPO Select Contact Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoContactSelect" position="mpo_dataMappingRule.xlsx,mpoContactSelect,1" srcEntityName="VendorContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoContacts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>mpoContacts.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>mpoContacts.contactTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectItemTask1" position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1">
    <DataMappingRule description="Mapping for MPO Select Item" domain="/" dstEntityName="MpoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectItemTask1" position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,12">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,14">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,15">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,16">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,17">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,18">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemBrand</srcFieldId>
          <dstFieldId>brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,20">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,21">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,22">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,23">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,24">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSize</srcFieldId>
          <dstFieldId>mpoItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,25">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemSize</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,26">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,27">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,28">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,29">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,30">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>mpoItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,31">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,32">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,33">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,34">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,35">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask1,39">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoSelectItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectItemTask2" position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2">
    <DataMappingRule description="Mapping for MPO Select Item" domain="/" dstEntityName="MpoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectItemTask2" position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,1" srcEntityName="ItemCust" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>custItemNo</srcFieldId>
          <dstFieldId>custItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,11">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectItemTask2,12">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoAddressSelect" position="mpo_dataMappingRule.xlsx,mpoAddressSelect">
    <DataMappingRule description="VPO Select Address Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoAddressSelect" position="mpo_dataMappingRule.xlsx,mpoAddressSelect,1" srcEntityName="VendorAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoAddresses</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>mpoAddresses.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>mpoAddresses.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>mpoAddresses.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelect,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>mpoAddresses.addressTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoItemCopy" position="mpo_dataMappingRule.xlsx,mpoItemCopy">
    <DataMappingRule description="Mapping from VPO Item Copy" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoItemCopy" position="mpo_dataMappingRule.xlsx,mpoItemCopy,1" srcEntityName="MpoItem" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoItemCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoItemCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoItemCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,mpoItemCopy,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoItemCopyPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoCopy" position="mpo_dataMappingRule.xlsx,mpoCopy">
    <DataMappingRule description="Mapping for MPO Copy" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoCopy" position="mpo_dataMappingRule.xlsx,mpoCopy,1" srcEntityName="Mpo" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>mpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoChargeCopy" position="mpo_dataMappingRule.xlsx,mpoChargeCopy">
    <DataMappingRule description="VPO Charge Copy Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoChargeCopy" position="mpo_dataMappingRule.xlsx,mpoChargeCopy,1" srcEntityName="MpoCharge" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoChargeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoChargeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoCharges</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoChargeCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>identity</srcFieldId>
          <dstFieldId>identity</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoChargeCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>flag</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>new</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoContactCopy" position="mpo_dataMappingRule.xlsx,mpoContactCopy">
    <DataMappingRule description="VPO Contact Copy Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoContactCopy" position="mpo_dataMappingRule.xlsx,mpoContactCopy,1" srcEntityName="MpoContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoContactCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoContacts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoAddressCopy" position="mpo_dataMappingRule.xlsx,mpoAddressCopy">
    <DataMappingRule description="VPO Address Copy Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoAddressCopy" position="mpo_dataMappingRule.xlsx,mpoAddressCopy,1" srcEntityName="MpoAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoAddresses</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoAttachmentCopy" position="mpo_dataMappingRule.xlsx,mpoAttachmentCopy">
    <DataMappingRule description="VPO Attachment Copy Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoAttachmentCopy" position="mpo_dataMappingRule.xlsx,mpoAttachmentCopy,1" srcEntityName="MpoAttachment" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoAttachmentCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAttachmentCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoChargeOnDocCopy" position="mpo_dataMappingRule.xlsx,mpoChargeOnDocCopy">
    <DataMappingRule description="VPO Charge on Doc Copy Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-01-01" id="mpoChargeOnDocCopy" position="mpo_dataMappingRule.xlsx,mpoChargeOnDocCopy,1" srcEntityName="MpoChargeOnDoc" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoChargeOnDocCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoChargeOnDocCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoChargeOnDocs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectProjectItem" position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem">
    <DataMappingRule description="Mapping for Project Item to Mpo" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectProjectItem" position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,1" srcEntityName="ProjectItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue>entity.quotation.totalCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>mpoItems.quotation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>brand</srcFieldId>
          <dstFieldId>mpoItems.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>mpoItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>mpoItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultUom</srcFieldId>
          <dstFieldId>mpoItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>mpoItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>mpoItems.mpoItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,27">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,29">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,30">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems.mpoItemSizes.itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>mpoItems.mpoItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,33">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,34">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,35">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,36">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,37">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems.mpoItemColors.itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,41">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoSelectProjectItemPreProcessor</implementationClass>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectProjectItem,42">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoSelectProjectPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoGenVpo" position="mpo_dataMappingRule.xlsx,mpoGenVpo">
    <DataMappingRule description="Mapping for Mpo to Vpo" domain="/" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoGenVpo" position="mpo_dataMappingRule.xlsx,mpoGenVpo,1" srcEntityName="Mpo" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,10">
          <mappingType>Field</mappingType>
          <srcFieldId>projectRef</srcFieldId>
          <dstFieldId>projRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>otherTerms</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>custContact</srcFieldId>
          <dstFieldId>custContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>orderType</srcFieldId>
          <dstFieldId>orderType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>customer</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,19">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>headerFactory</srcFieldId>
          <dstFieldId>headerFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>payMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,23">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,24">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,25">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,26">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,27">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>vpoItem.mpoItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>specVersion</srcFieldId>
          <dstFieldId>specVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>custItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,38">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.quotation.factoryId</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,39">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.quotation.portOfLoading</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,40">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerInner</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,41">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerOuter</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,42">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.htsNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,43">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoItem.moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.moq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,44">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.cso</srcFieldId>
          <dstFieldId>vpoItem.csoId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,45">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.season</srcFieldId>
          <dstFieldId>vpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,46">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.year</srcFieldId>
          <dstFieldId>vpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,47">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.quotation</srcFieldId>
          <dstFieldId>vpoItem.quoteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,48">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.hierarchy</srcFieldId>
          <dstFieldId>vpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,49">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.market</srcFieldId>
          <dstFieldId>vpoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,50">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.channel</srcFieldId>
          <dstFieldId>vpoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,51">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.countryOfOrigin</srcFieldId>
          <dstFieldId>vpoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,52">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.item</srcFieldId>
          <dstFieldId>vpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,53">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.item.fileId</srcFieldId>
          <dstFieldId>vpoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,54">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.brand</srcFieldId>
          <dstFieldId>vpoItem.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,55">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.uom</srcFieldId>
          <dstFieldId>vpoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,56">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.productCategory</srcFieldId>
          <dstFieldId>vpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,57">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.mpoItemColors</srcFieldId>
          <dstFieldId>vpoItem.vpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,58">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.item.classification</srcFieldId>
          <dstFieldId>vpoItem.classification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,59">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,60">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,61">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,62">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,63">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,64">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemColor.id</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,65">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.mpoItemColors.itemColor</srcFieldId>
          <dstFieldId>vpoItem.vpoItemColor.itemColorDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,66">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.lotNo</srcFieldId>
          <dstFieldId>vpoItem.lotNo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,mpoGenVpo,70">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoGenVpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectVendor" position="mpo_dataMappingRule.xlsx,mpoSelectVendor">
    <DataMappingRule description="Mapping from Vendor" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2013-09-04" id="mpoSelectVendor" position="mpo_dataMappingRule.xlsx,mpoSelectVendor,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2013-09-04">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDetails</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDefault=1</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDetails.currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectVqItemTask1" position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1">
    <DataMappingRule description="Mapping for Quotation to Mpo" domain="/" dstEntityName="MpoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectVqItemTask1" position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2018-01-24">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,14">
          <mappingType>Field</mappingType>
          <srcFieldId>totalCost</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,15">
          <mappingType>Field</mappingType>
          <srcFieldId>truckNumber</srcFieldId>
          <dstFieldId>countOfTruck</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,16">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCbm</srcFieldId>
          <dstFieldId>cbm</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,17">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,18">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,19">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,20">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,21">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,22">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemBrand</srcFieldId>
          <dstFieldId>brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,23">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,24">
          <mappingType>Section</mappingType>
          <srcFieldId>containerType</srcFieldId>
          <dstFieldId>containerType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,25">
          <mappingType>Section</mappingType>
          <srcFieldId>truckType</srcFieldId>
          <dstFieldId>truckType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,26">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,27">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,28">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,29">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,30">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>quotation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,31">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>mpoItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>item</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,32">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemSize</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,33">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,34">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,35">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,37">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>mpoItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>item</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,38">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,39">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,40">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,41">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask1,42">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectVqItemTask2" position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask2">
    <DataMappingRule description="Mapping for Item Customer to Mpo" domain="/" dstEntityName="MpoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectVqItemTask2" position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask2,1" srcEntityName="ItemCust" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>custItemNo</srcFieldId>
          <dstFieldId>custItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectVqItemTask2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectCustomer" position="mpo_dataMappingRule.xlsx,mpoSelectCustomer">
    <DataMappingRule description="Mapping from Customer" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2013-09-04" id="mpoSelectCustomer" position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2013-09-04">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,9">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>custContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>phoneNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,13">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,14">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,15">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>payMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCustomer,16">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoAddressSelectFromCustomer" position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer">
    <DataMappingRule description="Mpo Select Address From Customer Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2014-09-25" id="mpoAddressSelectFromCustomer" position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,1" srcEntityName="CustAddress" srcEntityVersion="1" status="1" updatedDate="2014-09-25">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoAddresses</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Customer</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>mpoAddresses.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>mpoAddresses.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>mpoAddresses.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoAddressSelectFromCustomer,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>mpoAddresses.addressTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoContactSelectFromCustomer" position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer">
    <DataMappingRule description="MPO Select Contact From Customer Mapping" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2014-09-25" id="mpoContactSelectFromCustomer" position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,1" srcEntityName="CustContact" srcEntityVersion="1" status="1" updatedDate="2014-09-25">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoContacts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,17">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Customer</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>mpoContacts.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoContactSelectFromCustomer,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>mpoContacts.contactTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoTermsAndConditionsCopy" position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy">
    <DataMappingRule description="Mapping for Copy MpoTermsAndConditions" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2020-07-23" id="mpoTermsAndConditionsCopy" position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,1" srcEntityName="MpoTermsAndConditions" srcEntityVersion="1" status="1" updatedDate="2020-07-23">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoTermsAndConditions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>tcName</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.tcName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>otherDesc</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.otherDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId>effectiveFrom</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.effectiveFrom</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId>effectiveTo</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.effectiveTo</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,16">
          <mappingType>Section</mappingType>
          <srcFieldId>tcType</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.tcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoTermsAndConditionsCopy,17">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>mpoTermsAndConditions.image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoNewMpo" position="mpo_dataMappingRule.xlsx,csoNewMpo">
    <DataMappingRule description="Mapping from cso to mpo" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2021-02-23" id="csoNewMpo" position="mpo_dataMappingRule.xlsx,csoNewMpo,1" srcEntityName="Cso" srcEntityVersion="1" status="1" updatedDate="2021-02-23">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId>projRef</srcFieldId>
          <dstFieldId>projectRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerContact</srcFieldId>
          <dstFieldId>custContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>phoneNo</srcFieldId>
          <dstFieldId>phoneNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,14">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,15">
          <mappingType>Field</mappingType>
          <srcFieldId>otherTerms</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,23">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>payMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,24">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,25">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,26">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.paymentMethod</srcFieldId>
          <dstFieldId>payMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,27">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultSourcingRecord.paymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultSourcingRecord.otherTerms</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,30">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,31">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,32">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>planedQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>sellPrice</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemNo</srcFieldId>
          <dstFieldId>custItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.mpoItems.item.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,38">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,39">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.mpoItems.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,40">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.item</srcFieldId>
          <dstFieldId>mpoItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,41">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.itemImage</srcFieldId>
          <dstFieldId>mpoItems.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,42">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.itemType</srcFieldId>
          <dstFieldId>mpoItems.itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,43">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,44">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,45">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.brand</srcFieldId>
          <dstFieldId>mpoItems.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,46">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.uom</srcFieldId>
          <dstFieldId>mpoItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,47">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.productCategory</srcFieldId>
          <dstFieldId>mpoItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,48">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.vq</srcFieldId>
          <dstFieldId>mpoItems.quotation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,49">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.market</srcFieldId>
          <dstFieldId>mpoItems.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,50">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.channel</srcFieldId>
          <dstFieldId>mpoItems.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,51">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.forwarder</srcFieldId>
          <dstFieldId>mpoItems.forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,52">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.destinationForwarder</srcFieldId>
          <dstFieldId>mpoItems.destinationForwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,53">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.countryOfShipment</srcFieldId>
          <dstFieldId>mpoItems.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,54">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem.hierarchy</srcFieldId>
          <dstFieldId>mpoItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,55">
          <mappingType>Section</mappingType>
          <srcFieldId>virtualCsoItemCs</srcFieldId>
          <dstFieldId>virtualMpoItemCs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,56">
          <mappingType>Field</mappingType>
          <srcFieldId>itemLotNo</srcFieldId>
          <dstFieldId>itemLotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,57">
          <mappingType>Field</mappingType>
          <srcFieldId>ean</srcFieldId>
          <dstFieldId>ean</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,58">
          <mappingType>Field</mappingType>
          <srcFieldId>upc</srcFieldId>
          <dstFieldId>upc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,59">
          <mappingType>Field</mappingType>
          <srcFieldId>skuNo</srcFieldId>
          <dstFieldId>skuNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,60">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>colorSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,61">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,62">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>sizeSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,63">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,64">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultPrice</srcFieldId>
          <dstFieldId>defaultPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,65">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,66">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>colorSizeQty</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,csoNewMpo,70">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoGenMpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="mpoSelectCsoItem" position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem">
    <DataMappingRule description="Mapping for Cso Item to Mpo" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="mpoSelectCsoItem" position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,1" srcEntityName="CsoItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>csoId</srcFieldId>
          <dstFieldId>cso</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Cso</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>sellPrice</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>mpoItems.quotation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>brand</srcFieldId>
          <dstFieldId>mpoItems.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>mpoItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>forwarder</srcFieldId>
          <dstFieldId>mpoItems.forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>destinationForwarder</srcFieldId>
          <dstFieldId>mpoItems.destinationForwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>mpoItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>mpoItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>mpoItems.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>mpoItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="mpo_dataMappingRule.xlsx,mpoSelectCsoItem,35">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.MpoSelectCsoItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
