<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="vpo" position="vpo_validation.xlsx">
  <sheet id="ValidationProfile" position="vpo_validation.xlsx,ValidationProfile">
    <ValidationProfile position="vpo_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="vpo_validation.xlsx,ValidationProfile,4">
          <id>9b48c460c12e4c3090b016cbb30bcd38</id>
          <profileName>Default Data Validation Profile Vpo[ver:1]</profileName>
          <inheritFrom/>
          <entityName>Vpo</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SaveAndConfirm,DraftStatus,OfficialStatus,CompletedStatus,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,Mark<PERSON><PERSON>ustomStatus05Doc,Mark<PERSON><PERSON>ustomStatus06Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus07D<PERSON>,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.467</updatedOn>
        </element>
        <element position="vpo_validation.xlsx,ValidationProfile,5">
          <id>9b48c460c12e4c3090b016cbb30baaaa</id>
          <profileName>Default Data Validation Profile Vpo[ver:1]</profileName>
          <inheritFrom/>
          <entityName>Vpo</entityName>
          <entityVer>1</entityVer>
          <action>ReleasedToVendorStatus,SendToVendor,VendorConfirmedStatus,VendorRejectedStatus</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.467</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="vpo_validation.xlsx,ValidationRule">
    <ValidationRule position="vpo_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="vpo_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <restapiBeanName>UniqueInSectionValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,7">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumericRangeValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,8">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <restapiBeanName>EmailValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,9">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,10">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,11">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <restapiBeanName>ExternalActiveValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,12">
          <type>VendorFactValidator</type>
          <className>com.core.cbx.validation.validator.VendorFactValidator</className>
          <restapiBeanName>VendorFactValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,13">
          <type>MatchOrderTypeValidator</type>
          <className>com.core.cbx.validation.validator.MatchOrderTypeValidator</className>
          <restapiBeanName>MatchOrderTypeValidator</restapiBeanName>
          <condition>isImportOrApiMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,14">
          <type>AttachLinkMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.AttachLinkMandatoryValidator</className>
          <restapiBeanName>AttachLinkMandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="vpo_validation.xlsx,ValidationRule,15">
          <type>AttachTypeValidator</type>
          <className>com.core.cbx.validation.validator.CurrentUserDeactivateValidator</className>
          <restapiBeanName>AttachTypeValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="vpo_validation.xlsx,MandatoryValidator">
    <ValidationField position="vpo_validation.xlsx,MandatoryValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,MandatoryValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>vpoDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vpoDate</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,9">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,10">
          <entityName>Vpo</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,11">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,12">
          <entityName>Vpo</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,13">
          <entityName>Vpo</entityName>
          <fieldId>paymentTerm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>paymentTerm</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,14">
          <entityName>Vpo</entityName>
          <fieldId>paymentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>paymentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,15">
          <entityName>Vpo</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,16">
          <entityName>Vpo</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,17">
          <entityName>VpoItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,18">
          <entityName>VpoItem</entityName>
          <fieldId>lotNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>lotNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,19">
          <entityName>VpoItem</entityName>
          <fieldId>itemDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,20">
          <entityName>VpoItem</entityName>
          <fieldId>uom</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>uom</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,21">
          <entityName>VpoItem</entityName>
          <fieldId>planedQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>planedQty</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,22">
          <entityName>VpoItem</entityName>
          <fieldId>sellPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>sellPrice</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,23">
          <entityName>VpoItem</entityName>
          <fieldId>dimensionUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>dimensionUOM</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,24">
          <entityName>VpoItem</entityName>
          <fieldId>weightUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>weightUOM</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,25">
          <entityName>VpoShip</entityName>
          <fieldId>shipmentNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID>shipmentNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,26">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoShipId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>shipmentNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,27">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoItemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,28">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,29">
          <entityName>VpoCharge</entityName>
          <fieldId>vpoItemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,30">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,31">
          <entityName>VpoCharge</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,32">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,33">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,34">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,35">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,36">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,37">
          <entityName>VpoContact</entityName>
          <fieldId>contactTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>contactTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,38">
          <entityName>VpoContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,39">
          <entityName>VpoContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,40">
          <entityName>VpoAddress</entityName>
          <fieldId>addressTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>addressTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,41">
          <entityName>VpoAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,42">
          <entityName>VpoAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,43">
          <entityName>VpoProductDtl</entityName>
          <fieldId>productionProcess</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>productionProcess</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,MandatoryValidator,46" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,MandatoryValidator,53">
          <entityName>Vpo</entityName>
          <fieldId>vpoDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vpoDate</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,54">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,55">
          <entityName>Vpo</entityName>
          <fieldId>currency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>currency</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,56">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,57">
          <entityName>Vpo</entityName>
          <fieldId>incoterm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>incoterm</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,58">
          <entityName>Vpo</entityName>
          <fieldId>paymentTerm</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>paymentTerm</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,59">
          <entityName>Vpo</entityName>
          <fieldId>paymentMethod</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>paymentMethod</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,60">
          <entityName>Vpo</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,61">
          <entityName>Vpo</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,62">
          <entityName>VpoItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,63">
          <entityName>VpoItem</entityName>
          <fieldId>lotNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>lotNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,64">
          <entityName>VpoItem</entityName>
          <fieldId>itemDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,65">
          <entityName>VpoItem</entityName>
          <fieldId>uom</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>uom</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,66">
          <entityName>VpoItem</entityName>
          <fieldId>planedQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>planedQty</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,67">
          <entityName>VpoItem</entityName>
          <fieldId>sellPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>sellPrice</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,68">
          <entityName>VpoItem</entityName>
          <fieldId>dimensionUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>dimensionUOM</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,69">
          <entityName>VpoItem</entityName>
          <fieldId>weightUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>weightUOM</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,70">
          <entityName>VpoShip</entityName>
          <fieldId>shipmentNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID>shipmentNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,71">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoShipId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>shipmentNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,72">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoItemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,73">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,74">
          <entityName>VpoCharge</entityName>
          <fieldId>vpoItemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,75">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,76">
          <entityName>VpoCharge</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,77">
          <entityName>VpoCharge</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,78">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,79">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,80">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,81">
          <entityName>VpoChargeOnDoc</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,82">
          <entityName>VpoContact</entityName>
          <fieldId>contactTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>contactTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,83">
          <entityName>VpoContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,84">
          <entityName>VpoContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,85">
          <entityName>VpoAddress</entityName>
          <fieldId>addressTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>addressTypeId</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,86">
          <entityName>VpoAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,87">
          <entityName>VpoAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,MandatoryValidator,88">
          <entityName>VpoProductDtl</entityName>
          <fieldId>productionProcess</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>productionProcess</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="vpo_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="vpo_validation.xlsx,UniqueInSectionValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>VpoShip</entityName>
          <fieldId>shipmentNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,9">
          <entityName>VpoItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010089</ERROR_ID>
          <FIELD_GROUP>itemId,lotNo</FIELD_GROUP>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,10">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoShipId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010095</ERROR_ID>
          <FIELD_GROUP>vpoShipId.shipmentNo,vpoItemId.itemNo,vpoItemId.lotNo</FIELD_GROUP>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,11">
          <entityName>VpoProductDtl</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF024</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,12">
          <entityName>VpoTermsAndConditions</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF024</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>vpoTermsAndConditions</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,UniqueInSectionValidator,15" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,22">
          <entityName>VpoShip</entityName>
          <fieldId>shipmentNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID/>
          <FIELD_GROUP/>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,23">
          <entityName>VpoItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010089</ERROR_ID>
          <FIELD_GROUP>itemId,lotNo</FIELD_GROUP>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,24">
          <entityName>VpoShipDtl</entityName>
          <fieldId>vpoShipId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010095</ERROR_ID>
          <FIELD_GROUP>vpoShipId.shipmentNo,vpoItemId.itemNo,vpoItemId.lotNo</FIELD_GROUP>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,25">
          <entityName>VpoProductDtl</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF024</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,UniqueInSectionValidator,26">
          <entityName>VpoTermsAndConditions</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF024</ERROR_ID>
          <FIELD_GROUP/>
          <GRID_ID>vpoTermsAndConditions</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="vpo_validation.xlsx,NumericRangeValidator">
    <ValidationField position="vpo_validation.xlsx,NumericRangeValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,NumericRangeValidator,8">
          <entityName>VpoShipDtl</entityName>
          <fieldId>originalQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <GREATE_THAN_VALUE/>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>originalQty</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vpo_validation.xlsx,NumericRangeValidator,9">
          <entityName>VpoShipDtl</entityName>
          <fieldId>qty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>1</MIN_VALUE>
          <GREATE_THAN_VALUE/>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>qty</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,NumericRangeValidator,12" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,NumericRangeValidator,19">
          <entityName>VpoShipDtl</entityName>
          <fieldId>originalQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <GREATE_THAN_VALUE/>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>originalQty</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="vpo_validation.xlsx,NumericRangeValidator,20">
          <entityName>VpoShipDtl</entityName>
          <fieldId>qty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>1</MIN_VALUE>
          <GREATE_THAN_VALUE/>
          <GRID_ID>vpoShipDtl</GRID_ID>
          <LABEL_FIELD_ID>qty</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="vpo_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="vpo_validation.xlsx,UniqueInModuleValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>vpoNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,UniqueInModuleValidator,11" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Vpo</entityName>
          <fieldId>vpoNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="vpo_validation.xlsx,EmailValidator">
    <ValidationField position="vpo_validation.xlsx,EmailValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,EmailValidator,8">
          <entityName>VpoContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,EmailValidator,11" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,EmailValidator,18">
          <entityName>VpoContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="vpo_validation.xlsx,ClassificationValidator">
    <ValidationField position="vpo_validation.xlsx,ClassificationValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,ClassificationValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,9">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,10">
          <entityName>Vpo</entityName>
          <fieldId>vpoItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,11">
          <entityName>Vpo</entityName>
          <fieldId>vpoShareFile</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShareFile</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>shareFileId</TARGET_FIELD>
          <DOCUMENT_NO>fileNo</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,12">
          <entityName>Vpo</entityName>
          <fieldId>vpoShip</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>forwarder</TARGET_FIELD>
          <DOCUMENT_NO>forwarderCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,ClassificationValidator,15" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,ClassificationValidator,22">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,23">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,24">
          <entityName>Vpo</entityName>
          <fieldId>vpoItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,25">
          <entityName>Vpo</entityName>
          <fieldId>vpoShareFile</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShareFile</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>shareFileId</TARGET_FIELD>
          <DOCUMENT_NO>fileNo</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,ClassificationValidator,26">
          <entityName>Vpo</entityName>
          <fieldId>vpoShip</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoShip</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>forwarder</TARGET_FIELD>
          <DOCUMENT_NO>forwarderCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="vpo_validation.xlsx,HCLValidator">
    <ValidationField position="vpo_validation.xlsx,HCLValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,HCLValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,HCLValidator,9">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,HCLValidator,10">
          <entityName>VpoItem</entityName>
          <fieldId>hierarchy</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD/>
          <TYPE>gridSelectBiz</TYPE>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,HCLValidator,13" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,HCLValidator,20">
          <entityName>Vpo</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,HCLValidator,21">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
        <element position="vpo_validation.xlsx,HCLValidator,22">
          <entityName>VpoItem</entityName>
          <fieldId>hierarchy</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD/>
          <TYPE>gridSelectBiz</TYPE>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="vpo_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="vpo_validation.xlsx,ExternalActiveValidator,1" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,ExternalActiveValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendorId</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="VendorFactValidator" position="vpo_validation.xlsx,VendorFactValidator">
    <ValidationField position="vpo_validation.xlsx,VendorFactValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,VendorFactValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>headerFactory</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>headerFactory</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>message.isVendorFact</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,9">
          <entityName>VpoItem</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>factId</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>message.isVendorFact</ERROR_ID>
          <GROUP_FIELD_ID>factId</GROUP_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,10">
          <entityName>Vpo</entityName>
          <fieldId>subContractor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>subContractor</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,11">
          <entityName>VpoItem</entityName>
          <fieldId>itemSubContractor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemSubContractor</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID>factoryAndSubContractor</GROUP_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,12">
          <entityName>VpoProductDtl</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>factName</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,VendorFactValidator,15" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,VendorFactValidator,22">
          <entityName>Vpo</entityName>
          <fieldId>headerFactory</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>headerFactory</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>message.isVendorFact</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,23">
          <entityName>VpoItem</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>factId</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>message.isVendorFact</ERROR_ID>
          <GROUP_FIELD_ID>factId</GROUP_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,24">
          <entityName>Vpo</entityName>
          <fieldId>subContractor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>subContractor</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,25">
          <entityName>VpoItem</entityName>
          <fieldId>itemSubContractor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <LABEL_FIELD_ID>itemSubContractor</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID>factoryAndSubContractor</GROUP_FIELD_ID>
        </element>
        <element position="vpo_validation.xlsx,VendorFactValidator,26">
          <entityName>VpoProductDtl</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoProductDtl</GRID_ID>
          <LABEL_FIELD_ID>factName</LABEL_FIELD_ID>
          <VENDOR_FIELD_ID>vendorId</VENDOR_FIELD_ID>
          <ERROR_ID>REF152</ERROR_ID>
          <GROUP_FIELD_ID/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="MatchOrderTypeValidator" position="vpo_validation.xlsx,MatchOrderTypeValidator">
    <ValidationField position="vpo_validation.xlsx,MatchOrderTypeValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,MatchOrderTypeValidator,8">
          <entityName>Vpo</entityName>
          <fieldId>orderType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <ERROR_ID>08010116</ERROR_ID>
        </element>
        <element position="vpo_validation.xlsx,MatchOrderTypeValidator,9">
          <entityName>VpoItem</entityName>
          <fieldId>itemType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoItem</GRID_ID>
          <ERROR_ID>08010116</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="AttachLinkMandatoryValidator" position="vpo_validation.xlsx,AttachLinkMandatoryValidator">
    <ValidationField position="vpo_validation.xlsx,AttachLinkMandatoryValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,AttachLinkMandatoryValidator,8">
          <entityName>VpoAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAttachment</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <validateBaseField>fileAddress</validateBaseField>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,AttachLinkMandatoryValidator,11" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,AttachLinkMandatoryValidator,18">
          <entityName>VpoAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoAttachment</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <validateBaseField>fileAddress</validateBaseField>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="AttachTypeValidator" position="vpo_validation.xlsx,AttachTypeValidator">
    <ValidationField position="vpo_validation.xlsx,AttachTypeValidator,1" profileId="9b48c460c12e4c3090b016cbb30bcd38" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,AttachTypeValidator,8">
          <entityName>VpoChainOfCustody</entityName>
          <fieldId>tableAttachments</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChainOfCustody</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="vpo_validation.xlsx,AttachTypeValidator,11" profileId="9b48c460c12e4c3090b016cbb30baaaa" profileName="">
      <elements id="default">
        <element position="vpo_validation.xlsx,AttachTypeValidator,18">
          <entityName>VpoChainOfCustody</entityName>
          <fieldId>tableAttachments</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>vpoChainOfCustody</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
