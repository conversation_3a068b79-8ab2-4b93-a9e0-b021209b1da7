<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="custInv" position="custInv_dataMappingRule.xlsx">
  <sheet id="custInvSelectAddress" position="custInv_dataMappingRule.xlsx,custInvSelectAddress">
    <DataMappingRule description="Mapping for Customer Address to Customer Invoice Address" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="custInvSelectAddress" position="custInv_dataMappingRule.xlsx,custInvSelectAddress,1" srcEntityName="CustAddress" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,10">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,15">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,16">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,17">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>district</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,18">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>invAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>invAddress.port</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>invAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectAddress,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>invAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectContact" position="custInv_dataMappingRule.xlsx,custInvSelectContact">
    <DataMappingRule description="Mapping for Customer Contact to Customer Invoice Contact" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="custInvSelectContact" position="custInv_dataMappingRule.xlsx,custInvSelectContact,1" srcEntityName="CustContact" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,17">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,18">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>invContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectContact,19">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>invContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invItemAddCpoDtl" position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl">
    <DataMappingRule description="Mapping for CpoShipDtl toCustInvItem" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="invItemAddCpoDtl" position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,1" srcEntityName="CpoShipDtl" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>arrDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>entity.cpoShipId.arrivalDate</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custInvItem.unitPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.cpoItemId.sellPrice</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custInvItem.arrDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>entity.cpoShipId.arrivalDate</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.cpoItemId.lotNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.cpoItemId.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.cpoItemId.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.cpoItemId.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custInvItem.customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.cpoItemId.customerItemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custInvItem.customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.cpoItemId.customerItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,18">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoShipDtlId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,19">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoItemId.itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,20">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipId</srcFieldId>
          <dstFieldId>cpoShip</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,21">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoItemId.uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,22">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoItemId.itemType</srcFieldId>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,23">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipId.forwarder</srcFieldId>
          <dstFieldId>forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,24">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipId.portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,25">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipDtlSize</srcFieldId>
          <dstFieldId>invItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,26">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlSize.cpoItemId</srcFieldId>
          <dstFieldId>invItemSize.itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,27">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlSize.itemSizeId</srcFieldId>
          <dstFieldId>invItemSize.itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,28">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,29">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlSize.displayName</srcFieldId>
          <dstFieldId>invItemSize.displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,30">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlSize.sizeCode</srcFieldId>
          <dstFieldId>invItemSize.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,31">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlSize.sizeName</srcFieldId>
          <dstFieldId>invItemSize.sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,32">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipDtlSize.cpoItemSizeId</srcFieldId>
          <dstFieldId>invItemSize.cpoItemSizeId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,33">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipDtlColor</srcFieldId>
          <dstFieldId>invItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,34">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlColor.cpoItemId</srcFieldId>
          <dstFieldId>invItemColor.itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,35">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlColor.itemColorId</srcFieldId>
          <dstFieldId>invItemColor.itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,36">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,37">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlColor.shortName</srcFieldId>
          <dstFieldId>invItemColor.shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,38">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlColor.colorCode</srcFieldId>
          <dstFieldId>invItemColor.colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,39">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoShipDtlColor.colorName</srcFieldId>
          <dstFieldId>invItemColor.colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,40">
          <mappingType>Section</mappingType>
          <srcFieldId>cpoShipDtlColor.cpoItemColorId</srcFieldId>
          <dstFieldId>invItemColor.cpoItemColorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpoDtl,44">
          <type>PostProcessor</type>
          <templateName>invItem_add_cpoShipDtl_cfg</templateName>
          <templateFile>invItem_add_cpoShipDtl_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvItemSelectCpoShipDtlProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="invItemAddCpo" position="custInv_dataMappingRule.xlsx,invItemAddCpo">
    <DataMappingRule description="Mapping for Cpo to CustInvItem" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="invItemAddCpo" position="custInv_dataMappingRule.xlsx,invItemAddCpo,1" srcEntityName="Cpo" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemAddCpo,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invItemCopy" position="custInv_dataMappingRule.xlsx,invItemCopy">
    <DataMappingRule description="Mapping for InvItem to CustInv" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="invItemCopy" position="custInv_dataMappingRule.xlsx,invItemCopy,1" srcEntityName="CustInvItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invItemCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>custInvItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invItemCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvCopyDoc" position="custInv_dataMappingRule.xlsx,custInvCopyDoc">
    <DataMappingRule description="Mapping from Cust Inv copy" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-03-14" id="custInvCopyDoc" position="custInv_dataMappingRule.xlsx,custInvCopyDoc,1" srcEntityName="CustInv" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvAddCust" position="custInv_dataMappingRule.xlsx,custInvAddCust">
    <DataMappingRule description="Mapping from Customer" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-03-14" id="custInvAddCust" position="custInv_dataMappingRule.xlsx,custInvAddCust,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,9">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,10">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,11">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,12">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,13">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,custInvAddCust,17">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvSelectCustomerPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="invContactCopy" position="custInv_dataMappingRule.xlsx,invContactCopy">
    <DataMappingRule description="Mapping for Customer Contact to Customer Invoice Contact" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-01-01" id="invContactCopy" position="custInv_dataMappingRule.xlsx,invContactCopy,1" srcEntityName="InvContact" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invContactCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invContactCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invAddressCopy" position="custInv_dataMappingRule.xlsx,invAddressCopy">
    <DataMappingRule description="Mapping for Customer Address to Customer Invoice Address" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-01-01" id="invAddressCopy" position="custInv_dataMappingRule.xlsx,invAddressCopy,1" srcEntityName="InvAddress" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invAttachmentCopy" position="custInv_dataMappingRule.xlsx,invAttachmentCopy">
    <DataMappingRule description="Mapping from Cust Inv Attachments copy" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-01-01" id="invAttachmentCopy" position="custInv_dataMappingRule.xlsx,invAttachmentCopy,1" srcEntityName="InvAttachment" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invAttachmentCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invAttachmentCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invChargeCopy" position="custInv_dataMappingRule.xlsx,invChargeCopy">
    <DataMappingRule description="Mapping for Copy CustInv Charge" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-03-15" id="invChargeCopy" position="custInv_dataMappingRule.xlsx,invChargeCopy,1" srcEntityName="InvCharge" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invChargeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invChargeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invCharge</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="invChargeOnDocCopy" position="custInv_dataMappingRule.xlsx,invChargeOnDocCopy">
    <DataMappingRule description="Mapping for Copy CustInv ChargeonDoc" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-01-01" id="invChargeOnDocCopy" position="custInv_dataMappingRule.xlsx,invChargeOnDocCopy,1" srcEntityName="InvChargeOnDoc" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,invChargeOnDocCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,invChargeOnDocCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>invChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectItem" position="custInv_dataMappingRule.xlsx,custInvSelectItem">
    <DataMappingRule description="Mapping for Item to Customer Invoice Item" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="custInvSelectItem" position="custInv_dataMappingRule.xlsx,custInvSelectItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,12">
          <mappingType>Section</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSize</srcFieldId>
          <dstFieldId>invItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false &amp;&amp; entity.dimension.code = 'SIZE'</condition>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>invItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive = false &amp;&amp; entity.isPrimary = true</condition>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,23">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,24">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,27">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,32">
          <type>PreProcessor</type>
          <templateName>Customer Invoice And Vendor Invoice select Item config</templateName>
          <templateFile>cust_and_vendor_inv_select_item_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustAndVendorInvSelectItemProcessor</implementationClass>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectItem,33">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvSelectItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectSAItemTask1" position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1">
    <DataMappingRule description="Mapping for Shipment Advice Item to Customer Invoice Item" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="custInvSelectSAItemTask1" position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,1" srcEntityName="ShipmentAdviceShipmentItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>containerNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.shipmentAdviceContainer.containerNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>unitPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.vendorPOShipmentItem.vpoItemId.sellPrice</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.vendorPOShipmentItem.vpoItemId.lotNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.vendorPOShipmentItem.vpoItemId.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.vendorPOShipmentItem.vpoItemId.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,17">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>sAShipItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.vendorPOShipmentItem.vpoItemId.itemType</mappedValue>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,19">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,20">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorPOShipmentItem.vpoItemId.cpoId</srcFieldId>
          <dstFieldId>cpoId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,21">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdviceItemSizes</srcFieldId>
          <dstFieldId>invItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,22">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,23">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,24">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,25">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,26">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeLabel</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,27">
          <mappingType>Field</mappingType>
          <srcFieldId>displayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,28">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdviceItemColors</srcFieldId>
          <dstFieldId>invItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,29">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,30">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,31">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,32">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,33">
          <mappingType>Field</mappingType>
          <srcFieldId>colorLabel</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,34">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,35">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdviceShipmentItems.shipmentAdviceItemCS</srcFieldId>
          <dstFieldId>invItemCs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,36">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,37">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,38">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,39">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSizeQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectSAItemTask1,43">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvSelectSAItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectVISItemTask1" position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1">
    <DataMappingRule description="Mapping for Vendor Invoice Shipment Item to Customer Invoice Item" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2014-08-19" id="custInvSelectVISItemTask1" position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,1" srcEntityName="VendorInvoiceShipItem" srcEntityVersion="1" status="1" updatedDate="2014-08-19">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,12">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,13">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,14">
          <mappingType>Field</mappingType>
          <srcFieldId>cpoNo</srcFieldId>
          <dstFieldId>cpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,15">
          <mappingType>Field</mappingType>
          <srcFieldId>actualArrivalDate</srcFieldId>
          <dstFieldId>arrDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,16">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>unitPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,17">
          <mappingType>Field</mappingType>
          <srcFieldId>invoiceQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,18">
          <mappingType>Field</mappingType>
          <srcFieldId>vesselFlightNo</srcFieldId>
          <dstFieldId>vesselFlight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,19">
          <mappingType>Field</mappingType>
          <srcFieldId>voyage</srcFieldId>
          <dstFieldId>voyage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,20">
          <mappingType>Field</mappingType>
          <srcFieldId>containerNo</srcFieldId>
          <dstFieldId>containerNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,21">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>vIShipItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,22">
          <mappingType>Field</mappingType>
          <srcFieldId>dispatchDate</srcFieldId>
          <dstFieldId>dispatchDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,23">
          <mappingType>Section</mappingType>
          <srcFieldId>VendorInvoiceShipItem.vendorPOItem.cpoId</srcFieldId>
          <dstFieldId>cpoId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,24">
          <mappingType>Section</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,25">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,26">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdvice</srcFieldId>
          <dstFieldId>shipmentAdvice</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,27">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,28">
          <mappingType>Section</mappingType>
          <srcFieldId>forwarder</srcFieldId>
          <dstFieldId>forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,29">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,30">
          <mappingType>Section</mappingType>
          <srcFieldId>viShipmentItemSizes</srcFieldId>
          <dstFieldId>invItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,31">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,32">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,33">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,34">
          <mappingType>Field</mappingType>
          <srcFieldId>displayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,35">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,37">
          <mappingType>Section</mappingType>
          <srcFieldId>viShipmentItemColors</srcFieldId>
          <dstFieldId>invItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,38">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,39">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,40">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,41">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,42">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,43">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,44">
          <mappingType>Section</mappingType>
          <srcFieldId>viShipmentItemCs</srcFieldId>
          <dstFieldId>invItemCs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,45">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,46">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,47">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,48">
          <mappingType>Field</mappingType>
          <srcFieldId>invoiceQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,49">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask1,53">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvSelectVISItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectVISItemTask2" position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask2">
    <DataMappingRule description="Mapping for Vendor Invoice Shipment Item to Customer Invoice Item" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2014-08-19" id="custInvSelectVISItemTask2" position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask2,1" srcEntityName="VendorInvoice" srcEntityVersion="1" status="1" updatedDate="2014-08-19">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectVISItemTask2,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendorInvoice</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoNewCustInv" position="custInv_dataMappingRule.xlsx,csoNewCustInv">
    <DataMappingRule description="Mapping for Customer Invoice, From Cso to Customer Invoice" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoNewCustInv" position="custInv_dataMappingRule.xlsx,csoNewCustInv,1" srcEntityName="Cso" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,9">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,10">
          <mappingType>Field</mappingType>
          <srcFieldId>otherTerms</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,11">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,12">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,13">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,14">
          <mappingType>Section</mappingType>
          <srcFieldId>customer</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,15">
          <mappingType>Section</mappingType>
          <srcFieldId>termsConditions</srcFieldId>
          <dstFieldId>termsConditions</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,16">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,17">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,18">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,19">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,20">
          <mappingType>Section</mappingType>
          <srcFieldId>csoChargeOnDoc</srcFieldId>
          <dstFieldId>invChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,21">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeType</srcFieldId>
          <dstFieldId>chargeType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,22">
          <mappingType>Field</mappingType>
          <srcFieldId>referencedDoc</srcFieldId>
          <dstFieldId>referencedDoc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,23">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeDesc</srcFieldId>
          <dstFieldId>chargeDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,24">
          <mappingType>Field</mappingType>
          <srcFieldId>reasonCode</srcFieldId>
          <dstFieldId>reasonCode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,25">
          <mappingType>Field</mappingType>
          <srcFieldId>calculateType</srcFieldId>
          <dstFieldId>calculateType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,26">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeValue</srcFieldId>
          <dstFieldId>chargeValue</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,27">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeAmt</srcFieldId>
          <dstFieldId>chargeAmt</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,28">
          <mappingType>Field</mappingType>
          <srcFieldId>notes</srcFieldId>
          <dstFieldId>notes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInv,32">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoNewCustInvPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoNewCustInvItem" position="custInv_dataMappingRule.xlsx,csoNewCustInvItem">
    <DataMappingRule description="Mapping for Customer Invoice, From Cso to Customer Invoice" domain="/" dstEntityName="CustInv" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoNewCustInvItem" position="custInv_dataMappingRule.xlsx,csoNewCustInvItem,1" srcEntityName="Cso" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInvItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,csoNewCustInvItem,12">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoNewCustInvItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="custInvSelectCsoItem" position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem">
    <DataMappingRule description="Mapping for Cso Item to Customer Invoice Item" domain="/" dstEntityName="CustInvItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="custInvSelectCsoItem" position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,1" srcEntityName="CsoItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>csoItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.itemType</mappedValue>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>csoNo</srcFieldId>
          <dstFieldId>csoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="custInv_dataMappingRule.xlsx,custInvSelectCsoItem,20">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CustInvSelectCsoItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
