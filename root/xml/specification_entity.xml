<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="specification" position="specification_entity.xlsx">
  <sheet id="_system" position="specification_entity.xlsx,_system">
    <ProjectInfo client="Base" position="specification_entity.xlsx,_system,1" project="SCMS" release_no="1.00"/>
    <ProductVersion position="specification_entity.xlsx,_system,7">
      <elements id="default">
        <element position="specification_entity.xlsx,_system,10">
          <updated_on>05-12月-2011</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="specification_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="N" main_entity="Specification" module="specification" position="specification_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
    <CustomField position="specification_entity.xlsx,generalInfo,7">
      <elements id="default">
        <element position="specification_entity.xlsx,generalInfo,10">
          <custom_field_type>Text</custom_field_type>
          <count>30</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,11">
          <custom_field_type>MemoText</custom_field_type>
          <count>20</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,12">
          <custom_field_type>Codelist</custom_field_type>
          <count>30</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,13">
          <custom_field_type>Number</custom_field_type>
          <count>20</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,14">
          <custom_field_type>Decimal</custom_field_type>
          <count>30</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,15">
          <custom_field_type>Date</custom_field_type>
          <count>20</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,16">
          <custom_field_type>Hcl</custom_field_type>
          <count>5</count>
        </element>
        <element position="specification_entity.xlsx,generalInfo,17">
          <custom_field_type>Checkbox</custom_field_type>
          <count>30</count>
        </element>
      </elements>
    </CustomField>
  </sheet>
  <sheet id="status" position="specification_entity.xlsx,status">
    <Status position="specification_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="specification_entity.xlsx,status,4">
          <code>customStatus01</code>
        </element>
        <element position="specification_entity.xlsx,status,5">
          <code>customStatus02</code>
        </element>
        <element position="specification_entity.xlsx,status,6">
          <code>customStatus03</code>
        </element>
        <element position="specification_entity.xlsx,status,7">
          <code>customStatus04</code>
        </element>
        <element position="specification_entity.xlsx,status,8">
          <code>customStatus05</code>
        </element>
        <element position="specification_entity.xlsx,status,9">
          <code>customStatus06</code>
        </element>
        <element position="specification_entity.xlsx,status,10">
          <code>customStatus07</code>
        </element>
        <element position="specification_entity.xlsx,status,11">
          <code>customStatus08</code>
        </element>
        <element position="specification_entity.xlsx,status,12">
          <code>customStatus09</code>
        </element>
        <element position="specification_entity.xlsx,status,13">
          <code>customStatus10</code>
        </element>
        <element position="specification_entity.xlsx,status,14">
          <code>requested</code>
        </element>
        <element position="specification_entity.xlsx,status,15">
          <code>submitted</code>
        </element>
        <element position="specification_entity.xlsx,status,16">
          <code>revisionRequest</code>
        </element>
        <element position="specification_entity.xlsx,status,17">
          <code>closed</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
  <sheet id="entityDef" position="specification_entity.xlsx,entityDef">
    <Entity name="Specification" position="specification_entity.xlsx,entityDef,1" ref_pattern="${specificationNo}" report_table_name="SPECIFICATION" table_name="CNT_SPECIFICATION">
      <elements id="reference">
        <element position="specification_entity.xlsx,entityDef,8">
          <entity_field_id>rfs</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Rfs.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,9">
          <entity_field_id>rfsNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfs.rfsNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,10">
          <entity_field_id>rfsShortDesc</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfs.shortDesc</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_SHORT_DESC</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,11">
          <entity_field_id>rfsSpecificationType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfs.specificationType</snapshot_field>
          <dataType>codelist</dataType>
          <data1>SPECIFICATION_TYPE</data1>
          <data2/>
          <report_column_name>RFS_SPECIFICATION_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,12">
          <entity_field_id>rfsExpiryDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfs.expiryDate</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_EXPIRY_DATE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,13">
          <entity_field_id>rfsNotesOrInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfs.notesOrInstructions</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_NOTE_OR_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,14">
          <entity_field_id>rfsItem</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>RfsItem.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_ITEM_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,15">
          <entity_field_id>notesInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfsItem.notesOrInstructions</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>NOTES_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,16">
          <entity_field_id>rfsVendor</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>RfsVendor.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RFS_VENDOR_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,17">
          <entity_field_id>vendorInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>rfsVendor.vendorInstructions</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,18">
          <entity_field_id>vq</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vq.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VQ_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,19">
          <entity_field_id>vqNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vq.vqNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VQ_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,20">
          <entity_field_id>measureTempl</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>MeasurementTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEASUREMENT_TEMPLATE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,21">
          <entity_field_id>templateName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>measureTempl.name</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEASUREMENT_TEMPLATE_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,22">
          <entity_field_id>component</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Component.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>COMPONENT_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,23">
          <entity_field_id>materialNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>component.componentNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MATERIAL_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,24">
          <entity_field_id>materialType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>component.materialType</snapshot_field>
          <dataType>codelist</dataType>
          <data1>COMPONENT_MATERIAL_TYPE</data1>
          <data2/>
          <report_column_name>MATERIAL_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,25">
          <entity_field_id>materialSubType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>component.materialSubType</snapshot_field>
          <dataType>codelist</dataType>
          <data1>COMPONENT_MATERIAL_SUB_TYPE</data1>
          <data2/>
          <report_column_name>MATERIAL_SUB_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,26">
          <entity_field_id>attributeSummary</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>component.attributeSummary</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTRIBUTE_SUMMARY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,27">
          <entity_field_id>otherRequirementTempId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>FactoryAuditTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OTHER_REQUIREMENT_TEMP</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,28">
          <entity_field_id>otherRequirementTempName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>otherRequirementTempId.name</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OTHER_REQUIREMENT_TEMP_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,29">
          <entity_field_id>otherRequirement2TempId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>FactoryAuditTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OTHER_REQUIREMENT2_TEMP</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,30">
          <entity_field_id>otherRequirement2TempName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>otherRequirement2TempId.name</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OTHER_REQUIREMENT2_TEMP_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,31">
          <entity_field_id>nutritionTemplate</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>NutritionTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEASUREMENT_TEMPLATE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,32">
          <entity_field_id>nutritionTemplateName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>nutritionTemplate.name</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEASUREMENT_TEMPLATE_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="specification_entity.xlsx,entityDef,36">
          <entity_field_id>specificationNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_ITEM_CODE", "system.pattern.ItemNo","ITM#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,37">
          <entity_field_id>buyerItemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>BUYER_ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,38">
          <entity_field_id>vendorItemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,39">
          <entity_field_id>refItem</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REF_ITEM</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,40">
          <entity_field_id>refBuyerItemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REF_BUYER_ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,41">
          <entity_field_id>setId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SET_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,42">
          <entity_field_id>setNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SET_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,43">
          <entity_field_id>isSet</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_SET</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,44">
          <entity_field_id>itemType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ITEM_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,45">
          <entity_field_id>isOrderIndividual</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_ORDER_INDIVIDUAL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,46">
          <entity_field_id>season</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SEASON</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SEASON</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,47">
          <entity_field_id>year</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>YEAR</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>YEAR</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,48">
          <entity_field_id>currency</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CURRENCY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>CURRENCY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,49">
          <entity_field_id>defaultSourcingRecord</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SourcingRecord.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DEFAULT_SOURCING_RECORD_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,50">
          <entity_field_id>defaultSeason</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SEASON</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DEFAULT_SEASON</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,51">
          <entity_field_id>defaultYear</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>YEAR</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DEFAULT_YEAR</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,52">
          <entity_field_id>itemBrand</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ITEM_BRAND</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_BRAND</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,53">
          <entity_field_id>originalShipmentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ORIGINAL_SHIPMENT_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,54">
          <entity_field_id>notesOrInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>NOTES_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,55">
          <entity_field_id>itemStyle</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ITEM_STYLE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_STYLE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,56">
          <entity_field_id>reOrder</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>RE_ORDER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,57">
          <entity_field_id>itemDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_DESC</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,58">
          <entity_field_id>shortDesc</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHORT_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,59">
          <entity_field_id>fileId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,60">
          <entity_field_id>imageAddress</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IMAGE_ADDRESS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,61">
          <entity_field_id>merchandiseHierarchy</entity_field_id>
          <entity_field_type>alias</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>//--DEPRECATED IN CNT-13429</description>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1>hierarchy</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,62">
          <entity_field_id>hierarchy</entity_field_id>
          <entity_field_type>hcl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MERCHANDISE_HIERARCHY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,63">
          <entity_field_id>defaultUom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DEFAULT_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,64">
          <entity_field_id>itemName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,65">
          <entity_field_id>quoteBrand</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Codelist.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>QUOTE_BRAND</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,66">
          <entity_field_id>masterCasePack</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MASTER_CASE_PACK</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,67">
          <entity_field_id>innerCasePack</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INNER_CASE_PACK</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,68">
          <entity_field_id>upcType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>UPC_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>UPC_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,69">
          <entity_field_id>masterUpc</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MASTER_UPC</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,70">
          <entity_field_id>eanType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>EAN_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>EAN_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,71">
          <entity_field_id>masterEan</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MASTER_EAN</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,72">
          <entity_field_id>vq2Ref</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VQ2_REF</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,73">
          <entity_field_id>sourcingDesc</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SOURCING_DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,74">
          <entity_field_id>sourcingNotesOrInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SOURCING_NOTES_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,75">
          <entity_field_id>targetPrice</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TARGET_PRICE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,76">
          <entity_field_id>landedCost</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>LANDED_COST</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,77">
          <entity_field_id>offerPrice</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>OFFER_PRICE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,78">
          <entity_field_id>retailPrice</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>RETAIL_PRICE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,79">
          <entity_field_id>margin</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MARGIN</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,80">
          <entity_field_id>ffDueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FF_DUE_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,81">
          <entity_field_id>dcDueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DC_DUE_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,82">
          <entity_field_id>deliveryTerm</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DELIVERY_TERM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DELIVERY_TERM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,83">
          <entity_field_id>market</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MARKET</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,84">
          <entity_field_id>destination</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DESTINATION</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,85">
          <entity_field_id>initialOrderQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INITIAL_ORDER_QTY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,86">
          <entity_field_id>totalQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,87">
          <entity_field_id>packType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>PACK_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>PACK_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,88">
          <entity_field_id>programDuration</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PROGRAM_DURATION</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,89">
          <entity_field_id>styleNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>STYLE_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,90">
          <entity_field_id>qqItemRefNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>QQ_ITEM_REF_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,91">
          <entity_field_id>specShortDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SPEC_SHORT_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,92">
          <entity_field_id>specDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SPEC_DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,93">
          <entity_field_id>specNotesOrInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SPEC_NOTES</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,94">
          <entity_field_id>hazardousGoods</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>HAZARDOUS_GOODS</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>HAZARDOUS_GOODS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,95">
          <entity_field_id>hangerType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>HANGER_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>HANGER_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,96">
          <entity_field_id>hangerSubType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>HANGER_SUB_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>HANGER_SUB_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,97">
          <entity_field_id>additionalDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ADDITIONAL_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,98">
          <entity_field_id>templateType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MEASUREMENT_TEMPLATE_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MEASUREMENT_TEMPLATE_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,99">
          <entity_field_id>templateDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MEASUREMENT_TEMPLATE_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,100">
          <entity_field_id>numberFormat</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>NUMBER_FORMAT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>NUMBER_FORMAT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,101">
          <entity_field_id>itemSizes</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_SIZES</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,102">
          <entity_field_id>sampleSize</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ItemSize.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_SIZE_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,103">
          <entity_field_id>sampleSizeUniqueKey</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_SIZE_UNIQUE_KEY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,104">
          <entity_field_id>sampleSizeLabel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_SIZE_LABEL</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,105">
          <entity_field_id>measurementType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MEASUREMENT_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MEASUREMENT_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,106">
          <entity_field_id>measurementUnit</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MEASUREMENT_UNIT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MEASUREMENT_UNIT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Snapshot</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,107">
          <entity_field_id>pomNotesOrInstructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>POM_NOTES_OR_INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,108">
          <entity_field_id>refPrice</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REF_PRICE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,109">
          <entity_field_id>refCurrency</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CURRENCY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>REF_CURRENCY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,110">
          <entity_field_id>sourcingAdditionalDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SOURCING_ADDITIONAL_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,111">
          <entity_field_id>materialSeason</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SEASON</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_SEASON</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,112">
          <entity_field_id>materialCountry</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>COUNTRY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_COUNTRY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,113">
          <entity_field_id>materialWastage</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_WASTAGE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,114">
          <entity_field_id>materialConsumption</entity_field_id>
          <entity_field_type>decimal-dimension</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_CONSUMPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,115">
          <entity_field_id>materialUOM</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MATERIAL_COST_UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,116">
          <entity_field_id>skuType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SKU_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SKU_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,117">
          <entity_field_id>skuNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SKU_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,118">
          <entity_field_id>hts</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant>2</constant>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>HTS</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>HTS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,119">
          <entity_field_id>vat</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VAT</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,120">
          <entity_field_id>vendor</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vendor.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,121">
          <entity_field_id>fact</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Fact.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="specification_entity.xlsx,entityDef,122">
          <entity_field_id>submittedBy</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SUBMITTED_BY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,123">
          <entity_field_id>submittedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SUBMITTED_ON</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,124">
          <entity_field_id>pomImage</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>POM_IMAGE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,125">
          <entity_field_id>sellingUnitSize</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SELLING_UNIT_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,126">
          <entity_field_id>sourcingOffice</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SOURCING_OFFICE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SOURCING_OFFICE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,127">
          <entity_field_id>unitPerFt20Container</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_PER_FT20_CONTAINER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,128">
          <entity_field_id>unitPerFt40Container</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_PER_FT40_CONTAINER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,129">
          <entity_field_id>unitPerFt40HCContainer</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_PER_FT40_H_C_CONTAINER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,130">
          <entity_field_id>unitPerFt45Container</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_PER_FT45_CONTAINER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,131">
          <entity_field_id>palletized</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PALLETIZED</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,132">
          <entity_field_id>palletType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>PALLET_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>PALLE_TTYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,133">
          <entity_field_id>ti</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TI</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,134">
          <entity_field_id>hi</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>HI</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,135">
          <entity_field_id>unitPerPallet</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_PER_PALLET</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,136">
          <entity_field_id>ingredientList</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INGREDIENT_LIST</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,137">
          <entity_field_id>halal</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>HALAL_STATUS</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>HALAL</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,138">
          <entity_field_id>maximumResidueLimits</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MAXIMUM_RESIDUE_LIMIT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MAXIMUM_RESIDUE_LIMITS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,139">
          <entity_field_id>dateCodeFormat</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SHELF_LIFE_DATE_FORMAT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DATE_CODE_FORMAT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,140">
          <entity_field_id>dateLotMarkingLocation</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DATE_LOT_MARKING_LOCATION</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DATE_LOT_MARKING_LOCATION</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,141">
          <entity_field_id>dateLotMarkingTech</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DATE_LOT_MARKING_TECH</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DATE_LOT_MARKING_TECH</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,142">
          <entity_field_id>servingsPerPackage</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SERVINGS_PER_PACKAGE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,143">
          <entity_field_id>servingSize</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SERVING_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,144">
          <entity_field_id>servingSizeUnit</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SERVING_SIZE_UNIT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SERVING_SIZE_UNIT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="specification_entity.xlsx,entityDef,145">
          <entity_field_id>isSaveOne</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_SAVE_ONE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="specification_entity.xlsx,entityDef,149">
          <entity_field_id>itemColor</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemColor.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,150">
          <entity_field_id>itemInnerColor</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemInnerColor.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,151">
          <entity_field_id>itemSize</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSize.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,152">
          <entity_field_id>itemSubItem</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSubItem.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SUB_ITEM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,153">
          <entity_field_id>itemColorSubItem</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemColorSubItem.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_COLOR_SUB_ITEM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,154">
          <entity_field_id>itemSizeSubItem</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSizeSubItem.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SIZE_SUB_ITEM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,155">
          <entity_field_id>itemSku</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSku.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SKU</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,156">
          <entity_field_id>specRequirement</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecRequirement.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_REQUIREMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,157">
          <entity_field_id>specMaterial</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecMaterial.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_MATERIAL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,158">
          <entity_field_id>specMaterialItemColor</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecMaterialItemColor.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPEC_MATERIAL_ITEM_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,159">
          <entity_field_id>specColorBom</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecColorBom.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_COLOR_BOM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,160">
          <entity_field_id>specColorBomItemColor</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecColorBomItemColor.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_COLOR_BOM_ITEM_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,161">
          <entity_field_id>specArtworkBom</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecArtworkBom.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_ARTWORK_BOM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,162">
          <entity_field_id>specDesign</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecDesign.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_DESIGN</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,163">
          <entity_field_id>specConstruction</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecConstruction.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_CONSTRUCTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,164">
          <entity_field_id>specInstruction</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecInstruction.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_INSTRUCTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,165">
          <entity_field_id>specFormulation</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecFormulation.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_FORMULATION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,166">
          <entity_field_id>specTreatment</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecTreatment.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_TREATMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,167">
          <entity_field_id>specOther</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecOther.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_OTHER</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,168">
          <entity_field_id>specGradingRule</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecGradingRule.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_GRADING</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,169">
          <entity_field_id>specGradingSize</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecGradingSize.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_GRADING_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,170">
          <entity_field_id>specMeasurement</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecMeasurement.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_MEASUREMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,171">
          <entity_field_id>specMeasurementSize</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecMeasurementSize.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPEC_MEASUREMENT_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,172">
          <entity_field_id>specAccessoriesMeasurement</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecAccessoriesMeasurement.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPEC_ACCESSORIES_MEASUREMENTS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,173">
          <entity_field_id>specAccessoryMeasureSize</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecAccessoryMeasureSize.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPEC_ACCESSORY_MEASURE_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,174">
          <entity_field_id>specPack</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecPack.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_PACKAGING</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,175">
          <entity_field_id>specPackArtwork</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SpecPackArtwork.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SPECIFICATION_PACK_ARTWORK</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,176">
          <entity_field_id>itemCust</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemCust.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_CUSTOMER</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,177">
          <entity_field_id>itemCustFinalDest</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemCustFinalDest.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_CUST_FINAL_DEST</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,178">
          <entity_field_id>itemSourAgent</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSourAgent.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SOURCING_AGENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,179">
          <entity_field_id>itemVendorFact</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemVendorFact.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_VENDOR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,180">
          <entity_field_id>itemRelated</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemRelated.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_RELATED</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,181">
          <entity_field_id>itemOther1</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemOther1.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_OTHER1</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,182">
          <entity_field_id>itemOther2</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemOther2.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_OTHER2</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,183">
          <entity_field_id>itemOther3</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemOther3.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_OTHER3</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,184">
          <entity_field_id>itemImage</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemImage.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_IMAGE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,185">
          <entity_field_id>itemAttachment</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemAttachment.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_ATTACHMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,186">
          <entity_field_id>itemPackingDefinition</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemPackingDefinition.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_PACKING_DEFINITION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,187">
          <entity_field_id>itemTermsAndConditions</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemTermsAndConditions.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,188">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <transitive_fields/>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,189">
          <entity_field_id>licenses</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>LICENSE</data1>
          <transitive_fields/>
          <report_column_name>LICENSE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,190">
          <entity_field_id>itemShareFile</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemShareFile.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SHARE_FILE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,191">
          <entity_field_id>itemOtherRequirements</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemOtherRequirement.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>item_Other_Requirement</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,192">
          <entity_field_id>itemOtherRequirement2s</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemOtherRequirement2.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>item_Other_Requirement</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,193">
          <entity_field_id>setItem</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SET_ITEM</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,194">
          <entity_field_id>shelfLifeInformation</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>SHELF_LIFE_TYPE</data1>
          <transitive_fields/>
          <report_column_name>SHELF_LIFE_INFORMATION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,195">
          <entity_field_id>itemComponent</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemComponent.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_COMPONENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,196">
          <entity_field_id>itemSensory</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemSensory.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SENSORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,197">
          <entity_field_id>itemPhysicalStandard</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemPhysicalStandard.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_PHYSICAL_STANDARD</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,198">
          <entity_field_id>itemChemicalStandard</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemChemicalStandard.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_CHEMICAL_STANDARD</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,199">
          <entity_field_id>itemAllergen</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemAllergen.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_ALLERGEN</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,200">
          <entity_field_id>itemShelfLifeAndStorage</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemShelfLifeAndStorage.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_SHELF_LIFE_AND_STORAGE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,201">
          <entity_field_id>itemBenchmark</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemBenchmark.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_BENCHMARK</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,202">
          <entity_field_id>itemMicrobiologicalStandard</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemMicrobiologicalStandard.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_MICROBIOLOGICAL_STANDARD</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,203">
          <entity_field_id>itemMetalAndContaminant</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemMetalAndContaminant.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ITEM_METAL_AND_CONTAMINANT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="specification_entity.xlsx,entityDef,204">
          <entity_field_id>itemNutrition</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemNutrition.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
        <element position="specification_entity.xlsx,entityDef,205">
          <entity_field_id>itemNutritionColumn</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemNutritionColumn.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
        <element position="specification_entity.xlsx,entityDef,206">
          <entity_field_id>itemNutritionTempCol</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ItemNutritionTempCol.itemId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
      </elements>
    </Entity>
  </sheet>
</entity>
