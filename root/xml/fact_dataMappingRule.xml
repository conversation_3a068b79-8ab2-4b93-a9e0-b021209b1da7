<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="fact" position="fact_dataMappingRule.xlsx">
  <sheet id="factCopyContact" position="fact_dataMappingRule.xlsx,factCopyContact">
    <DataMappingRule description="Factory Copy Contact Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factCopyContact" position="fact_dataMappingRule.xlsx,factCopyContact,1" srcEntityName="FactContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factCopyContact,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyContact,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyContact,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factAttachmentCopy" position="fact_dataMappingRule.xlsx,factAttachmentCopy">
    <DataMappingRule description="Factory Copy Attachment Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factAttachmentCopy" position="fact_dataMappingRule.xlsx,factAttachmentCopy,1" srcEntityName="FactAttachment" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factAttachmentCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factAttachmentCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factImageCopy" position="fact_dataMappingRule.xlsx,factImageCopy">
    <DataMappingRule description="Factory Copy Image Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factImageCopy" position="fact_dataMappingRule.xlsx,factImageCopy,1" srcEntityName="FactImage" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factImageCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factImageCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factAddressCopy" position="fact_dataMappingRule.xlsx,factAddressCopy">
    <DataMappingRule description="Factory Copy Address Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factAddressCopy" position="fact_dataMappingRule.xlsx,factAddressCopy,1" srcEntityName="FactAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factAddressCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="financialDtlCopy" position="fact_dataMappingRule.xlsx,financialDtlCopy">
    <DataMappingRule description="Factory Copy Financial Detail Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="financialDtlCopy" position="fact_dataMappingRule.xlsx,financialDtlCopy,1" srcEntityName="FinancialDtl" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,financialDtlCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,financialDtlCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>financialDtl</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,financialDtlCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factEmployeCopy" position="fact_dataMappingRule.xlsx,factEmployeCopy">
    <DataMappingRule description="Factory Copy Fact Employees Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factEmployeeCopy" position="fact_dataMappingRule.xlsx,factEmployeCopy,1" srcEntityName="FactEmployee" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factEmployeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factEmployeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factEmployee</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="productCapacityCopy" position="fact_dataMappingRule.xlsx,productCapacityCopy">
    <DataMappingRule description="Factory Copy Product Capacity Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="productCapacityCopy" position="fact_dataMappingRule.xlsx,productCapacityCopy,1" srcEntityName="ProductCapacity" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,productCapacityCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,productCapacityCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>productCapacity</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factCustSelect" position="fact_dataMappingRule.xlsx,factCustSelect">
    <DataMappingRule description="Factory Select Customer Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factCustSelect" position="fact_dataMappingRule.xlsx,factCustSelect,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factCustSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factCust</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>factCust.custId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>factCust.custName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>custCode</srcFieldId>
          <dstFieldId>factCust.custCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>factCust.custDocStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCustSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>factCust.isLatestCust</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factVendorSelect" position="fact_dataMappingRule.xlsx,factVendorSelect">
    <DataMappingRule description="Factory Select Vendor Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factVendorSelect" position="fact_dataMappingRule.xlsx,factVendorSelect,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendorFact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,10">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorTypeId</srcFieldId>
          <dstFieldId>vendorFact.vendorType</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,11">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorSubType</srcFieldId>
          <dstFieldId>vendorFact.vendorSubType</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>vendorFact.vendorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>vendorFact.vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorFact.vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>vendorFact.vendorDocStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>vendorFact.isLatestVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factCopyDoc" position="fact_dataMappingRule.xlsx,factCopyDoc">
    <DataMappingRule description="Mapping from factory copy" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-03-14" id="factCopyDoc" position="fact_dataMappingRule.xlsx,factCopyDoc,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId>factCode</srcFieldId>
          <dstFieldId>factCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,14">
          <mappingType>Field</mappingType>
          <srcFieldId>syncByVendor</srcFieldId>
          <dstFieldId>syncByVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,15">
          <mappingType>Field</mappingType>
          <srcFieldId>isFactAccess</srcFieldId>
          <dstFieldId>isFactAccess</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="fact_dataMappingRule.xlsx,factCopyDoc,19">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.FactCopyDocProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="factKeyCustomerCopy" position="fact_dataMappingRule.xlsx,factKeyCustomerCopy">
    <DataMappingRule description="Factory Copy Key Customer Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2013-08-06" id="factKeyCustomerCopy" position="fact_dataMappingRule.xlsx,factKeyCustomerCopy,1" srcEntityName="FactKeyCustomer" srcEntityVersion="1" status="1" updatedDate="2013-08-06">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factKeyCustomerCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factKeyCustomerCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>keyCustomers</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factHistoricalCopy" position="fact_dataMappingRule.xlsx,factHistoricalCopy">
    <DataMappingRule description="Factory Copy Historical Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2013-08-06" id="factHistoricalCopy" position="fact_dataMappingRule.xlsx,factHistoricalCopy,1" srcEntityName="FactHistorical" srcEntityVersion="1" status="1" updatedDate="2013-08-06">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factHistoricalCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factHistoricalCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>historicals</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factHcSelect" position="fact_dataMappingRule.xlsx,factHcSelect">
    <DataMappingRule description="Factory Select HCL" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factHcSelect" position="fact_dataMappingRule.xlsx,factHcSelect,1" srcEntityName="HclNode" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factHcSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factHcSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factHc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factToDomain" position="fact_dataMappingRule.xlsx,factToDomain">
    <DataMappingRule description="Fact To Domain" domain="/" dstEntityName="Domain" dstEntityVersion="1" effectiveDate="2012-02-20" id="factToDomain" position="fact_dataMappingRule.xlsx,factToDomain,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factToDomain,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,9">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,11">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>parentPath</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>/</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>parentId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>/</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>levels</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>2</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>enabled</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factToDomain,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factContactToUser" position="fact_dataMappingRule.xlsx,factContactToUser">
    <DataMappingRule description="Fact Contact To User" domain="/" dstEntityName="User" dstEntityVersion="1" effectiveDate="2012-02-20" id="factContactToUser" position="fact_dataMappingRule.xlsx,factContactToUser,1" srcEntityName="FactContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factContactToUser,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,9">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,10">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,11">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>loginId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,12">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,13">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,14">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>phone</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,15">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>phone</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,16">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobile</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,17">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>fax</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factContactToUser,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="externalFactoryUpdates" position="fact_dataMappingRule.xlsx,externalFactoryUpdates">
    <DataMappingRule description="Updated by external Factory" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="externalFactoryUpdates" position="fact_dataMappingRule.xlsx,externalFactoryUpdates,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,9">
          <mappingType>Field</mappingType>
          <srcFieldId>reference</srcFieldId>
          <dstFieldId>reference</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>businessName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,11">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,12">
          <mappingType>Field</mappingType>
          <srcFieldId>parentGroup</srcFieldId>
          <dstFieldId>parentGroup</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,13">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,15">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,16">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,17">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,18">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLng</srcFieldId>
          <dstFieldId>gpsLng</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,19">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLat</srcFieldId>
          <dstFieldId>gpsLat</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,20">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,21">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,22">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,23">
          <mappingType>Field</mappingType>
          <srcFieldId>addressRemarks</srcFieldId>
          <dstFieldId>addressRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,24">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstruction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,25">
          <mappingType>Field</mappingType>
          <srcFieldId>leadTime</srcFieldId>
          <dstFieldId>leadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,26">
          <mappingType>Field</mappingType>
          <srcFieldId>minOrderQty</srcFieldId>
          <dstFieldId>minOrderQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,27">
          <mappingType>Field</mappingType>
          <srcFieldId>exportLicenseNo</srcFieldId>
          <dstFieldId>exportLicenseNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,28">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>contactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,29">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,30">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,31">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,32">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,33">
          <mappingType>Field</mappingType>
          <srcFieldId>contactRemarks</srcFieldId>
          <dstFieldId>contactRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,34">
          <mappingType>Field</mappingType>
          <srcFieldId>background</srcFieldId>
          <dstFieldId>background</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,35">
          <mappingType>Field</mappingType>
          <srcFieldId>companyEmail</srcFieldId>
          <dstFieldId>companyEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,36">
          <mappingType>Field</mappingType>
          <srcFieldId>companyWebsite</srcFieldId>
          <dstFieldId>companyWebsite</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,37">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfEmployees</srcFieldId>
          <dstFieldId>noOfEmployees</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,38">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRegistrationNo</srcFieldId>
          <dstFieldId>businessRegistrationNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,39">
          <mappingType>Field</mappingType>
          <srcFieldId>yearEstablished</srcFieldId>
          <dstFieldId>yearEstablished</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,40">
          <mappingType>Field</mappingType>
          <srcFieldId>portalAccessEnabled</srcFieldId>
          <dstFieldId>portalAccessEnabled</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,41">
          <mappingType>Field</mappingType>
          <srcFieldId>productionFloorArea</srcFieldId>
          <dstFieldId>productionFloorArea</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,42">
          <mappingType>Field</mappingType>
          <srcFieldId>productionMonthlyCapacity</srcFieldId>
          <dstFieldId>productionMonthlyCapacity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,43">
          <mappingType>Field</mappingType>
          <srcFieldId>lastTechnicalComplianceDate</srcFieldId>
          <dstFieldId>lastTechnicalComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,44">
          <mappingType>Field</mappingType>
          <srcFieldId>lastSocialComplianceDate</srcFieldId>
          <dstFieldId>lastSocialComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,45">
          <mappingType>Field</mappingType>
          <srcFieldId>products</srcFieldId>
          <dstFieldId>products</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,46">
          <mappingType>Field</mappingType>
          <srcFieldId>score</srcFieldId>
          <dstFieldId>score</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,47">
          <mappingType>Field</mappingType>
          <srcFieldId>qaSummary</srcFieldId>
          <dstFieldId>qaSummary</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,48">
          <mappingType>Field</mappingType>
          <srcFieldId>accredited</srcFieldId>
          <dstFieldId>accredited</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,49">
          <mappingType>Section</mappingType>
          <srcFieldId>deactivationReason</srcFieldId>
          <dstFieldId>deactivationReason</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,50">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,51">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>shipmentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,52">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,53">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,54">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,55">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,56">
          <mappingType>Section</mappingType>
          <srcFieldId>placeOfIncorporation</srcFieldId>
          <dstFieldId>placeOfIncorporation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,57">
          <mappingType>Section</mappingType>
          <srcFieldId>preferredLanguage</srcFieldId>
          <dstFieldId>preferredLanguage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,58">
          <mappingType>Section</mappingType>
          <srcFieldId>rank</srcFieldId>
          <dstFieldId>rank</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,59">
          <mappingType>Section</mappingType>
          <srcFieldId>riskLevel</srcFieldId>
          <dstFieldId>riskLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,60">
          <mappingType>Section</mappingType>
          <srcFieldId>assessmentLevel</srcFieldId>
          <dstFieldId>assessmentLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,61">
          <mappingType>Section</mappingType>
          <srcFieldId>performanceLevel</srcFieldId>
          <dstFieldId>performanceLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,62">
          <mappingType>Section</mappingType>
          <srcFieldId>factTypeId</srcFieldId>
          <dstFieldId>factTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,63">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl</srcFieldId>
          <dstFieldId>financialDtl</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,64">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,65">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl.paymentMethod</srcFieldId>
          <dstFieldId>financialDtl.paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,66">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl.paymentTerm</srcFieldId>
          <dstFieldId>financialDtl.paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,67">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl.currency</srcFieldId>
          <dstFieldId>financialDtl.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,68">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstruction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,69">
          <mappingType>Field</mappingType>
          <srcFieldId>accountName</srcFieldId>
          <dstFieldId>accountName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,70">
          <mappingType>Field</mappingType>
          <srcFieldId>accountNo</srcFieldId>
          <dstFieldId>accountNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,71">
          <mappingType>Field</mappingType>
          <srcFieldId>accountRefNo</srcFieldId>
          <dstFieldId>accountRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,72">
          <mappingType>Field</mappingType>
          <srcFieldId>bankName</srcFieldId>
          <dstFieldId>bankName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,73">
          <mappingType>Field</mappingType>
          <srcFieldId>bankAddresses</srcFieldId>
          <dstFieldId>bankAddresses</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,74">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl.country</srcFieldId>
          <dstFieldId>financialDtl.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,75">
          <mappingType>Field</mappingType>
          <srcFieldId>bankCode</srcFieldId>
          <dstFieldId>bankCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,76">
          <mappingType>Field</mappingType>
          <srcFieldId>swiftCode</srcFieldId>
          <dstFieldId>swiftCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,77">
          <mappingType>Field</mappingType>
          <srcFieldId>vatNo</srcFieldId>
          <dstFieldId>vatNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,78">
          <mappingType>Field</mappingType>
          <srcFieldId>avgRate</srcFieldId>
          <dstFieldId>avgRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,79">
          <mappingType>Field</mappingType>
          <srcFieldId>totalRate</srcFieldId>
          <dstFieldId>totalRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,80">
          <mappingType>Field</mappingType>
          <srcFieldId>exportRate</srcFieldId>
          <dstFieldId>exportRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,81">
          <mappingType>Field</mappingType>
          <srcFieldId>creditLimit</srcFieldId>
          <dstFieldId>creditLimit</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,82">
          <mappingType>Field</mappingType>
          <srcFieldId>creditReport</srcFieldId>
          <dstFieldId>creditReport</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,83">
          <mappingType>Section</mappingType>
          <srcFieldId>factHc</srcFieldId>
          <dstFieldId>factHc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,84">
          <mappingType>Field</mappingType>
          <srcFieldId>hclTypeName</srcFieldId>
          <dstFieldId>hclTypeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,85">
          <mappingType>Field</mappingType>
          <srcFieldId>hclLevelName</srcFieldId>
          <dstFieldId>hclLevelName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,86">
          <mappingType>Field</mappingType>
          <srcFieldId>hclNodeCode</srcFieldId>
          <dstFieldId>hclNodeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,87">
          <mappingType>Field</mappingType>
          <srcFieldId>hclFullLineage</srcFieldId>
          <dstFieldId>hclFullLineage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,88">
          <mappingType>Field</mappingType>
          <srcFieldId>hclId</srcFieldId>
          <dstFieldId>hclId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,89">
          <mappingType>Field</mappingType>
          <srcFieldId>hclNodeId</srcFieldId>
          <dstFieldId>hclNodeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,90">
          <mappingType>Field</mappingType>
          <srcFieldId>hclTypeId</srcFieldId>
          <dstFieldId>hclTypeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,91">
          <mappingType>Field</mappingType>
          <srcFieldId>hclTypeLevelId</srcFieldId>
          <dstFieldId>hclTypeLevelId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,92">
          <mappingType>Field</mappingType>
          <srcFieldId>percentageOfBusiness</srcFieldId>
          <dstFieldId>percentageOfBusiness</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,93">
          <mappingType>Field</mappingType>
          <srcFieldId>hclNodeLevel</srcFieldId>
          <dstFieldId>hclNodeLevel</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,94">
          <mappingType>Field</mappingType>
          <srcFieldId>hclName</srcFieldId>
          <dstFieldId>hclName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,95">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress</srcFieldId>
          <dstFieldId>factAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,96">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,97">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,98">
          <mappingType>Field</mappingType>
          <srcFieldId>addressType</srcFieldId>
          <dstFieldId>addressType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,99">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,100">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,101">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,102">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,103">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,104">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,105">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,106">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>businessName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,107">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,108">
          <mappingType>Field</mappingType>
          <srcFieldId>reference</srcFieldId>
          <dstFieldId>reference</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,109">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,110">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress.country</srcFieldId>
          <dstFieldId>factAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,111">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress.language</srcFieldId>
          <dstFieldId>factAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,112">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress.portOfDischarge</srcFieldId>
          <dstFieldId>factAddress.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,113">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress.addressTypeId</srcFieldId>
          <dstFieldId>factAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,114">
          <mappingType>Section</mappingType>
          <srcFieldId>factContact</srcFieldId>
          <dstFieldId>factContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,115">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,116">
          <mappingType>Field</mappingType>
          <srcFieldId>contactType</srcFieldId>
          <dstFieldId>contactType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,117">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,118">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,119">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,120">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,121">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,122">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,123">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,124">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,125">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,126">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,127">
          <mappingType>Section</mappingType>
          <srcFieldId>factContact.title</srcFieldId>
          <dstFieldId>factContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,128">
          <mappingType>Section</mappingType>
          <srcFieldId>factContact.contactTypeId</srcFieldId>
          <dstFieldId>factContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,129">
          <mappingType>Section</mappingType>
          <srcFieldId>factEmployee</srcFieldId>
          <dstFieldId>factEmployee</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,130">
          <mappingType>Section</mappingType>
          <srcFieldId>factEmployee.employeeDepartment</srcFieldId>
          <dstFieldId>factEmployee.employeeDepartment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,131">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfEmployees</srcFieldId>
          <dstFieldId>noOfEmployees</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,132">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfWorkingDay</srcFieldId>
          <dstFieldId>noOfWorkingDay</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,133">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfShiftsDay</srcFieldId>
          <dstFieldId>noOfShiftsDay</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,134">
          <mappingType>Section</mappingType>
          <srcFieldId>productCapacity</srcFieldId>
          <dstFieldId>productCapacity</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,135">
          <mappingType>Field</mappingType>
          <srcFieldId>productType</srcFieldId>
          <dstFieldId>productType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,136">
          <mappingType>Field</mappingType>
          <srcFieldId>monthlyCapacity</srcFieldId>
          <dstFieldId>monthlyCapacity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,137">
          <mappingType>Section</mappingType>
          <srcFieldId>productCapacity.uom</srcFieldId>
          <dstFieldId>productCapacity.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,138">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,139">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers1</srcFieldId>
          <dstFieldId>factoryOthers1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,140">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers2</srcFieldId>
          <dstFieldId>factoryOthers2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,141">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers3</srcFieldId>
          <dstFieldId>factoryOthers3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,142">
          <mappingType>Section</mappingType>
          <srcFieldId>factImage</srcFieldId>
          <dstFieldId>factImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,143">
          <mappingType>Field</mappingType>
          <srcFieldId>imageType</srcFieldId>
          <dstFieldId>imageType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,144">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,145">
          <mappingType>Section</mappingType>
          <srcFieldId>factImage.fileId</srcFieldId>
          <dstFieldId>factImage.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,146">
          <mappingType>Section</mappingType>
          <srcFieldId>factImage.imageTypeId</srcFieldId>
          <dstFieldId>factImage.imageTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,147">
          <mappingType>Section</mappingType>
          <srcFieldId>factAttachment</srcFieldId>
          <dstFieldId>factAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,148">
          <mappingType>Field</mappingType>
          <srcFieldId>attachmentType</srcFieldId>
          <dstFieldId>imageType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,149">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,150">
          <mappingType>Section</mappingType>
          <srcFieldId>factAttachment.fileId</srcFieldId>
          <dstFieldId>factAttachment.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,externalFactoryUpdates,151">
          <mappingType>Section</mappingType>
          <srcFieldId>factAttachment.attachTypeId</srcFieldId>
          <dstFieldId>factAttachment.attachTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factDeliver" position="fact_dataMappingRule.xlsx,factDeliver">
    <DataMappingRule description="Mapping for Factory Deliver" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-03-14" id="factDeliver" position="fact_dataMappingRule.xlsx,factDeliver,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entityVersion,isForReference,createdOn,integrationSource,integrationStatus,integrationNote</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>reference</srcFieldId>
          <dstFieldId>reference</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>factCode</srcFieldId>
          <dstFieldId>factCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>businessName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>parentGroup</srcFieldId>
          <dstFieldId>parentGroup</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLng</srcFieldId>
          <dstFieldId>gpsLng</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLat</srcFieldId>
          <dstFieldId>gpsLat</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>addressRemarks</srcFieldId>
          <dstFieldId>addressRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,38">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstruction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,39">
          <mappingType>Field</mappingType>
          <srcFieldId>leadTime</srcFieldId>
          <dstFieldId>leadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,40">
          <mappingType>Field</mappingType>
          <srcFieldId>minOrderQty</srcFieldId>
          <dstFieldId>minOrderQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,41">
          <mappingType>Field</mappingType>
          <srcFieldId>exportLicenseNo</srcFieldId>
          <dstFieldId>exportLicenseNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,42">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>contactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,43">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,44">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,45">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,46">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,47">
          <mappingType>Field</mappingType>
          <srcFieldId>contactRemarks</srcFieldId>
          <dstFieldId>contactRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,48">
          <mappingType>Field</mappingType>
          <srcFieldId>background</srcFieldId>
          <dstFieldId>background</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,49">
          <mappingType>Field</mappingType>
          <srcFieldId>companyEmail</srcFieldId>
          <dstFieldId>companyEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,50">
          <mappingType>Field</mappingType>
          <srcFieldId>companyWebsite</srcFieldId>
          <dstFieldId>companyWebsite</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,51">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfEmployees</srcFieldId>
          <dstFieldId>noOfEmployees</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRegistrationNo</srcFieldId>
          <dstFieldId>businessRegistrationNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>yearEstablished</srcFieldId>
          <dstFieldId>yearEstablished</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>portalAccessEnabled</srcFieldId>
          <dstFieldId>portalAccessEnabled</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>productionFloorArea</srcFieldId>
          <dstFieldId>productionFloorArea</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>productionMonthlyCapacity</srcFieldId>
          <dstFieldId>productionMonthlyCapacity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>lastTechnicalComplianceDate</srcFieldId>
          <dstFieldId>lastTechnicalComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>lastSocialComplianceDate</srcFieldId>
          <dstFieldId>lastSocialComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>products</srcFieldId>
          <dstFieldId>products</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>score</srcFieldId>
          <dstFieldId>score</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>qaSummary</srcFieldId>
          <dstFieldId>qaSummary</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>accredited</srcFieldId>
          <dstFieldId>accredited</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>profileType</srcFieldId>
          <dstFieldId>profileType</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>syncByVendor</srcFieldId>
          <dstFieldId>syncByVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,68">
          <mappingType>Section</mappingType>
          <srcFieldId>zone</srcFieldId>
          <dstFieldId>zone</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,69">
          <mappingType>Section</mappingType>
          <srcFieldId>deactivationReason</srcFieldId>
          <dstFieldId>deactivationReason</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,70">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,71">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>shipmentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,72">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,73">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,74">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,75">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,76">
          <mappingType>Section</mappingType>
          <srcFieldId>placeOfIncorporation</srcFieldId>
          <dstFieldId>placeOfIncorporation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,77">
          <mappingType>Section</mappingType>
          <srcFieldId>preferredLanguage</srcFieldId>
          <dstFieldId>preferredLanguage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,78">
          <mappingType>Section</mappingType>
          <srcFieldId>rank</srcFieldId>
          <dstFieldId>rank</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,79">
          <mappingType>Section</mappingType>
          <srcFieldId>riskLevel</srcFieldId>
          <dstFieldId>riskLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,80">
          <mappingType>Section</mappingType>
          <srcFieldId>assessmentLevel</srcFieldId>
          <dstFieldId>assessmentLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,81">
          <mappingType>Section</mappingType>
          <srcFieldId>performanceLevel</srcFieldId>
          <dstFieldId>performanceLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,82">
          <mappingType>Section</mappingType>
          <srcFieldId>factTypeId</srcFieldId>
          <dstFieldId>factTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,83">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl</srcFieldId>
          <dstFieldId>financialDtl</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,84">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress</srcFieldId>
          <dstFieldId>factAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,85">
          <mappingType>Section</mappingType>
          <srcFieldId>factContact</srcFieldId>
          <dstFieldId>factContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,86">
          <mappingType>Section</mappingType>
          <srcFieldId>factEmployee</srcFieldId>
          <dstFieldId>factEmployee</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>productCapacity</srcFieldId>
          <dstFieldId>productCapacity</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers1</srcFieldId>
          <dstFieldId>factoryOthers1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers2</srcFieldId>
          <dstFieldId>factoryOthers2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers3</srcFieldId>
          <dstFieldId>factoryOthers3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>certification</srcFieldId>
          <dstFieldId>certification</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>productionArea</srcFieldId>
          <dstFieldId>productionArea</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>processingDetail</srcFieldId>
          <dstFieldId>processingDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>factImage</srcFieldId>
          <dstFieldId>factImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>factAttachment</srcFieldId>
          <dstFieldId>factAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,96">
          <mappingType>Section</mappingType>
          <srcFieldId>factHc</srcFieldId>
          <dstFieldId>factHc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,97">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,98">
          <mappingType>Section</mappingType>
          <srcFieldId>productType</srcFieldId>
          <dstFieldId>productType</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,99">
          <mappingType>Section</mappingType>
          <srcFieldId>factShareFile</srcFieldId>
          <dstFieldId>factShareFile</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,100">
          <mappingType>Section</mappingType>
          <srcFieldId>capability</srcFieldId>
          <dstFieldId>capability</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factDeliver,101">
          <mappingType>Section</mappingType>
          <srcFieldId>factAgreements</srcFieldId>
          <dstFieldId>factAgreements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factVendorDeliver" position="fact_dataMappingRule.xlsx,factVendorDeliver">
    <DataMappingRule description="Mapping for Factory Deliver" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-03-14" id="factVendorDeliver" position="fact_dataMappingRule.xlsx,factVendorDeliver,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entityVersion,isForReference,createdOn,integrationSource,integrationStatus,integrationNote,factHc,productCategory,agreementStatus,agreementTemplateName,agreementTemplate,onBoardingProgress,profileType,factAgreements</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>reference</srcFieldId>
          <dstFieldId>reference</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>factCode</srcFieldId>
          <dstFieldId>factCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>businessName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>parentGroup</srcFieldId>
          <dstFieldId>parentGroup</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLng</srcFieldId>
          <dstFieldId>gpsLng</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>gpsLat</srcFieldId>
          <dstFieldId>gpsLat</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>addressRemarks</srcFieldId>
          <dstFieldId>addressRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,38">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstruction</srcFieldId>
          <dstFieldId>paymentInstruction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,39">
          <mappingType>Field</mappingType>
          <srcFieldId>leadTime</srcFieldId>
          <dstFieldId>leadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,40">
          <mappingType>Field</mappingType>
          <srcFieldId>minOrderQty</srcFieldId>
          <dstFieldId>minOrderQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,41">
          <mappingType>Field</mappingType>
          <srcFieldId>exportLicenseNo</srcFieldId>
          <dstFieldId>exportLicenseNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,42">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>contactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,43">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,44">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,45">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,46">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,47">
          <mappingType>Field</mappingType>
          <srcFieldId>contactRemarks</srcFieldId>
          <dstFieldId>contactRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,48">
          <mappingType>Field</mappingType>
          <srcFieldId>background</srcFieldId>
          <dstFieldId>background</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,49">
          <mappingType>Field</mappingType>
          <srcFieldId>companyEmail</srcFieldId>
          <dstFieldId>companyEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,50">
          <mappingType>Field</mappingType>
          <srcFieldId>companyWebsite</srcFieldId>
          <dstFieldId>companyWebsite</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,51">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfEmployees</srcFieldId>
          <dstFieldId>noOfEmployees</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRegistrationNo</srcFieldId>
          <dstFieldId>businessRegistrationNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>yearEstablished</srcFieldId>
          <dstFieldId>yearEstablished</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>portalAccessEnabled</srcFieldId>
          <dstFieldId>portalAccessEnabled</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>productionFloorArea</srcFieldId>
          <dstFieldId>productionFloorArea</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>productionMonthlyCapacity</srcFieldId>
          <dstFieldId>productionMonthlyCapacity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>lastTechnicalComplianceDate</srcFieldId>
          <dstFieldId>lastTechnicalComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>lastSocialComplianceDate</srcFieldId>
          <dstFieldId>lastSocialComplianceDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>products</srcFieldId>
          <dstFieldId>products</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>score</srcFieldId>
          <dstFieldId>score</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>qaSummary</srcFieldId>
          <dstFieldId>qaSummary</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>accredited</srcFieldId>
          <dstFieldId>accredited</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>profileType</srcFieldId>
          <dstFieldId>profileType</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>syncByVendor</srcFieldId>
          <dstFieldId>syncByVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,68">
          <mappingType>Section</mappingType>
          <srcFieldId>zone</srcFieldId>
          <dstFieldId>zone</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,69">
          <mappingType>Section</mappingType>
          <srcFieldId>deactivationReason</srcFieldId>
          <dstFieldId>deactivationReason</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,70">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,71">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>shipmentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,72">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,73">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,74">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,75">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,76">
          <mappingType>Section</mappingType>
          <srcFieldId>placeOfIncorporation</srcFieldId>
          <dstFieldId>placeOfIncorporation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,77">
          <mappingType>Section</mappingType>
          <srcFieldId>preferredLanguage</srcFieldId>
          <dstFieldId>preferredLanguage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,78">
          <mappingType>Section</mappingType>
          <srcFieldId>rank</srcFieldId>
          <dstFieldId>rank</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,79">
          <mappingType>Section</mappingType>
          <srcFieldId>riskLevel</srcFieldId>
          <dstFieldId>riskLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,80">
          <mappingType>Section</mappingType>
          <srcFieldId>assessmentLevel</srcFieldId>
          <dstFieldId>assessmentLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,81">
          <mappingType>Section</mappingType>
          <srcFieldId>performanceLevel</srcFieldId>
          <dstFieldId>performanceLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,82">
          <mappingType>Section</mappingType>
          <srcFieldId>factTypeId</srcFieldId>
          <dstFieldId>factTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,83">
          <mappingType>Section</mappingType>
          <srcFieldId>financialDtl</srcFieldId>
          <dstFieldId>financialDtl</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,84">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress</srcFieldId>
          <dstFieldId>factAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,85">
          <mappingType>Section</mappingType>
          <srcFieldId>factContact</srcFieldId>
          <dstFieldId>factContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,86">
          <mappingType>Section</mappingType>
          <srcFieldId>factEmployee</srcFieldId>
          <dstFieldId>factEmployee</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>productCapacity</srcFieldId>
          <dstFieldId>productCapacity</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>certification</srcFieldId>
          <dstFieldId>certification</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>productionArea</srcFieldId>
          <dstFieldId>productionArea</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>processingDetail</srcFieldId>
          <dstFieldId>processingDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers1</srcFieldId>
          <dstFieldId>factoryOthers1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers2</srcFieldId>
          <dstFieldId>factoryOthers2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryOthers3</srcFieldId>
          <dstFieldId>factoryOthers3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>factImage</srcFieldId>
          <dstFieldId>factImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>factAttachment</srcFieldId>
          <dstFieldId>factAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,96">
          <mappingType>Section</mappingType>
          <srcFieldId>productType</srcFieldId>
          <dstFieldId>productType</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,97">
          <mappingType>Section</mappingType>
          <srcFieldId>factShareFile</srcFieldId>
          <dstFieldId>factShareFile</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factVendorDeliver,98">
          <mappingType>Section</mappingType>
          <srcFieldId>capability</srcFieldId>
          <dstFieldId>capability</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="certificationCopy" position="fact_dataMappingRule.xlsx,certificationCopy">
    <DataMappingRule description="Mapping for Copy certification records" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2018-01-29" id="certificationCopy" position="fact_dataMappingRule.xlsx,certificationCopy,1" srcEntityName="Certification" srcEntityVersion="1" status="1" updatedDate="2018-01-29">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,certificationCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,certificationCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>certification</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="productionAreaCopy" position="fact_dataMappingRule.xlsx,productionAreaCopy">
    <DataMappingRule description="Mapping for Copy productionArea records" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2018-01-29" id="productionAreaCopy" position="fact_dataMappingRule.xlsx,productionAreaCopy,1" srcEntityName="ProductionArea" srcEntityVersion="1" status="1" updatedDate="2018-01-29">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,productionAreaCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,productionAreaCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>productionArea</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="processingDetailCopy" position="fact_dataMappingRule.xlsx,processingDetailCopy">
    <DataMappingRule description="Mapping for Copy processingDetail records" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2018-01-29" id="processingDetailCopy" position="fact_dataMappingRule.xlsx,processingDetailCopy,1" srcEntityName="ProcessingDetail" srcEntityVersion="1" status="1" updatedDate="2018-01-29">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,processingDetailCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,processingDetailCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>processingDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factOther1Copy" position="fact_dataMappingRule.xlsx,factOther1Copy">
    <DataMappingRule description="Mapping from fact other copy" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2020-01-03" id="factOther1Copy" position="fact_dataMappingRule.xlsx,factOther1Copy,1" srcEntityName="FactoryOthers1" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factOther1Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther1Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factoryOthers1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther1Copy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.FactoryOthers1SeqNoGenerator</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factOther2Copy" position="fact_dataMappingRule.xlsx,factOther2Copy">
    <DataMappingRule description="Mapping from fact other copy" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2020-01-03" id="factOther2Copy" position="fact_dataMappingRule.xlsx,factOther2Copy,1" srcEntityName="FactoryOthers2" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factOther2Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther2Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factoryOthers2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther2Copy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.FactoryOthers2SeqNoGenerator</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factOther3Copy" position="fact_dataMappingRule.xlsx,factOther3Copy">
    <DataMappingRule description="Mapping from fact other copy" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2020-01-03" id="factOther3Copy" position="fact_dataMappingRule.xlsx,factOther3Copy,1" srcEntityName="FactoryOthers3" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factOther3Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther3Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factoryOthers3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factOther3Copy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.FactoryOthers3SeqNoGenerator</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factSelectCertification" position="fact_dataMappingRule.xlsx,factSelectCertification">
    <DataMappingRule description="Mapping from Codelist to Fact" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2013-12-29" id="factSelectCertification" position="fact_dataMappingRule.xlsx,factSelectCertification,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2013-01-03">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factSelectCertification,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectCertification,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>certification</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectCertification,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>certification.certificationType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factSelectAgreementTemplateItem" position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem">
    <DataMappingRule description="" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2015-09-11" id="factSelectAgreementTemplateItem" position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,1" srcEntityName="AgreementTemplateItem" srcEntityVersion="1" status="1" updatedDate="2015-09-11">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factAgreements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>factAgreements.code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,11">
          <mappingType>Section</mappingType>
          <srcFieldId>type</srcFieldId>
          <dstFieldId>factAgreements.type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,12">
          <mappingType>Section</mappingType>
          <srcFieldId>category</srcFieldId>
          <dstFieldId>factAgreements.category</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="fact_dataMappingRule.xlsx,factSelectAgreementTemplateItem,16">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.FactAgreementsProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="factSelectRelatedFact" position="fact_dataMappingRule.xlsx,factSelectRelatedFact">
    <DataMappingRule description="Fact Select Fact Mapping" domain="/" dstEntityName="Fact" dstEntityVersion="1" effectiveDate="2012-02-20" id="factSelectRelatedFact" position="fact_dataMappingRule.xlsx,factSelectRelatedFact,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="fact_dataMappingRule.xlsx,factSelectRelatedFact,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectRelatedFact,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factories</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="fact_dataMappingRule.xlsx,factSelectRelatedFact,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factories.relatedFactId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
