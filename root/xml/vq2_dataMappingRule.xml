<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="vq2" position="vq2_dataMappingRule.xlsx">
  <sheet id="rfqItemToVq2" position="vq2_dataMappingRule.xlsx,rfqItemToVq2">
    <DataMappingRule description="Mapping from RfqItem To Vq2" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2015-09-04" id="itemToVq2" position="vq2_dataMappingRule.xlsx,rfqItemToVq2,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2015-09-04">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>initialItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>vendorItemDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,11">
          <mappingType>Field</mappingType>
          <srcFieldId>masterCasePack</srcFieldId>
          <dstFieldId>masterCasePack</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,12">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCasePack</srcFieldId>
          <dstFieldId>innerCasePack</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,13">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,14">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,15">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,16">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,17">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,19">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>file</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,20">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteBrand</srcFieldId>
          <dstFieldId>quoteBrand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,21">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>moqUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,22">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>moreImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.fileId != null</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,23">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,24">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>moreImages.file</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,rfqItemToVq2,28">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.RfqItemToVq2PostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="copyFromExistingVq2" position="vq2_dataMappingRule.xlsx,copyFromExistingVq2">
    <DataMappingRule description="Mapping from Copy From Existing Vq2" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2015-09-04" id="copyFromExistingVq2" position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,1" srcEntityName="Vq2" srcEntityVersion="1" status="1" updatedDate="2015-09-04">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>heavyFreight</srcFieldId>
          <dstFieldId>heavyFreight</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,11">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRate</srcFieldId>
          <dstFieldId>freightRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,12">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRate</srcFieldId>
          <dstFieldId>dutyRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,13">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneous</srcFieldId>
          <dstFieldId>miscellaneous</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,14">
          <mappingType>Field</mappingType>
          <srcFieldId>royalty</srcFieldId>
          <dstFieldId>royalty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,15">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFee</srcFieldId>
          <dstFieldId>commissionFee</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,16">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1</srcFieldId>
          <dstFieldId>additionalFee1</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,17">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2</srcFieldId>
          <dstFieldId>additionalFee2</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,18">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3</srcFieldId>
          <dstFieldId>additionalFee3</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,19">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4</srcFieldId>
          <dstFieldId>additionalFee4</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,20">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5</srcFieldId>
          <dstFieldId>additionalFee5</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,21">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRateValue</srcFieldId>
          <dstFieldId>freightRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,22">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRateValue</srcFieldId>
          <dstFieldId>dutyRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,23">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneousValue</srcFieldId>
          <dstFieldId>miscellaneousValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,24">
          <mappingType>Field</mappingType>
          <srcFieldId>royaltyValue</srcFieldId>
          <dstFieldId>royaltyValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,25">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFeeValue</srcFieldId>
          <dstFieldId>commissionFeeValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,26">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1Value</srcFieldId>
          <dstFieldId>additionalFee1Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,27">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2Value</srcFieldId>
          <dstFieldId>additionalFee2Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,28">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3Value</srcFieldId>
          <dstFieldId>additionalFee3Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,29">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4Value</srcFieldId>
          <dstFieldId>additionalFee4Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,30">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5Value</srcFieldId>
          <dstFieldId>additionalFee5Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,31">
          <mappingType>Field</mappingType>
          <srcFieldId>landedCost</srcFieldId>
          <dstFieldId>landedCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,32">
          <mappingType>Field</mappingType>
          <srcFieldId>suggestedRetailPrice</srcFieldId>
          <dstFieldId>suggestedRetailPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,33">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,34">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDescription</srcFieldId>
          <dstFieldId>vendorItemDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,35">
          <mappingType>Field</mappingType>
          <srcFieldId>height</srcFieldId>
          <dstFieldId>height</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,36">
          <mappingType>Field</mappingType>
          <srcFieldId>width</srcFieldId>
          <dstFieldId>width</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,37">
          <mappingType>Field</mappingType>
          <srcFieldId>length</srcFieldId>
          <dstFieldId>length</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,38">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,39">
          <mappingType>Field</mappingType>
          <srcFieldId>masterCasePack</srcFieldId>
          <dstFieldId>masterCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,40">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCasePack</srcFieldId>
          <dstFieldId>innerCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,41">
          <mappingType>Field</mappingType>
          <srcFieldId>masterUPC</srcFieldId>
          <dstFieldId>masterUPC</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,42">
          <mappingType>Field</mappingType>
          <srcFieldId>newFactory</srcFieldId>
          <dstFieldId>newFactory</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,43">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,44">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,45">
          <mappingType>Field</mappingType>
          <srcFieldId>overallRemarks</srcFieldId>
          <dstFieldId>overallRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,46">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,47">
          <mappingType>Field</mappingType>
          <srcFieldId>inStoreDate</srcFieldId>
          <dstFieldId>inStoreDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,48">
          <mappingType>Field</mappingType>
          <srcFieldId>container20Qty</srcFieldId>
          <dstFieldId>container20Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,49">
          <mappingType>Field</mappingType>
          <srcFieldId>container40Qty</srcFieldId>
          <dstFieldId>container40Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,50">
          <mappingType>Field</mappingType>
          <srcFieldId>container45Qty</srcFieldId>
          <dstFieldId>container45Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,51">
          <mappingType>Field</mappingType>
          <srcFieldId>container53Qty</srcFieldId>
          <dstFieldId>container53Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,52">
          <mappingType>Field</mappingType>
          <srcFieldId>otherColors</srcFieldId>
          <dstFieldId>otherColors</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,53">
          <mappingType>Field</mappingType>
          <srcFieldId>otherSizes</srcFieldId>
          <dstFieldId>otherSizes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,54">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeRequired</srcFieldId>
          <dstFieldId>shelfLifeRequired</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,55">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorMinShelfLife</srcFieldId>
          <dstFieldId>vendorMinShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,56">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorTotalShelfLife</srcFieldId>
          <dstFieldId>vendorTotalShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,57">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeComment</srcFieldId>
          <dstFieldId>shelfLifeComment</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,58">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,59">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,60">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,61">
          <mappingType>Section</mappingType>
          <srcFieldId>hts</srcFieldId>
          <dstFieldId>hts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,62">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteBrand</srcFieldId>
          <dstFieldId>quoteBrand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,63">
          <mappingType>Section</mappingType>
          <srcFieldId>license</srcFieldId>
          <dstFieldId>license</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,64">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionalUOM</srcFieldId>
          <dstFieldId>dimensionalUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,65">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,66">
          <mappingType>Section</mappingType>
          <srcFieldId>masterUpcType</srcFieldId>
          <dstFieldId>masterUpcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,67">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,68">
          <mappingType>Section</mappingType>
          <srcFieldId>vqType</srcFieldId>
          <dstFieldId>vqType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,69">
          <mappingType>Section</mappingType>
          <srcFieldId>existingFactory</srcFieldId>
          <dstFieldId>existingFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,70">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerms</srcFieldId>
          <dstFieldId>paymentTerms</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,71">
          <mappingType>Section</mappingType>
          <srcFieldId>moqUOM</srcFieldId>
          <dstFieldId>moqUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,72">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,73">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,74">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,75">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCartons</srcFieldId>
          <dstFieldId>vqCartons</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,76">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Assortments</srcFieldId>
          <dstFieldId>vq2Assortments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,77">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>assortmentKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.Vq2AssortmentKeyGenerator("ASMT","YYMM","6")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,78">
          <mappingType>Section</mappingType>
          <srcFieldId>assortmentType</srcFieldId>
          <dstFieldId>assortmentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,79">
          <mappingType>Section</mappingType>
          <srcFieldId>colors</srcFieldId>
          <dstFieldId>colors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,80">
          <mappingType>Section</mappingType>
          <srcFieldId>sizes</srcFieldId>
          <dstFieldId>sizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,81">
          <mappingType>Section</mappingType>
          <srcFieldId>noOfAssortment</srcFieldId>
          <dstFieldId>noOfAssortment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,82">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Components</srcFieldId>
          <dstFieldId>vq2Components</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,83">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>componentKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.Vq2ComponentKeyGenerator("CM","2")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,84">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,85">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummarys</srcFieldId>
          <dstFieldId>vq2CostSummarys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,86">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,87">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummaryCategorys</srcFieldId>
          <dstFieldId>vq2CostSummaryCategorys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,88">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2OtherCosts</srcFieldId>
          <dstFieldId>vq2OtherCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,89">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,90">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2HTSTradeFlows</srcFieldId>
          <dstFieldId>vq2HTSTradeFlows</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,91">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Process</srcFieldId>
          <dstFieldId>vq2Process</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,92">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Packagings</srcFieldId>
          <dstFieldId>vq2Packagings</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,93">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments</srcFieldId>
          <dstFieldId>vq2Attachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,94">
          <mappingType>Section</mappingType>
          <srcFieldId>productClass</srcFieldId>
          <dstFieldId>productClass</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,95">
          <mappingType>Section</mappingType>
          <srcFieldId>taxClassification</srcFieldId>
          <dstFieldId>taxClassification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,96">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements</srcFieldId>
          <dstFieldId>vq2Requirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,copyFromExistingVq2,100">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CopyFromExistingVq2PostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyDoc" position="vq2_dataMappingRule.xlsx,vq2CopyDoc">
    <DataMappingRule description="Mapping from vq2 copy" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyDoc" position="vq2_dataMappingRule.xlsx,vq2CopyDoc,1" srcEntityName="Vq2" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyDoc,16">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.Vq2CopyDocPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyCarton" position="vq2_dataMappingRule.xlsx,vq2CopyCarton">
    <DataMappingRule description="Mapping for Vq2 Copy Carton" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyCarton" position="vq2_dataMappingRule.xlsx,vq2CopyCarton,1" srcEntityName="Vq2Carton" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyCarton,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyCarton,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqCartons</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyCompliance" position="vq2_dataMappingRule.xlsx,vq2CopyCompliance">
    <DataMappingRule description="Mapping for Vq2 Copy Compliance" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyCompliance" position="vq2_dataMappingRule.xlsx,vq2CopyCompliance,1" srcEntityName="Vq2Compliance" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyCompliance,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyCompliance,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2Compliances</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyComponent" position="vq2_dataMappingRule.xlsx,vq2CopyComponent">
    <DataMappingRule description="Mapping for Vq2 Copy Component" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyComponent" position="vq2_dataMappingRule.xlsx,vq2CopyComponent,1" srcEntityName="Vq2Component" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyComponent,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyComponent,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2Components</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyComponent,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>componentKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.Vq2ComponentKeyGenerator("CM","2")</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyOtherCost" position="vq2_dataMappingRule.xlsx,vq2CopyOtherCost">
    <DataMappingRule description="Mapping for Vq2 Copy Other Cost" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyOtherCost" position="vq2_dataMappingRule.xlsx,vq2CopyOtherCost,1" srcEntityName="Vq2OtherCost" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyOtherCost,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyOtherCost,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2OtherCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyProcess" position="vq2_dataMappingRule.xlsx,vq2CopyProcess">
    <DataMappingRule description="Mapping for Vq2 Copy Process" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyProcess" position="vq2_dataMappingRule.xlsx,vq2CopyProcess,1" srcEntityName="Vq2Process" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyProcess,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyProcess,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2Process</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyPackaging" position="vq2_dataMappingRule.xlsx,vq2CopyPackaging">
    <DataMappingRule description="Mapping for Vq2 Copy Packaging" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyPackaging" position="vq2_dataMappingRule.xlsx,vq2CopyPackaging,1" srcEntityName="Vq2Packaging" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyPackaging,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyPackaging,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2Packagings</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2CopyAttachment" position="vq2_dataMappingRule.xlsx,vq2CopyAttachment">
    <DataMappingRule description="Mapping for Vq2 Copy Attachment" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2CopyAttachment" position="vq2_dataMappingRule.xlsx,vq2CopyAttachment,1" srcEntityName="Vq2Attachment" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2CopyAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2CopyAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vq2Attachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2SelectReqTemplItem" position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem">
    <DataMappingRule description="Mapping from ReqTemplItem to Vq2Requirement" domain="/" dstEntityName="Vq2Requirement" dstEntityVersion="1" effectiveDate="2015-09-04" id="vq2SelectReqTemplItem" position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,1" srcEntityName="ReqTemplItem" srcEntityVersion="1" status="1" updatedDate="2015-09-04">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>dataString</srcFieldId>
          <dstFieldId>dataString</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDecimal</srcFieldId>
          <dstFieldId>dataDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDate</srcFieldId>
          <dstFieldId>dataDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>mandatory</srcFieldId>
          <dstFieldId>mandatory</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>require</srcFieldId>
          <dstFieldId>requireQA</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>notesOrInstructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>dataEntity</srcFieldId>
          <dstFieldId>dataEntity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>unitOfRequirement</srcFieldId>
          <dstFieldId>unitOfRequirement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment</srcFieldId>
          <dstFieldId>attachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,vq2SelectReqTemplItem,23">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.Vq2SelectReqTemplItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2Deliver" position="vq2_dataMappingRule.xlsx,vq2Deliver">
    <DataMappingRule description="Mapping for Vq2 send to Vendor" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-15" id="vq2Deliver" position="vq2_dataMappingRule.xlsx,vq2Deliver,1" srcEntityName="Vq2" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>isForReference,integrationSource,integrationStatus,integrationNote</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>proposedPrice</srcFieldId>
          <dstFieldId>proposedPrice</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>entityVersion</srcFieldId>
          <dstFieldId>entityVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>createdOn</srcFieldId>
          <dstFieldId>createdOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>rfqExpiryDate</srcFieldId>
          <dstFieldId>rfqExpiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>comments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>vqNo</srcFieldId>
          <dstFieldId>vqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>heavyFreight</srcFieldId>
          <dstFieldId>heavyFreight</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRate</srcFieldId>
          <dstFieldId>freightRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRate</srcFieldId>
          <dstFieldId>dutyRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneous</srcFieldId>
          <dstFieldId>miscellaneous</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>royalty</srcFieldId>
          <dstFieldId>royalty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,38">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFee</srcFieldId>
          <dstFieldId>commissionFee</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,39">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1</srcFieldId>
          <dstFieldId>additionalFee1</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,40">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2</srcFieldId>
          <dstFieldId>additionalFee2</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,41">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3</srcFieldId>
          <dstFieldId>additionalFee3</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,42">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4</srcFieldId>
          <dstFieldId>additionalFee4</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,43">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5</srcFieldId>
          <dstFieldId>additionalFee5</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,44">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRateValue</srcFieldId>
          <dstFieldId>freightRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,45">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRateValue</srcFieldId>
          <dstFieldId>dutyRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,46">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneousValue</srcFieldId>
          <dstFieldId>miscellaneousValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,47">
          <mappingType>Field</mappingType>
          <srcFieldId>royaltyValue</srcFieldId>
          <dstFieldId>royaltyValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,48">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFeeValue</srcFieldId>
          <dstFieldId>commissionFeeValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,49">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1Value</srcFieldId>
          <dstFieldId>additionalFee1Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,50">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2Value</srcFieldId>
          <dstFieldId>additionalFee2Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,51">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3Value</srcFieldId>
          <dstFieldId>additionalFee3Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4Value</srcFieldId>
          <dstFieldId>additionalFee4Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5Value</srcFieldId>
          <dstFieldId>additionalFee5Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>landedCost</srcFieldId>
          <dstFieldId>landedCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>suggestedRetailPrice</srcFieldId>
          <dstFieldId>suggestedRetailPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>initialItemNo</srcFieldId>
          <dstFieldId>initialItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDescription</srcFieldId>
          <dstFieldId>vendorItemDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>height</srcFieldId>
          <dstFieldId>height</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>width</srcFieldId>
          <dstFieldId>width</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>length</srcFieldId>
          <dstFieldId>length</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>masterCasePack</srcFieldId>
          <dstFieldId>masterCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCasePack</srcFieldId>
          <dstFieldId>innerCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>masterUPC</srcFieldId>
          <dstFieldId>masterUPC</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId>expiryDate</srcFieldId>
          <dstFieldId>expiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,68">
          <mappingType>Field</mappingType>
          <srcFieldId>newFactory</srcFieldId>
          <dstFieldId>newFactory</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,69">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,70">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,71">
          <mappingType>Field</mappingType>
          <srcFieldId>overallRemarks</srcFieldId>
          <dstFieldId>overallRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,72">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,73">
          <mappingType>Field</mappingType>
          <srcFieldId>inStoreDate</srcFieldId>
          <dstFieldId>inStoreDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,74">
          <mappingType>Field</mappingType>
          <srcFieldId>container20Qty</srcFieldId>
          <dstFieldId>container20Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,75">
          <mappingType>Field</mappingType>
          <srcFieldId>container40Qty</srcFieldId>
          <dstFieldId>container40Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,76">
          <mappingType>Field</mappingType>
          <srcFieldId>container45Qty</srcFieldId>
          <dstFieldId>container45Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,77">
          <mappingType>Field</mappingType>
          <srcFieldId>container53Qty</srcFieldId>
          <dstFieldId>container53Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,78">
          <mappingType>Field</mappingType>
          <srcFieldId>otherColors</srcFieldId>
          <dstFieldId>otherColors</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,79">
          <mappingType>Field</mappingType>
          <srcFieldId>otherSizes</srcFieldId>
          <dstFieldId>otherSizes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,80">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeRequired</srcFieldId>
          <dstFieldId>shelfLifeRequired</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorMinShelfLife</srcFieldId>
          <dstFieldId>vendorMinShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorTotalShelfLife</srcFieldId>
          <dstFieldId>vendorTotalShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeComment</srcFieldId>
          <dstFieldId>shelfLifeComment</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,84">
          <mappingType>Field</mappingType>
          <srcFieldId>componentLatestKey</srcFieldId>
          <dstFieldId>componentLatestKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,85">
          <mappingType>Field</mappingType>
          <srcFieldId>assortmentLatestKey</srcFieldId>
          <dstFieldId>assortmentLatestKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,86">
          <mappingType>Section</mappingType>
          <srcFieldId>qq</srcFieldId>
          <dstFieldId>qq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>rfq</srcFieldId>
          <dstFieldId>rfq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>rfqItem</srcFieldId>
          <dstFieldId>rfqItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>vqType</srcFieldId>
          <dstFieldId>vqType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>file</srcFieldId>
          <dstFieldId>file</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteBrand</srcFieldId>
          <dstFieldId>quoteBrand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,96">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionalUOM</srcFieldId>
          <dstFieldId>dimensionalUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,97">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,98">
          <mappingType>Section</mappingType>
          <srcFieldId>masterUpcType</srcFieldId>
          <dstFieldId>masterUpcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,99">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,100">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerms</srcFieldId>
          <dstFieldId>paymentTerms</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,101">
          <mappingType>Section</mappingType>
          <srcFieldId>moqUOM</srcFieldId>
          <dstFieldId>moqUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,102">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,103">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,104">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,105">
          <mappingType>Section</mappingType>
          <srcFieldId>assortmentType</srcFieldId>
          <dstFieldId>assortmentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,106">
          <mappingType>Section</mappingType>
          <srcFieldId>noOfAssortment</srcFieldId>
          <dstFieldId>noOfAssortment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,107">
          <mappingType>Section</mappingType>
          <srcFieldId>productClass</srcFieldId>
          <dstFieldId>productClass</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,108">
          <mappingType>Section</mappingType>
          <srcFieldId>taxClassification</srcFieldId>
          <dstFieldId>taxClassification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,109">
          <mappingType>Section</mappingType>
          <srcFieldId>existingFactory</srcFieldId>
          <dstFieldId>existingFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,110">
          <mappingType>Section</mappingType>
          <srcFieldId>license</srcFieldId>
          <dstFieldId>license</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,111">
          <mappingType>Section</mappingType>
          <srcFieldId>hts</srcFieldId>
          <dstFieldId>hts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,112">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,113">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,114">
          <mappingType>Section</mappingType>
          <srcFieldId>moreImages</srcFieldId>
          <dstFieldId>moreImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,115">
          <mappingType>Section</mappingType>
          <srcFieldId>existingFactory</srcFieldId>
          <dstFieldId>existingFactory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,116">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCartons</srcFieldId>
          <dstFieldId>vqCartons</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,117">
          <mappingType>Section</mappingType>
          <srcFieldId>colors</srcFieldId>
          <dstFieldId>colors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,118">
          <mappingType>Section</mappingType>
          <srcFieldId>sizes</srcFieldId>
          <dstFieldId>sizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,119">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Assortments</srcFieldId>
          <dstFieldId>vq2Assortments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,120">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Compliances</srcFieldId>
          <dstFieldId>vq2Compliances</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,121">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Components</srcFieldId>
          <dstFieldId>vq2Components</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,122">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummarys</srcFieldId>
          <dstFieldId>vq2CostSummarys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,123">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummaryCategorys</srcFieldId>
          <dstFieldId>vq2CostSummaryCategorys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,124">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2OtherCosts</srcFieldId>
          <dstFieldId>vq2OtherCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,125">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2HTSTradeFlows</srcFieldId>
          <dstFieldId>vq2HTSTradeFlows</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,126">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Process</srcFieldId>
          <dstFieldId>vq2Process</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,127">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Packagings</srcFieldId>
          <dstFieldId>vq2Packagings</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,128">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments</srcFieldId>
          <dstFieldId>vq2Attachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2Deliver,129">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements</srcFieldId>
          <dstFieldId>vq2Requirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2VendorDeliver" position="vq2_dataMappingRule.xlsx,vq2VendorDeliver">
    <DataMappingRule description="Mapping for Vq2 send to Buyer" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-15" id="vq2VendorDeliver" position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,1" srcEntityName="Vq2" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId>proposedPrice</srcFieldId>
          <dstFieldId>proposedPrice</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>isForReference,integrationSource,integrationStatus,integrationNote</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>entityVersion</srcFieldId>
          <dstFieldId>entityVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>createdOn</srcFieldId>
          <dstFieldId>createdOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>rfqExpiryDate</srcFieldId>
          <dstFieldId>rfqExpiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>comments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>vqNo</srcFieldId>
          <dstFieldId>vqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>heavyFreight</srcFieldId>
          <dstFieldId>heavyFreight</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRate</srcFieldId>
          <dstFieldId>freightRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRate</srcFieldId>
          <dstFieldId>dutyRate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneous</srcFieldId>
          <dstFieldId>miscellaneous</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>royalty</srcFieldId>
          <dstFieldId>royalty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,38">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFee</srcFieldId>
          <dstFieldId>commissionFee</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,39">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1</srcFieldId>
          <dstFieldId>additionalFee1</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,40">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2</srcFieldId>
          <dstFieldId>additionalFee2</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,41">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3</srcFieldId>
          <dstFieldId>additionalFee3</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,42">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4</srcFieldId>
          <dstFieldId>additionalFee4</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,43">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5</srcFieldId>
          <dstFieldId>additionalFee5</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,44">
          <mappingType>Field</mappingType>
          <srcFieldId>freightRateValue</srcFieldId>
          <dstFieldId>freightRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,45">
          <mappingType>Field</mappingType>
          <srcFieldId>dutyRateValue</srcFieldId>
          <dstFieldId>dutyRateValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,46">
          <mappingType>Field</mappingType>
          <srcFieldId>miscellaneousValue</srcFieldId>
          <dstFieldId>miscellaneousValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,47">
          <mappingType>Field</mappingType>
          <srcFieldId>royaltyValue</srcFieldId>
          <dstFieldId>royaltyValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,48">
          <mappingType>Field</mappingType>
          <srcFieldId>commissionFeeValue</srcFieldId>
          <dstFieldId>commissionFeeValue</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,49">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee1Value</srcFieldId>
          <dstFieldId>additionalFee1Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,50">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee2Value</srcFieldId>
          <dstFieldId>additionalFee2Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,51">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee3Value</srcFieldId>
          <dstFieldId>additionalFee3Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee4Value</srcFieldId>
          <dstFieldId>additionalFee4Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalFee5Value</srcFieldId>
          <dstFieldId>additionalFee5Value</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>landedCost</srcFieldId>
          <dstFieldId>landedCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>suggestedRetailPrice</srcFieldId>
          <dstFieldId>suggestedRetailPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>initialItemNo</srcFieldId>
          <dstFieldId>initialItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDescription</srcFieldId>
          <dstFieldId>vendorItemDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>height</srcFieldId>
          <dstFieldId>height</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>width</srcFieldId>
          <dstFieldId>width</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>length</srcFieldId>
          <dstFieldId>length</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>masterCasePack</srcFieldId>
          <dstFieldId>masterCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCasePack</srcFieldId>
          <dstFieldId>innerCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>masterUPC</srcFieldId>
          <dstFieldId>masterUPC</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId>expiryDate</srcFieldId>
          <dstFieldId>expiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,68">
          <mappingType>Field</mappingType>
          <srcFieldId>newFactory</srcFieldId>
          <dstFieldId>newFactory</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,69">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,70">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,71">
          <mappingType>Field</mappingType>
          <srcFieldId>overallRemarks</srcFieldId>
          <dstFieldId>overallRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,72">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,73">
          <mappingType>Field</mappingType>
          <srcFieldId>inStoreDate</srcFieldId>
          <dstFieldId>inStoreDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,74">
          <mappingType>Field</mappingType>
          <srcFieldId>container20Qty</srcFieldId>
          <dstFieldId>container20Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,75">
          <mappingType>Field</mappingType>
          <srcFieldId>container40Qty</srcFieldId>
          <dstFieldId>container40Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,76">
          <mappingType>Field</mappingType>
          <srcFieldId>container45Qty</srcFieldId>
          <dstFieldId>container45Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,77">
          <mappingType>Field</mappingType>
          <srcFieldId>container53Qty</srcFieldId>
          <dstFieldId>container53Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,78">
          <mappingType>Field</mappingType>
          <srcFieldId>otherColors</srcFieldId>
          <dstFieldId>otherColors</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,79">
          <mappingType>Field</mappingType>
          <srcFieldId>otherSizes</srcFieldId>
          <dstFieldId>otherSizes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,80">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeRequired</srcFieldId>
          <dstFieldId>shelfLifeRequired</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorMinShelfLife</srcFieldId>
          <dstFieldId>vendorMinShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorTotalShelfLife</srcFieldId>
          <dstFieldId>vendorTotalShelfLife</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>shelfLifeComment</srcFieldId>
          <dstFieldId>shelfLifeComment</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,84">
          <mappingType>Field</mappingType>
          <srcFieldId>externalDocVer</srcFieldId>
          <dstFieldId>externalDocVer</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,85">
          <mappingType>Field</mappingType>
          <srcFieldId>externalDocRefNo</srcFieldId>
          <dstFieldId>externalDocRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,86">
          <mappingType>Field</mappingType>
          <srcFieldId>componentLatestKey</srcFieldId>
          <dstFieldId>componentLatestKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,87">
          <mappingType>Field</mappingType>
          <srcFieldId>assortmentLatestKey</srcFieldId>
          <dstFieldId>assortmentLatestKey</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>qq</srcFieldId>
          <dstFieldId>qq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>rfq</srcFieldId>
          <dstFieldId>rfq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>rfqItem</srcFieldId>
          <dstFieldId>rfqItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>vqType</srcFieldId>
          <dstFieldId>vqType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,96">
          <mappingType>Section</mappingType>
          <srcFieldId>file</srcFieldId>
          <dstFieldId>file</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,97">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteBrand</srcFieldId>
          <dstFieldId>quoteBrand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,98">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionalUOM</srcFieldId>
          <dstFieldId>dimensionalUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,99">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,100">
          <mappingType>Section</mappingType>
          <srcFieldId>masterUpcType</srcFieldId>
          <dstFieldId>masterUpcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,101">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,102">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerms</srcFieldId>
          <dstFieldId>paymentTerms</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,103">
          <mappingType>Section</mappingType>
          <srcFieldId>moqUOM</srcFieldId>
          <dstFieldId>moqUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,104">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,105">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,106">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,107">
          <mappingType>Section</mappingType>
          <srcFieldId>assortmentType</srcFieldId>
          <dstFieldId>assortmentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,108">
          <mappingType>Section</mappingType>
          <srcFieldId>noOfAssortment</srcFieldId>
          <dstFieldId>noOfAssortment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,109">
          <mappingType>Section</mappingType>
          <srcFieldId>productClass</srcFieldId>
          <dstFieldId>productClass</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,110">
          <mappingType>Section</mappingType>
          <srcFieldId>taxClassification</srcFieldId>
          <dstFieldId>taxClassification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,111">
          <mappingType>Section</mappingType>
          <srcFieldId>existingFactory</srcFieldId>
          <dstFieldId>existingFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,112">
          <mappingType>Section</mappingType>
          <srcFieldId>license</srcFieldId>
          <dstFieldId>license</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,113">
          <mappingType>Section</mappingType>
          <srcFieldId>hts</srcFieldId>
          <dstFieldId>hts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,114">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,115">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,116">
          <mappingType>Section</mappingType>
          <srcFieldId>moreImages</srcFieldId>
          <dstFieldId>moreImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,117">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCartons</srcFieldId>
          <dstFieldId>vqCartons</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,118">
          <mappingType>Section</mappingType>
          <srcFieldId>colors</srcFieldId>
          <dstFieldId>colors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,119">
          <mappingType>Section</mappingType>
          <srcFieldId>sizes</srcFieldId>
          <dstFieldId>sizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,120">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Assortments</srcFieldId>
          <dstFieldId>vq2Assortments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,121">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Compliances</srcFieldId>
          <dstFieldId>vq2Compliances</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,122">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Components</srcFieldId>
          <dstFieldId>vq2Components</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,123">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummarys</srcFieldId>
          <dstFieldId>vq2CostSummarys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,124">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2CostSummaryCategorys</srcFieldId>
          <dstFieldId>vq2CostSummaryCategorys</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,125">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2OtherCosts</srcFieldId>
          <dstFieldId>vq2OtherCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,126">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2HTSTradeFlows</srcFieldId>
          <dstFieldId>vq2HTSTradeFlows</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,127">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Process</srcFieldId>
          <dstFieldId>vq2Process</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,128">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Packagings</srcFieldId>
          <dstFieldId>vq2Packagings</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,129">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments</srcFieldId>
          <dstFieldId>vq2Attachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2VendorDeliver,130">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements</srcFieldId>
          <dstFieldId>vq2Requirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vq2AdoptAsNewItem" position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem">
    <DataMappingRule description="Mapping for Vq2 Adop tAs New Item" domain="/" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2012-03-14" id="vq2AdoptAsNewItem" position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,1" srcEntityName="Vq2" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>vq2Ref</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDescription</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>overallRemarks</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>masterCasePack</srcFieldId>
          <dstFieldId>masterCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCasePack</srcFieldId>
          <dstFieldId>innerCasePack</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>masterUPC</srcFieldId>
          <dstFieldId>masterUpc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>masterUpcType</srcFieldId>
          <dstFieldId>upcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionalUOM</srcFieldId>
          <dstFieldId>dimensionalUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>moqUOM</srcFieldId>
          <dstFieldId>defaultUom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteBrand</srcFieldId>
          <dstFieldId>quoteBrand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>file</srcFieldId>
          <dstFieldId>fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>license</srcFieldId>
          <dstFieldId>licenses</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemVendorFact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>itemVendorFact.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemFactory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>existingFactory</srcFieldId>
          <dstFieldId>itemFactory.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>colors</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,31">
          <mappingType>Field</mappingType>
          <srcFieldId>displayValue</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,32">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isPrimary</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>sizes</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,34">
          <mappingType>Field</mappingType>
          <srcFieldId>displayValue</srcFieldId>
          <dstFieldId>sizeDisplayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>moreImages</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,36">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,37">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,38">
          <mappingType>Section</mappingType>
          <srcFieldId>moreImages.file</srcFieldId>
          <dstFieldId>itemImage.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,39">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments</srcFieldId>
          <dstFieldId>itemAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,40">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments.file</srcFieldId>
          <dstFieldId>itemAttachment.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Attachments.attachType</srcFieldId>
          <dstFieldId>itemAttachment.attachTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,43">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements</srcFieldId>
          <dstFieldId>specRequirement</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,44">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,45">
          <mappingType>Field</mappingType>
          <srcFieldId>category</srcFieldId>
          <dstFieldId>category</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,46">
          <mappingType>Field</mappingType>
          <srcFieldId>type</srcFieldId>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,47">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,48">
          <mappingType>Field</mappingType>
          <srcFieldId>details</srcFieldId>
          <dstFieldId>details</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,49">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,50">
          <mappingType>Field</mappingType>
          <srcFieldId>mandatory</srcFieldId>
          <dstFieldId>mandatory</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,51">
          <mappingType>Field</mappingType>
          <srcFieldId>requireQA</srcFieldId>
          <dstFieldId>require</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,52">
          <mappingType>Field</mappingType>
          <srcFieldId>unitOfReqBookName</srcFieldId>
          <dstFieldId>unitOfReqBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,53">
          <mappingType>Field</mappingType>
          <srcFieldId>dataType</srcFieldId>
          <dstFieldId>dataType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,54">
          <mappingType>Field</mappingType>
          <srcFieldId>dataString</srcFieldId>
          <dstFieldId>dataString</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,55">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDecimal</srcFieldId>
          <dstFieldId>dataDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,56">
          <mappingType>Field</mappingType>
          <srcFieldId>dataDate</srcFieldId>
          <dstFieldId>dataDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,57">
          <mappingType>Field</mappingType>
          <srcFieldId>dataEntityBookName</srcFieldId>
          <dstFieldId>dataEntityBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,58">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements.unitOfRequirement</srcFieldId>
          <dstFieldId>specRequirement.unitOfRequirement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,59">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements.image</srcFieldId>
          <dstFieldId>specRequirement.imageId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,60">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements.attachment</srcFieldId>
          <dstFieldId>specRequirement.attachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,61">
          <mappingType>Section</mappingType>
          <srcFieldId>vq2Requirements.dataEntity</srcFieldId>
          <dstFieldId>specRequirement.dataEntity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,vq2AdoptAsNewItem,65">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.Vq2AdoptNewItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vendorToVq2" position="vq2_dataMappingRule.xlsx,vendorToVq2">
    <DataMappingRule description="Mapping for Vendor To Vq2" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2012-03-14" id="vendorToVq2" position="vq2_dataMappingRule.xlsx,vendorToVq2,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>leadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,11">
          <mappingType>Field</mappingType>
          <srcFieldId>avgLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,12">
          <mappingType>Field</mappingType>
          <srcFieldId>avgMinOrderQty</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,14">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,15">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerms</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,16">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,17">
          <mappingType>Section</mappingType>
          <srcFieldId>factId</srcFieldId>
          <dstFieldId>existingFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,vendorToVq2,21">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SendVq2InheritanceVendorProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="rfqToVq2" position="vq2_dataMappingRule.xlsx,rfqToVq2">
    <DataMappingRule description="Mapping from RFQ To Vq2" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2015-09-04" id="rfqToVq2" position="vq2_dataMappingRule.xlsx,rfqToVq2,1" srcEntityName="Rfq" srcEntityVersion="1" status="1" updatedDate="2015-09-04">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,rfqToVq2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqToVq2,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqToVq2,10">
          <mappingType>Field</mappingType>
          <srcFieldId>rfqNo</srcFieldId>
          <dstFieldId>rfqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqToVq2,11">
          <mappingType>Field</mappingType>
          <srcFieldId>expiryDate</srcFieldId>
          <dstFieldId>rfqExpiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,rfqToVq2,12">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="qqRfqItemToVq2" position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2">
    <DataMappingRule description="Mapping from RFQITEM for qq To Vq2" domain="/" dstEntityName="Vq2" dstEntityVersion="1" effectiveDate="2015-09-04" id="qqRfqItemToVq2" position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,1" srcEntityName="RfqItem" srcEntityVersion="1" status="1" updatedDate="2017-03-13">
      <elements id="mappingRule">
        <element position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,9">
          <mappingType>Field</mappingType>
          <srcFieldId>proposedPrice</srcFieldId>
          <dstFieldId>proposedPrice</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfqItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,11">
          <mappingType>Section</mappingType>
          <srcFieldId>qq</srcFieldId>
          <dstFieldId>qq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq2_dataMappingRule.xlsx,qqRfqItemToVq2,15">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.QqRfqItemToVq2Processor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
