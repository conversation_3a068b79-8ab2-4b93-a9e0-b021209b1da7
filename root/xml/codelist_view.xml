<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="codelist" position="codelist_view.xlsx">
  <sheet id="codelistlistView" position="codelist_view.xlsx,codelistlistView">
    <ViewDefinition advancedSearchId="" description="Code List - All Profiles" id="codelistlistView" label="Code Lists" moduleId="codelist" position="codelist_view.xlsx,codelistlistView,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistlistView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,14">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>CodelistBook</value>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,15">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessReference:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistlistView,19">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistlistView,30">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,31">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>typeId:typeName:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,32">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,33">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,34">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,35">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,36">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,37">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>effectiveFrom:effectiveFrom:timestamp</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,38">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>effectiveTo:effectiveTo:timestamp</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,39">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:Integer</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,40">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isLatest:isLatest:boolean</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,41">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,42">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,43">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,44">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,45">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,46">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CCB.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,47">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,codelistlistView,48">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popCodelistView" position="codelist_view.xlsx,popCodelistView">
    <ViewDefinition advancedSearchId="" description="CodeList Lookup" id="popCodelistView" label="Code List Lookup" moduleId="codelist" position="codelist_view.xlsx,popCodelistView,1" queryId="listCodelists" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,popCodelistView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Codelist</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>version:codelistBook.version:integer,entityName:entityName:string,id:id:string,refNo:refNo:string,businessRefNo:codelistBook.businessReference:string,fullLineage:codelistBook.hierarchyFullCode:string,bookName:codelistBook.name:string,bookVersion:codelistBook.version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="codelist_view.xlsx,popCodelistView,22">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>codelist.code</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>code:code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,23">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>155px</width>
          <visibility>1</visibility>
          <mappedField>codelist.name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,24">
          <id>disabled</id>
          <label>Not Available</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>codelist.disabled</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>disabled:disabled:boolean</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,25">
          <id>condition</id>
          <label>Condition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>codelist.condition</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>condition:condition:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,26">
          <id>parentId</id>
          <label>Parent ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.parent_id</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>parentId:codelistBook.id:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,27">
          <id>parentCode</id>
          <label>Parent Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.code</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>parentCode:codelistBook.code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,28">
          <id>seqNo</id>
          <label>SeqNo</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility>0</visibility>
          <mappedField>codelist.seq_no</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>seqNo:seq_no:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,29">
          <id>Codelist</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>codelist</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,30">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistView,31">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment/>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>cb.updated_on</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popCodelistCertificationView" position="codelist_view.xlsx,popCodelistCertificationView">
    <ViewDefinition advancedSearchId="" description="CodeList Lookup" id="popCodelistCertificationView" label="Code List Lookup" moduleId="codelist" position="codelist_view.xlsx,popCodelistCertificationView,1" queryId="listCodelists" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,popCodelistCertificationView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Codelist</value>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>version:codelistBook.version:integer,entityName:entityName:string,id:id:string,refNo:refNo:string,businessRefNo:codelistBook.businessReference:string,fullLineage:codelistBook.hierarchyFullCode:string,bookName:codelistBook.name:string,bookVersion:codelistBook.version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="codelist_view.xlsx,popCodelistCertificationView,22">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.code</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>code:code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,23">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>155px</width>
          <visibility>1</visibility>
          <mappedField>codelist.name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,24">
          <id>disabled</id>
          <label>Not Available</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.disabled</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>disabled:disabled:boolean</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,25">
          <id>parentId</id>
          <label>Parent ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.parent_id</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>parentId:codelistBook.id:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,26">
          <id>parentCode</id>
          <label>Parent Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>codelist.code</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>parentCode:codelistBook.code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,27">
          <id>seqNo</id>
          <label>SeqNo</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility>0</visibility>
          <mappedField>codelist.seq_no</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>seqNo:seq_no:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,28">
          <id>Codelist</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>codelist</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,29">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCodelistCertificationView,30">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment/>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>cb.updated_on</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popVendorTypeView" position="codelist_view.xlsx,popVendorTypeView">
    <ViewDefinition advancedSearchId="" description="VendorType Lookup" id="popVendorTypeView" label="Vendor Type Lookup" moduleId="codelist" position="codelist_view.xlsx,popVendorTypeView,1" queryId="listVendorType" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,popVendorTypeView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="codelist_view.xlsx,popVendorTypeView,20">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CODELIST.code</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,21">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>155px</width>
          <visibility>1</visibility>
          <mappedField>CODELIST.name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,22">
          <id>parentId</id>
          <label>Parent ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CODELIST.parent_id</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,23">
          <id>parentCode</id>
          <label>Parent Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CODELIST.code</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,24">
          <id>seqNo</id>
          <label>SeqNo</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility>0</visibility>
          <mappedField>CODELIST.seq_no</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,25">
          <id>Codelist</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility/>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,popVendorTypeView,26">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment/>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>CB.updated_on</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popCustSectionCodelistView" position="codelist_view.xlsx,popCustSectionCodelistView">
    <ViewDefinition advancedSearchId="" description="HTS CodeList Lookup" id="popCustSectionCodelistView" label="HTS Lookup" moduleId="codelist" position="codelist_view.xlsx,popCustSectionCodelistView,1" queryId="listCustSectionCodelists" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,popCustSectionCodelistView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,12">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>codelistBook.name=codelistBookName=string</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Codelist</value>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>version:codelistBook.version:integer,entityName:entityName:string,id:id:string,refNo:codelistBook.refNo:string,businessRefNo:codelistBook.businessReference:string,bookName:codelistBook.name:string,bookVersion:codelistBook.version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="codelist_view.xlsx,popCustSectionCodelistView,22">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CTC.CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption/>
          <esMapping>code:code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,23">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XL</width>
          <visibility>1</visibility>
          <mappedField>CTC.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,24">
          <id>custMemoText1</id>
          <label>Custom Tariff Description.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XL</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>custMemoText1:customFields.customFieldMemoText1:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popCustSectionCodelistView,25">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popBankKeyView" position="codelist_view.xlsx,popBankKeyView">
    <ViewDefinition advancedSearchId="" description="CodeList Bank Key Lookup" id="popBankKeyView" label="Code List Lookup" moduleId="codelist" position="codelist_view.xlsx,popBankKeyView,1" queryId="listCodelistBankKey" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,popBankKeyView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>0</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,11">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Codelist</value>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>version:codelistBook.version:integer,entityName:entityName:string,id:id:string,refNo:codelistBook.refNo:string,businessRefNo:codelistBook.businessReference:string,fullLineage:codelistBook.hierarchyFullCode:string,bookName:codelistBook.name:string,bookVersion:codelistBook.version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="codelist_view.xlsx,popBankKeyView,22">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>code:code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,23">
          <id>bankKey</id>
          <label>Bank Key</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>bankKey:customFields.customFieldText1:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,24">
          <id>name</id>
          <label>Bank Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XL</width>
          <visibility>1</visibility>
          <mappedField>codelist.name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,25">
          <id>country</id>
          <label>Country/Region</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XL</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>country:customFields.customFieldCodeList1.code:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,26">
          <id>bankAddress</id>
          <label>Bank Address</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>bankAddress:customFields.customFieldText2:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,27">
          <id>bankCode</id>
          <label>Bank Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>bankCode:customFields.customFieldText3:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,28">
          <id>swiftCode</id>
          <label>Bank Code / Swift Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XL</width>
          <visibility>1</visibility>
          <mappedField>CODELIST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>swiftCode:customFields.customFieldText4:string</esMapping>
        </element>
        <element position="codelist_view.xlsx,popBankKeyView,29">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment/>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>cb.updated_on</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus01View" position="codelist_view.xlsx,codelistForCustomStatus01View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 01 View" id="codelistForCustomStatus01View" label="Code Lists - Custom Status 01" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus01View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus01View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus02View" position="codelist_view.xlsx,codelistForCustomStatus02View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 02 View" id="codelistForCustomStatus02View" label="Code Lists - Custom Status 02" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus02View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus02View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus03View" position="codelist_view.xlsx,codelistForCustomStatus03View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 03 View" id="codelistForCustomStatus03View" label="Code Lists - Custom Status 03" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus03View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus03View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus04View" position="codelist_view.xlsx,codelistForCustomStatus04View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 04 View" id="codelistForCustomStatus04View" label="Code Lists - Custom Status 04" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus04View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus04View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus05View" position="codelist_view.xlsx,codelistForCustomStatus05View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 05 View" id="codelistForCustomStatus05View" label="Code Lists - Custom Status 05" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus05View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus05View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus06View" position="codelist_view.xlsx,codelistForCustomStatus06View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 06 View" id="codelistForCustomStatus06View" label="Code Lists - Custom Status 06" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus06View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus06View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus07View" position="codelist_view.xlsx,codelistForCustomStatus07View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 07 View" id="codelistForCustomStatus07View" label="Code Lists - Custom Status 07" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus07View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus07View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus08View" position="codelist_view.xlsx,codelistForCustomStatus08View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 08 View" id="codelistForCustomStatus08View" label="Code Lists - Custom Status 08" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus08View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus08View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus09View" position="codelist_view.xlsx,codelistForCustomStatus09View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 09 View" id="codelistForCustomStatus09View" label="Code Lists - Custom Status 09" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus09View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus09View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="codelistForCustomStatus10View" position="codelist_view.xlsx,codelistForCustomStatus10View">
    <ViewDefinition advancedSearchId="" description="Code Lists Custom Status 10 View" id="codelistForCustomStatus10View" label="Code Lists - Custom Status 10" moduleId="codelist" position="codelist_view.xlsx,codelistForCustomStatus10View,1" queryId="listCodelistModel" searchCriterion="">
      <elements id="options">
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>false</value>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,17">
          <id>openPopupWin</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook&amp;winId=popCodelistBookNewAction</actionParams>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,18">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,19">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,20">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,21">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,22">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;entityName=CodelistBook</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,23">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=codelist&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,28">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,29">
          <id>typeId</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,30">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,31">
          <id>CodelistBook</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,32">
          <id>CodelistBook</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CCB</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,33">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,34">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,35">
          <id>effectiveFrom</id>
          <label>Effective from</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,36">
          <id>effectiveTo</id>
          <label>to</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,37">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>CCB.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,38">
          <id>isLatest</id>
          <label>Latest</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>CCB.IS_LATEST</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,42">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,43">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,44">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CCB.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="codelist_view.xlsx,codelistForCustomStatus10View,45">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CCB.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
