<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="correctiveActionPlans" position="correctiveActionPlans_entity.xlsx">
  <sheet id="_system" position="correctiveActionPlans_entity.xlsx,_system">
    <ProjectInfo client="Base" position="correctiveActionPlans_entity.xlsx,_system,1" project="" release_no="1.00"/>
    <ProductVersion position="correctiveActionPlans_entity.xlsx,_system,7">
      <elements id="default">
        <element position="correctiveActionPlans_entity.xlsx,_system,10">
          <updated_on>14-七月-2015</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="correctiveActionPlans_entity.xlsx,generalInfo">
    <GeneralInfo custom_table_name="CTM_CORRECTIVE_ACTION_PLANS" is_for_external="Y" is_system_entity="N" main_entity="CorrectiveActionPlans" module="correctiveActionPlans" position="correctiveActionPlans_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
    <CustomField position="correctiveActionPlans_entity.xlsx,generalInfo,8">
      <elements id="default">
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,11">
          <custom_field_type>Text</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,12">
          <custom_field_type>MemoText</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,13">
          <custom_field_type>Codelist</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,14">
          <custom_field_type>Number</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,15">
          <custom_field_type>Decimal</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,16">
          <custom_field_type>Date</custom_field_type>
          <count>7</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,17">
          <custom_field_type>Hcl</custom_field_type>
          <count>5</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,18">
          <custom_field_type>Checkbox</custom_field_type>
          <count>10</count>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,generalInfo,19">
          <custom_field_type>Selection</custom_field_type>
          <count>5</count>
        </element>
      </elements>
    </CustomField>
  </sheet>
  <sheet id="entityDef" position="correctiveActionPlans_entity.xlsx,entityDef">
    <Entity name="CorrectiveActionPlans" position="correctiveActionPlans_entity.xlsx,entityDef,1" ref_pattern="${correctiveActionPlansNo}" report_table_name="CORRECTIVE_ACTION_PLANS" table_name="CNT_CORRECTIVE_ACTION_PLANS">
      <elements id="reference">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,8">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ITEM_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,9">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <entity_lookup_type/>
          <snapshot_field>item.fileId</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,10">
          <entity_field_id>itemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.itemNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,11">
          <entity_field_id>itemName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.itemName</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ITEM_NAME</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,12">
          <entity_field_id>itemDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.itemDesc</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ITEM_DESC</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,13">
          <entity_field_id>sourcingRecord</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SourcingRecord.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SOURCING_RECOED_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,14">
          <entity_field_id>sourcingRecordNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>sourcingRecord.sourcingRecordNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SOURCING_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,15">
          <entity_field_id>vpo</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vpo.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,16">
          <entity_field_id>vpoNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vpo.vpoNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VPO_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,20">
          <entity_field_id>correctiveActionPlansNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_CAP_NO","system.pattern.correctiveActionNo", "CAPA#{Date:YYYY}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CORRECTIVE_ACTION_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,21">
          <entity_field_id>shortDesc</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHORT_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,22">
          <entity_field_id>auditor</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>FACTORY_AUDIT_AUDITOR</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>AUDITOR</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,23">
          <entity_field_id>reportChannel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_REPORT_CHANNEL</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>REPORT_CHANNEL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,24">
          <entity_field_id>reportDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REPORT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,25">
          <entity_field_id>dueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DUE_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,26">
          <entity_field_id>actualCompleteDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ACTUAL_COMPLETE_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,27">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,28">
          <entity_field_id>hierarchy</entity_field_id>
          <entity_field_type>hcl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>HIERARCHY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,29">
          <entity_field_id>issueTotalNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ISSUE_TOTAL_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,30">
          <entity_field_id>openIssueNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>OPEN_ISSUE_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,31">
          <entity_field_id>resolvedIssueNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>RESOLVED_ISSUE_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,32">
          <entity_field_id>invalidIssueNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INVALID_ISSUE_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,33">
          <entity_field_id>overallCAPStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>OVERALL_CAP_STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,34">
          <entity_field_id>vendorEmail</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_EMAIL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,35">
          <entity_field_id>shipmentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,36">
          <entity_field_id>vendor</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vendor.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,37">
          <entity_field_id>vendorCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,38">
          <entity_field_id>vendorName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,39">
          <entity_field_id>factory</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Fact.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACTORY_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,40">
          <entity_field_id>capType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>CAP_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,41">
          <entity_field_id>factAudit</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>FactAudit.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACT_AUDIT_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,42">
          <entity_field_id>overallIssueStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>OVERALL_ISSUE_STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,43">
          <entity_field_id>maxRound</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MAX_ROUND</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,47">
          <entity_field_id>shipmentAdvices</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-entity</dataType>
          <data1>ShipmentAdvice</data1>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SHIPMENT_ADVICE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,48">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <transitive_fields/>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,49">
          <entity_field_id>issueActions</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>IssueAction.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>ISSUE_ACTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,50">
          <entity_field_id>correctiveActionPlansOther1</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlansOther1.capId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,51">
          <entity_field_id>correctiveActionPlansOther2</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlansOther2.capId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,52">
          <entity_field_id>correctiveActionPlansOther3</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlansOther3.capId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,53">
          <entity_field_id>correctiveActionPlansImages</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlansImage.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>IMAGES</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,54">
          <entity_field_id>correctiveActionPlansAttachs</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlansAttach.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>ATTACHMENTS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,55">
          <entity_field_id>sectionAttachmentList</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SectionAttachment.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,56">
          <entity_field_id>referenceItem</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-entity</dataType>
          <data1>Item</data1>
          <transitive_fields/>
          <report_column_name>REFERENCEITEMS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Snapshot</entity_lookup_type>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,57">
          <entity_field_id>sourcingAgency</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>SOURCING_AGENT</data1>
          <transitive_fields/>
          <report_column_name>SOURCING_AGENCY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
    <Entity name="IssueAction" position="correctiveActionPlans_entity.xlsx,entityDef,60" ref_pattern="${issueNo}" report_table_name="ISSUE_ACTION" table_name="CNT_ISSUE_ACTION">
      <elements id="reference"/>
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,70">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CORRECTIVE_ACTION_PLANS_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,71">
          <entity_field_id>issueNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_ISSUE_NO","system.pattern.issueNo", "ISS#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ISSUE_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,72">
          <entity_field_id>issueCategory</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_ISSUE_CATEGORY</data1>
          <data2/>
          <report_column_name>ISSUE_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,73">
          <entity_field_id>issueType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_ISSUES_TYPE_DESC</data1>
          <data2/>
          <report_column_name>ISSUE_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,74">
          <entity_field_id>issueDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ISSUE_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,75">
          <entity_field_id>issueDetails</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ISSUE_DETAILS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,76">
          <entity_field_id>vendorComment</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_COMMENT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,77">
          <entity_field_id>severity</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_ISSUES_SEVERITY</data1>
          <data2/>
          <report_column_name>SEVERITY</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,78">
          <entity_field_id>totalAffectedQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_AFFECTED_QTY</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,79">
          <entity_field_id>image1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,80">
          <entity_field_id>image2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,81">
          <entity_field_id>image3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,82">
          <entity_field_id>attachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,83">
          <entity_field_id>attachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,84">
          <entity_field_id>attachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,85">
          <entity_field_id>overallActionsStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OVERALL_ACTIONS_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,86">
          <entity_field_id>issueStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAP_ISSUES_ISSUE_STATUS</data1>
          <data2/>
          <report_column_name>ISSUE_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,87">
          <entity_field_id>remarks</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REMARKS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,88">
          <entity_field_id>isSample</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_SAMPLE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,89">
          <entity_field_id>isPPMeeting</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_PP_MEETING</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,90">
          <entity_field_id>isInlineInspection</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_INLINE_INSPECTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,91">
          <entity_field_id>isFinalInspection</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_FINAL_INSPECTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,92">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,93">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,94">
          <entity_field_id>overallCapaStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>OVERALL_CAPA_STATUS</data1>
          <data2/>
          <report_column_name>OVERALL_CAPA_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,95">
          <entity_field_id>evaluation</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>EVALUATION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,96">
          <entity_field_id>latestCapaStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAPA_ACTION_STATUS</data1>
          <data2/>
          <report_column_name>LATEST_CAPA_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,100">
          <entity_field_id>correctiveActions</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveAction.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>CORRECTIVE_ACTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,101">
          <entity_field_id>issueFiles</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>TableAttachment.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>ISSUE_FILES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveAction" position="correctiveActionPlans_entity.xlsx,entityDef,105" ref_pattern="" report_table_name="CORRECTIVE_ACTION" table_name="CNT_CORRECTIVE_ACTION">
      <elements id="reference"/>
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,115">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ISSUE_ACTION_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,116">
          <entity_field_id>possibleCause</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>POSSIBLE_CAUSE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,117">
          <entity_field_id>proposedAction</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PROPOSED_ACTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,118">
          <entity_field_id>vendorAction</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ACTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,119">
          <entity_field_id>expectedStartingDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>EXPECTED_STARTING_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,120">
          <entity_field_id>expectedActionDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>EXPECTED_ACTION_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,121">
          <entity_field_id>actualActionDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ACTUAL_ACTION_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,122">
          <entity_field_id>longTermCAPA</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>LONG_TERM_CAPA</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,123">
          <entity_field_id>isVerified</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_VERIFIED</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,124">
          <entity_field_id>image1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,125">
          <entity_field_id>image2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,126">
          <entity_field_id>image3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,127">
          <entity_field_id>attachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,128">
          <entity_field_id>attachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,129">
          <entity_field_id>attachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,130">
          <entity_field_id>energySaving</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>CorrectiveActionPlanEnergySaving.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ENERGY_SAVING</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,131">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,132">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,133">
          <entity_field_id>capaStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CAPA_ACTION_STATUS</data1>
          <data2/>
          <report_column_name>CAPA_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,134">
          <entity_field_id>round</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ROUND</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,135">
          <entity_field_id>preventiveAction</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PREVENTIVE_ACTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,136">
          <entity_field_id>responsiblePerson</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RESPONSIBLE_PERSON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,137">
          <entity_field_id>reviewComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REVIEW_COMMENTS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,141">
          <entity_field_id>tableAttachments</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>TableAttachment.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name>CAPA_FILES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlansImage" position="correctiveActionPlans_entity.xlsx,entityDef,145" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_IMAGE" table_name="CNT_CAP_IMAGE">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,152">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CORRECTIVE_ACTION_PLANS_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,153">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,154">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,155">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,156">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,160">
          <entity_field_id>imageTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>IMAGE_TYPE</data1>
          <transitive_fields/>
          <report_column_name>N/A</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlansAttach" position="correctiveActionPlans_entity.xlsx,entityDef,163" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_ATTACH" table_name="CNT_CAP_ATTACHMENT">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,170">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CORRECTIVE_ACTION_PLANS_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,171">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,172">
          <entity_field_id>attachment</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,173">
          <entity_field_id>fileAddress</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,174">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,175">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,179">
          <entity_field_id>attachmentTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>ATTACHMENT_TYPE</data1>
          <transitive_fields/>
          <report_column_name>N/A</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlansOther1" position="correctiveActionPlans_entity.xlsx,entityDef,182" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_OTHER1" table_name="CNT_CAP_OTHER1">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,189">
          <entity_field_id>capId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,190">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,191">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlansOther2" position="correctiveActionPlans_entity.xlsx,entityDef,195" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_OTHER2" table_name="CNT_CAP_OTHER2">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,202">
          <entity_field_id>capId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,203">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,204">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlansOther3" position="correctiveActionPlans_entity.xlsx,entityDef,208" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_OTHER3" table_name="CNT_CAP_OTHER3">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,215">
          <entity_field_id>capId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,216">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,217">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlanEnergySaving" position="correctiveActionPlans_entity.xlsx,entityDef,220" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_ENERGY_SAVING" table_name="CNT_CAP_ENERGY_SAVING">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,227">
          <entity_field_id>otherAggregatedSavings</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>OTHER_AGGREGATED_SAVINGS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,228">
          <entity_field_id>totalEnergySavings</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_ENERGY_SAVINGS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,229">
          <entity_field_id>totalEmissionsSavings</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_EMISSIONS_SAVINGS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,230">
          <entity_field_id>baselineEmissionsReduction</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>BASELINE_EMISSIONS_REDUCTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,231">
          <entity_field_id>economicBenefit</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ECONOMIC_BENEFIT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,232">
          <entity_field_id>investment</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INVESTMENT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,233">
          <entity_field_id>payback</entity_field_id>
          <entity_field_type>decimal-percentage</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PAYBACK</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,237">
          <entity_field_id>energySource</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>CorrectiveActionPlanEnergySource.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
      </elements>
    </Entity>
    <Entity name="CorrectiveActionPlanEnergySource" position="correctiveActionPlans_entity.xlsx,entityDef,240" ref_pattern="" report_table_name="CORRECTIVE_ACTION_PLAN_ENERGY_SOURCE" table_name="CNT_CAP_ENERGY_SOURCE">
      <elements id="header">
        <element position="correctiveActionPlans_entity.xlsx,entityDef,247">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PARENT_ID</report_column_name>
          <tracking_level/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,248">
          <entity_field_id>carbonReductionType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CARBON_REDUCTION_TYPE</data1>
          <data2/>
          <report_column_name>CARBON_REDUCTION_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,249">
          <entity_field_id>energyUnit</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ENERGY_UNIT</data1>
          <data2/>
          <report_column_name>ENERGY_UNIT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,250">
          <entity_field_id>energySource</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ENERGY_SOURCE</data1>
          <data2/>
          <report_column_name>ENERGY_SOURCE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,251">
          <entity_field_id>refrigerantType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>REFRIGERANT_GASES</data1>
          <data2/>
          <report_column_name>REFRIGERANT_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,252">
          <entity_field_id>energySavingsOfYear</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ENERGY_SAVINGS_OF_YEAR</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,253">
          <entity_field_id>energySavingsOfMJ</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ENERGY_SAVINGS_OF_MJ</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,entityDef,254">
          <entity_field_id>emissionsSavings</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>EMISSIONS_SAVINGS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="correctiveActionPlans_entity.xlsx,status">
    <Status position="correctiveActionPlans_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="correctiveActionPlans_entity.xlsx,status,4">
          <code>customStatus01</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,5">
          <code>customStatus02</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,6">
          <code>customStatus03</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,7">
          <code>customStatus04</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,8">
          <code>customStatus05</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,9">
          <code>customStatus06</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,10">
          <code>customStatus07</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,11">
          <code>customStatus08</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,12">
          <code>customStatus09</code>
        </element>
        <element position="correctiveActionPlans_entity.xlsx,status,13">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
