<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="testProtocolTemplate" position="testProtocolTemplate_dataMappingRule.xlsx">
  <sheet id="testProtocolTemplateCopyDoc" position="testProtocolTemplate_dataMappingRule.xlsx,testProtocolTemplateCopyDoc">
    <DataMappingRule description="Mapping for Copy TestProtocolTemplate" domain="/" dstEntityName="TestProtocolTemplate" dstEntityVersion="1" effectiveDate="2012-03-15" id="testProtocolTemplateCopyDoc" position="testProtocolTemplate_dataMappingRule.xlsx,testProtocolTemplateCopyDoc,1" srcEntityName="TestProtocolTemplate" srcEntityVersion="1" status="1" updatedDate="2012-03-22">
      <elements id="mappingRule">
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testProtocolTemplateCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testProtocolTemplateCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testProtocolTemplateCopyDoc,13">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.TestProtocolTemplateCopyDocPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="testReportSelectTPT" position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT">
    <DataMappingRule description="Mapping for TestProtocolTemplateDetail to TestReport" domain="/" dstEntityName="TestReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="testReportSelectTPT" position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,1" srcEntityName="TestProtocolTemplate" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,9">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail</srcFieldId>
          <dstFieldId>testReportDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,10">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod</srcFieldId>
          <dstFieldId>testReportDetails.testMethodId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,11">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.isMandatory</srcFieldId>
          <dstFieldId>testReportDetails.isMandatory</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,12">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testType</srcFieldId>
          <dstFieldId>testReportDetails.testType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,13">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testName</srcFieldId>
          <dstFieldId>testReportDetails.testName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,14">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testMethod</srcFieldId>
          <dstFieldId>testReportDetails.testMethod</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,15">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testRequirements</srcFieldId>
          <dstFieldId>testReportDetails.testRequirements</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,16">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testNo</srcFieldId>
          <dstFieldId>testReportDetails.testNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,17">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.description</srcFieldId>
          <dstFieldId>testReportDetails.testDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,18">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testResultType</srcFieldId>
          <dstFieldId>testReportDetails.resultType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,19">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.targetTestValue</srcFieldId>
          <dstFieldId>testReportDetails.targetValue</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,20">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.minimumTolerance</srcFieldId>
          <dstFieldId>testReportDetails.minTolerance</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,21">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.maximumTolerance</srcFieldId>
          <dstFieldId>testReportDetails.maxTolerance</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,22">
          <mappingType>Section</mappingType>
          <srcFieldId>testProtocolTemplateDetail.testMethod.testResultUnit</srcFieldId>
          <dstFieldId>testReportDetails.testResultUnit</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="testProtocolTemplate_dataMappingRule.xlsx,testReportSelectTPT,26">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.TestReportSelectTemplatePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
