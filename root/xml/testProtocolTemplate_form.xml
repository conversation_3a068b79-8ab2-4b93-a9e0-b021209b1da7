<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form module="testProtocolTemplate" position="testProtocolTemplate_form.xlsx">
  <sheet id="system" position="testProtocolTemplate_form.xlsx,system">
    <ProjectInfo client="cnt" position="testProtocolTemplate_form.xlsx,system,1" project="base" releaseNo="1.0a"/>
    <ProductVersion position="testProtocolTemplate_form.xlsx,system,7">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,system,10">
          <updatedOn>16-七月-2015</updatedOn>
          <summary>Creation</summary>
          <releaseNo>01-一月-1900</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="form" position="testProtocolTemplate_form.xlsx,form">
    <Form id="testProtocolTemplateForm" label="Test Template" module="testProtocolTemplate" position="testProtocolTemplate_form.xlsx,form,1" version="1"/>
    <TabGroup id="testProtocolTemplateTabGroup" label="" position="testProtocolTemplate_form.xlsx,form,7">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,14">
          <id>tabHeader</id>
          <label/>
          <type>Tab</type>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,15">
          <id>testProtocolTemplateDetail</id>
          <label/>
          <type>Tab</type>
        </element>
      </elements>
    </TabGroup>
    <Toolbar position="testProtocolTemplate_form.xlsx,form,18">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,21">
          <id>testProtocolTemplateMenubar</id>
          <label/>
          <type>Menubar</type>
        </element>
      </elements>
    </Toolbar>
    <Menubar align="left" cssClass="" id="testProtocolTemplateMenubar" label="" position="testProtocolTemplate_form.xlsx,form,24">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,31">
          <id>newGroup</id>
          <label>Create</label>
          <type>MenuGroup</type>
          <action>newGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,32">
          <id>editDoc</id>
          <label>Edit</label>
          <type>MenuItem</type>
          <action>EditDocAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,33">
          <id>amendDoc</id>
          <label>Amend</label>
          <type>MenuItem</type>
          <action>AmendDocAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,34">
          <id>baseSaveDoc</id>
          <label>Save</label>
          <type>MenuItem</type>
          <action>BaseSaveDocAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,35">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>MenuItem</type>
          <action>SaveAndConfirmAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,36">
          <id>discardDoc</id>
          <label>Cancel</label>
          <type>MenuItem</type>
          <action>DiscardDocAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,37">
          <id>printGroup</id>
          <label>Print</label>
          <type>MenuGroup</type>
          <action>printGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,38">
          <id>exportGroup</id>
          <label>Export</label>
          <type>MenuGroup</type>
          <action>exportGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,39">
          <id>actionsGroup</id>
          <label>Actions</label>
          <type>MenuGroup</type>
          <action>actionsGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>MenuGroup</type>
          <action>moreGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,41">
          <id>markAsGroup</id>
          <label>Mark as</label>
          <type>MenuGroup</type>
          <action>markAsGroup</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,42">
          <id>initializeCpm</id>
          <label>Initialize CPM</label>
          <type>MenuItem</type>
          <action>InitializeCpmAction</action>
          <actionParams/>
        </element>
      </elements>
    </Menubar>
    <Linkbar align="right" cssClass="" id="testProtocolTemplateLinkbar" label="" position="testProtocolTemplate_form.xlsx,form,45">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,52">
          <id>duplicateWindow</id>
          <label>Duplicate Window</label>
          <action/>
          <actionParams/>
          <rendererClass/>
          <image>duplicateWindow.png</image>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,53">
          <id>followDoc</id>
          <label>Follow</label>
          <action>FollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>follow.png</image>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,54">
          <id>unfollowDoc</id>
          <label>Unfollow</label>
          <action>UnfollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>unfollow.png</image>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,55">
          <id>addToFavorites</id>
          <label>Add to Favorites</label>
          <action>AddDocToFavoriteAction</action>
          <actionParams/>
          <rendererClass/>
          <image>favorites.png</image>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,56">
          <id>approval</id>
          <label>Approval</label>
          <action>OpenApprovalByDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>approval.gif</image>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,57">
          <id>relatedActivities</id>
          <label>Related Activities</label>
          <action>ShowRelatedDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>activities.png</image>
        </element>
      </elements>
    </Linkbar>
    <Header position="testProtocolTemplate_form.xlsx,form,60">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,63">
          <id>docStatus</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>inactive:(inactive),active:,canceled:(canceled)</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,64">
          <id>version</id>
          <label>Version</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format>{version}({editingStatus})</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,65">
          <id>headerIntegration</id>
          <label/>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,66">
          <id>testProtocolTemplateLinkbar</id>
          <label/>
          <type>Linkbar</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
      </elements>
    </Header>
    <DropdownStores position="testProtocolTemplate_form.xlsx,form,69">
      <elements id="default"/>
    </DropdownStores>
    <MenuGroup id="newGroup" label="Create" position="testProtocolTemplate_form.xlsx,form,74">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,81">
          <id>newDoc</id>
          <label>New Test Protocol Template</label>
          <type>MenuItem</type>
          <action>NewDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="actionsGroup" label="Actions" position="testProtocolTemplate_form.xlsx,form,84">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,91">
          <id>copyDoc</id>
          <label>Copy</label>
          <type>MenuItem</type>
          <action>CopyDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,92">
          <id>activateDoc</id>
          <label>Active</label>
          <type>MenuItem</type>
          <action>ActivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,93">
          <id>deactivateDoc</id>
          <label>Inactive</label>
          <type>MenuItem</type>
          <action>DeactivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,94">
          <id>cancelDoc</id>
          <label>Canceled</label>
          <type>MenuItem</type>
          <action>CancelDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="markAsGroup" label="Mark as" position="testProtocolTemplate_form.xlsx,form,97">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,104">
          <id>markAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus01DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,105">
          <id>markAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus02DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,106">
          <id>markAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus03DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,107">
          <id>markAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus04DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,108">
          <id>markAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus05DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,109">
          <id>markAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus06DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,110">
          <id>markAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus07DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,111">
          <id>markAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus08DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,112">
          <id>markAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus09DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,113">
          <id>markAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus10DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="moreGroup" label="More" position="testProtocolTemplate_form.xlsx,form,116">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,123">
          <id>customDocAction01</id>
          <label>Custom Action 1</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,124">
          <id>customDocAction02</id>
          <label>Custom Action 2</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,125">
          <id>customDocAction03</id>
          <label>Custom Action 3</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,126">
          <id>customDocAction04</id>
          <label>Custom Action 4</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,127">
          <id>customDocAction05</id>
          <label>Custom Action 5</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,128">
          <id>customDocAction06</id>
          <label>Custom Action 6</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,129">
          <id>customDocAction07</id>
          <label>Custom Action 7</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,130">
          <id>customDocAction08</id>
          <label>Custom Action 8</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,131">
          <id>customDocAction09</id>
          <label>Custom Action 9</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,132">
          <id>customDocAction10</id>
          <label>Custom Action 10</label>
          <type>MenuItem</type>
          <action>TestTemplateCustom10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="printGroup" label="Print" position="testProtocolTemplate_form.xlsx,form,135">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,142">
          <id>customPrint01</id>
          <label>Custom Print 01</label>
          <type>MenuItem</type>
          <action>CustomPrint01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,143">
          <id>customPrint02</id>
          <label>Custom Print 02</label>
          <type>MenuItem</type>
          <action>CustomPrint02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,144">
          <id>customPrint03</id>
          <label>Custom Print 03</label>
          <type>MenuItem</type>
          <action>CustomPrint03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,145">
          <id>customPrint04</id>
          <label>Custom Print 04</label>
          <type>MenuItem</type>
          <action>CustomPrint04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,146">
          <id>customPrint05</id>
          <label>Custom Print 05</label>
          <type>MenuItem</type>
          <action>CustomPrint05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,147">
          <id>customPrint06</id>
          <label>Custom Print 06</label>
          <type>MenuItem</type>
          <action>CustomPrint06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,148">
          <id>customPrint07</id>
          <label>Custom Print 07</label>
          <type>MenuItem</type>
          <action>CustomPrint07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,149">
          <id>customPrint08</id>
          <label>Custom Print 08</label>
          <type>MenuItem</type>
          <action>CustomPrint08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,150">
          <id>customPrint09</id>
          <label>Custom Print 09</label>
          <type>MenuItem</type>
          <action>CustomPrint09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,151">
          <id>customPrint10</id>
          <label>Custom Print 10</label>
          <type>MenuItem</type>
          <action>CustomPrint10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="exportGroup" label="Export" position="testProtocolTemplate_form.xlsx,form,154">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,form,161">
          <id>customExport01</id>
          <label>Custom Export 01</label>
          <type>MenuItem</type>
          <action>CustomExport01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,162">
          <id>customExport02</id>
          <label>Custom Export 02</label>
          <type>MenuItem</type>
          <action>CustomExport02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,163">
          <id>customExport03</id>
          <label>Custom Export 03</label>
          <type>MenuItem</type>
          <action>CustomExport03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,164">
          <id>customExport04</id>
          <label>Custom Export 04</label>
          <type>MenuItem</type>
          <action>CustomExport04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,165">
          <id>customExport05</id>
          <label>Custom Export 05</label>
          <type>MenuItem</type>
          <action>CustomExport05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,166">
          <id>customExport06</id>
          <label>Custom Export 06</label>
          <type>MenuItem</type>
          <action>CustomExport06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,167">
          <id>customExport07</id>
          <label>Custom Export 07</label>
          <type>MenuItem</type>
          <action>CustomExport07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,168">
          <id>customExport08</id>
          <label>Custom Export 08</label>
          <type>MenuItem</type>
          <action>CustomExport08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,169">
          <id>customExport09</id>
          <label>Custom Export 09</label>
          <type>MenuItem</type>
          <action>CustomExport09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,form,170">
          <id>customExport10</id>
          <label>Custom Export 10</label>
          <type>MenuItem</type>
          <action>CustomExport10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
  </sheet>
  <sheet id="tabHeader" position="testProtocolTemplate_form.xlsx,tabHeader">
    <Tab id="tabHeader" label="Header" position="testProtocolTemplate_form.xlsx,tabHeader,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,tabHeader,8">
          <id>generalInfoSection</id>
          <label>General Information</label>
          <type>Section</type>
          <type>Section</type>
        </element>
      </elements>
    </Tab>
    <Section id="generalInfoSection" label="General Information" position="testProtocolTemplate_form.xlsx,tabHeader,11" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="testProtocolTemplate_form.xlsx,tabHeader,21">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <action/>
          <size>M</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,tabHeader,22">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <action/>
          <size>L</size>
          <format/>
          <readonlyFormat/>
          <readonly/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
        </element>
      </elements>
    </Section>
  </sheet>
  <sheet id="testProtocolTemplateDetail" position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail">
    <Tab id="testProtocolTemplateDetail" label="Test Details" position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,1" ratio="1">
      <elements id="default">
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,8">
          <id>testProtocolTemplateDetails</id>
          <label>Test Details</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="TestProtocolTemplateDetail" frozenColumns="2" id="testProtocolTemplateDetails" label="Test Details" position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,11" ratio="100%" rowRenderer="" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,18">
          <id>createTestMethod</id>
          <label>Add Test...</label>
          <action>createTestMethod</action>
          <actionParams>entityName=TestMethod</actionParams>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,19">
          <id>selectTestActions</id>
          <label>Select...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupTestTemplateDetail</actionParams>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,20">
          <id>copyTestActions</id>
          <label>Copy</label>
          <action>TestCopyAction</action>
          <actionParams/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,21">
          <id>deleteTestActions</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,25">
          <id>testType</id>
          <label>Test Type</label>
          <type>Label</type>
          <readonly>TRUE</readonly>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.testType</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,26">
          <id>testName</id>
          <label>Test Name</label>
          <type>Text</type>
          <readonly>TRUE</readonly>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.testName</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,27">
          <id>testMethod</id>
          <label>Test Method</label>
          <type>Text</type>
          <readonly>TRUE</readonly>
          <size>L</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.testMethod</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,28">
          <id>isMandatory</id>
          <label>Mandatory</label>
          <type>CheckBox</type>
          <readonly/>
          <size>S</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping/>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,29">
          <id>testResultType</id>
          <label>Test Result Type</label>
          <type>Dropdown</type>
          <readonly/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <extraParams/>
          <format>{name}</format>
          <mapping/>
          <readonlyFormat>{name}</readonlyFormat>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,30">
          <id>targetTestValue</id>
          <label>Target Test Value</label>
          <type>Decimal</type>
          <readonly/>
          <size>S</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping/>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,31">
          <id>minimumTolerance</id>
          <label>Minimum Tolerance</label>
          <type>Decimal</type>
          <readonly/>
          <size>S</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping/>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,32">
          <id>maxiumumTolerance</id>
          <label>Maximum Tolerance</label>
          <type>Decimal</type>
          <readonly/>
          <size>S</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping/>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,33">
          <id>testResultUnit</id>
          <label>Test Result Unit</label>
          <type>Dropdown</type>
          <readonly/>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <extraParams/>
          <format>{name}</format>
          <mapping/>
          <readonlyFormat>{name}</readonlyFormat>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,34">
          <id>isDefault</id>
          <label>Default</label>
          <type>CheckBox</type>
          <readonly/>
          <size>S</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping/>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,35">
          <id>testRequirements</id>
          <label>Test Requirements</label>
          <type>TextArea</type>
          <readonly>TRUE</readonly>
          <size>L</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.testRequirements</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,36">
          <id>testNo</id>
          <label>Test No.</label>
          <type>Text</type>
          <readonly>TRUE</readonly>
          <size>M</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.testNo</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
        <element position="testProtocolTemplate_form.xlsx,testProtocolTemplateDetail,37">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <readonly>TRUE</readonly>
          <size>L</size>
          <sorting>TRUE</sorting>
          <action/>
          <actionParams/>
          <extraParams/>
          <format/>
          <mapping>testProtocol.description</mapping>
          <readonlyFormat/>
          <popupFormat/>
          <rendererClass/>
          <hideLabel/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
</form>
