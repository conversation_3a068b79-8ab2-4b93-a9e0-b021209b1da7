<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="testProtocolTemplate" position="testProtocolTemplate_view_security.xlsx">
  <sheet id="generalInfo" position="testProtocolTemplate_view_security.xlsx,generalInfo">
    <GeneralInfo position="testProtocolTemplate_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="testProtocolTemplate_view_security.xlsx,condition">
    <ConditionList position="testProtocolTemplate_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="testProtocolTemplate_view_security.xlsx,default">
    <ActionConditionMatrix position="testProtocolTemplate_view_security.xlsx,default,1">
      <elements id="default">
        <element position="testProtocolTemplate_view_security.xlsx,default,4">
          <actionId>searchNewDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,5">
          <actionId>searchViewAllExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,6">
          <actionId>searchViewCurExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,7">
          <actionId>importRawData</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,8">
          <actionId>searchActivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,9">
          <actionId>searchDeactivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,10">
          <actionId>searchCancelDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,11">
          <actionId>adminModuleDownload</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,12">
          <actionId>adminModuleUpload</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,13">
          <actionId>batchUpdateField</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,14">
          <actionId>openBatchUpdateWin</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,15">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,16">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,17">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,18">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,19">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,20">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,21">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,22">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,23">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,24">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,25">
          <actionId>searchCustomAction01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,26">
          <actionId>searchCustomAction02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,27">
          <actionId>searchCustomAction03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,28">
          <actionId>searchCustomAction04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,29">
          <actionId>searchCustomAction05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,30">
          <actionId>searchCustomAction06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,31">
          <actionId>searchCustomAction07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,32">
          <actionId>searchCustomAction08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,33">
          <actionId>searchCustomAction09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,34">
          <actionId>searchCustomAction10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,default,35">
          <actionId>openCpmMode</actionId>
          <ANY>allowed</ANY>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="testProtocolTemplate_view_security.xlsx,default,38">
      <elements id="default"/>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="testProtocolTemplate_view_security.xlsx,acl">
    <ActionRule position="testProtocolTemplate_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="testProtocolTemplate_view_security.xlsx,acl,4">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,5">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,6">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,7">
          <actionId>importRawData</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,8">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,9">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,10">
          <actionId>searchCancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,11">
          <actionId>adminModuleDownload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,12">
          <actionId>adminModuleUpload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,13">
          <actionId>batchUpdateField</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,14">
          <actionId>openBatchUpdateWin</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,15">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,16">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,17">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,18">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,19">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,20">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,21">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,22">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,23">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,24">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,25">
          <actionId>searchCustomAction01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,26">
          <actionId>searchCustomAction02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,27">
          <actionId>searchCustomAction03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,28">
          <actionId>searchCustomAction04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,29">
          <actionId>searchCustomAction05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,30">
          <actionId>searchCustomAction06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,31">
          <actionId>searchCustomAction07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,32">
          <actionId>searchCustomAction08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,33">
          <actionId>searchCustomAction09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,34">
          <actionId>searchCustomAction10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="testProtocolTemplate_view_security.xlsx,acl,35">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <testProtocolTemplate.Author>not-has</testProtocolTemplate.Author>
          <testProtocolTemplate.Editor>not-has</testProtocolTemplate.Editor>
          <testProtocolTemplate.ReadOnly>not-has</testProtocolTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="testProtocolTemplate_view_security.xlsx,acl,38">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="testProtocolTemplate_view_security.xlsx,acl,43">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
