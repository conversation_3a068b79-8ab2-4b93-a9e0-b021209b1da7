<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="sampleTracker" position="sampleTracker_entity.xlsx">
  <sheet id="_system" position="sampleTracker_entity.xlsx,_system">
    <ProjectInfo client="Base" position="sampleTracker_entity.xlsx,_system,1" project="" release_no="1.00"/>
    <ProductVersion position="sampleTracker_entity.xlsx,_system,7">
      <elements id="default">
        <element position="sampleTracker_entity.xlsx,_system,10">
          <updated_on>16-十月-2014</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="sampleTracker_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="N" main_entity="SampleTracker" module="sampleTracker" position="sampleTracker_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
    <CustomField position="sampleTracker_entity.xlsx,generalInfo,8">
      <elements id="default">
        <element position="sampleTracker_entity.xlsx,generalInfo,11">
          <custom_field_type>Text</custom_field_type>
          <count>30</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,12">
          <custom_field_type>MemoText</custom_field_type>
          <count>20</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,13">
          <custom_field_type>Codelist</custom_field_type>
          <count>20</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,14">
          <custom_field_type>Number</custom_field_type>
          <count>20</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,15">
          <custom_field_type>Decimal</custom_field_type>
          <count>30</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,16">
          <custom_field_type>Date</custom_field_type>
          <count>20</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,17">
          <custom_field_type>Hcl</custom_field_type>
          <count>5</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,18">
          <custom_field_type>Checkbox</custom_field_type>
          <count>10</count>
        </element>
        <element position="sampleTracker_entity.xlsx,generalInfo,19">
          <custom_field_type>Selection</custom_field_type>
          <count>5</count>
        </element>
      </elements>
    </CustomField>
  </sheet>
  <sheet id="entityDef" position="sampleTracker_entity.xlsx,entityDef">
    <Entity name="SampleTracker" position="sampleTracker_entity.xlsx,entityDef,1" ref_pattern="${trackerNo}" report_table_name="SAMPLE_TRACKER" table_name="CNT_SAMPLE_TRACKER">
      <elements id="reference">
        <element position="sampleTracker_entity.xlsx,entityDef,8">
          <entity_field_id>sampleRequest</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleRequest.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_REQUEST_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,9">
          <entity_field_id>sampleRequestNo</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>sampleRequest.sampleRequestNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_REQUEST_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,10">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>itemNo,itemName,shortDesc,itemDesc,itemType,version,itemBrand,defaultUom,fileId</transitive_fields>
          <report_column_name>ITEM_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,11">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <entity_lookup_type/>
          <snapshot_field>item.fileId</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,12">
          <entity_field_id>reviewUser</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REVIEW_USER_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,13">
          <entity_field_id>reviewUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>reviewUser.userName</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REVIEW_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,14">
          <entity_field_id>requestedUser</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUESTED_USER_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,15">
          <entity_field_id>requestedUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>requestedUser.userName</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUESTED_USER_NAME</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,16">
          <entity_field_id>sampleRequestTemplate</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleRequestTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SAMPLE_REQUEST_TEMPLATE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,17">
          <entity_field_id>materialRequestTemplate</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>MaterialRequestTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MATERIAL_REQUEST_TEMPLATE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,18">
          <entity_field_id>documentRequestTemplate</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>DocumentRequestTemplate.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DOCUMENT_REQUEST_TEMPLATE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,19">
          <entity_field_id>sourcingRecord</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SourcingRecord.id</entity_lookup>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>sourcingRecordNo,id</transitive_fields>
          <report_column_name>SOURCING_RECORD_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,20">
          <entity_field_id>project</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Project.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PROJECT_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,21">
          <entity_field_id>projectNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>project.projectNo</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PROJECT_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,22">
          <entity_field_id>projectName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>project.projectName</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PROJECT_NAME</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,23">
          <entity_field_id>lineSheetNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>LINESHEET_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,24">
          <entity_field_id>lineSheetName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>LINESHEET_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,28">
          <entity_field_id>trackerNo</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_TRACKER_NO","system.pattern.TrackerNo", "ST#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TRACKER_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,29">
          <entity_field_id>requestFrom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>REQUEST_FROM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUESET_FROM</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,30">
          <entity_field_id>shortDesc</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHORT_DESC</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,31">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,32">
          <entity_field_id>requestedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUESETED_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,33">
          <entity_field_id>vendorEmail</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_EMAIL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,34">
          <entity_field_id>country</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>COUNTRY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>COUNTRY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,35">
          <entity_field_id>reviewedOn</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REVIEWED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,36">
          <entity_field_id>result</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_REVIEW_RESULT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>RESULT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,37">
          <entity_field_id>comments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>COMMENTS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,38">
          <entity_field_id>requestedUserNamePretend</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,39">
          <entity_field_id>reviewUserNamePretend</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,40">
          <entity_field_id>season</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SEASON</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SEASON</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,41">
          <entity_field_id>year</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>YEAR</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>YEAR</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,42">
          <entity_field_id>vendor</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vendor.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>businessName,vendorCode,vendorRating</transitive_fields>
          <report_column_name>VENDOR_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,43">
          <entity_field_id>vendorCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,44">
          <entity_field_id>vendorName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,45">
          <entity_field_id>vendorRating</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>VENDOR_RATING</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_RATING</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,46">
          <entity_field_id>factory</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Fact.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>businessName,rank,assessmentLevel</transitive_fields>
          <report_column_name>FACTORY_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,47">
          <entity_field_id>factoryName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACTORY_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,48">
          <entity_field_id>rank</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>FACTORY_RANK</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACTORY_RANK</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,49">
          <entity_field_id>assessmentLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>FACTORY_ASSESSMENT_LEVEL</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACTORY_ASSESSMENT_LEVEL</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,50">
          <entity_field_id>custId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Cust.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date>2</max_date>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,51">
          <entity_field_id>styleNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>STYLE_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
      </elements>
      <elements id="collection">
        <element position="sampleTracker_entity.xlsx,entityDef,55">
          <entity_field_id>sampleDetail</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SampleDetail.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SAMPLE_DETAIL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,56">
          <entity_field_id>materialDetail</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>MaterialDetail.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>MATERIAL_DETAIL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,57">
          <entity_field_id>documentDetail</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>DocumentDetail.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>DOCUMENT_DETAIL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,58">
          <entity_field_id>responsibleParties</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-entity</dataType>
          <data1>User</data1>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>RESPONSIBLE_PARTIES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,59">
          <entity_field_id>sampleTrackerImages</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SampleTrackerImage.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SAMPLE_TRACKER_IMAGES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,60">
          <entity_field_id>sampleTrackerAttachments</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SampleTrackerAttachment.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SAMPLE_TRACKER_ATTACHMENTS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,61">
          <entity_field_id>sampleTrackerShareFiles</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>SampleTrackerShareFile.sampleTrackerId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>SAMPLE_TRACKER_SHARE_FILES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,62">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <transitive_fields/>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="SampleDetail" position="sampleTracker_entity.xlsx,entityDef,65" ref_pattern="" report_table_name="SAMPLE_DETAIL" table_name="CNT_SAMPLE_DETAIL">
      <elements id="reference">
        <element position="sampleTracker_entity.xlsx,entityDef,72">
          <entity_field_id>color</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Color.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>COLOR_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,73">
          <entity_field_id>colorName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>color.colorName</snapshot_field>
          <report_column_name>COLOR_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,74">
          <entity_field_id>pattern</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Pattern.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>PATTERN_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,75">
          <entity_field_id>patternName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>pattern.shortName</snapshot_field>
          <report_column_name>PATTERN_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,76">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>ITEM_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,77">
          <entity_field_id>itemVersion</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.version</snapshot_field>
          <report_column_name>ITEM_VERSION</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,78">
          <entity_field_id>sampleRequest</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleRequest.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>SAMPLE_REQUEST_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,79">
          <entity_field_id>sampleRequestNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>sampleRequest.sampleRequestNo</snapshot_field>
          <report_column_name>SAMPLE_REQUEST_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,80">
          <entity_field_id>requestedUser</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>REQUESTED_USER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,81">
          <entity_field_id>requestedUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>requestedUser.userName</snapshot_field>
          <report_column_name>REQUESTED_USER_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
      </elements>
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,85">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,86">
          <entity_field_id>seqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,87">
          <entity_field_id>sampleId</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_SAMPLE_DETAIL_ID","system.pattern.sampleDetailSampleId", "SRID#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,88">
          <entity_field_id>sampleType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_TYPE</data1>
          <data2/>
          <report_column_name>SAMPLE_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,89">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,90">
          <entity_field_id>isReqVendorEva</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>deprecated from CNT-38701</description>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,91">
          <entity_field_id>isSampleSent</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,92">
          <entity_field_id>allowVendorSubmission</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALLOW_VENDOR_SUBMISSION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>deprecated from CNT-38701</description>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,93">
          <entity_field_id>requestedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESTED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,94">
          <entity_field_id>sampleVersion</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VER</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,95">
          <entity_field_id>requestedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESETED_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,96">
          <entity_field_id>requestedUserNamePretend</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,97">
          <entity_field_id>uom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <report_column_name>UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,98">
          <entity_field_id>altColor</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALTERNATE_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,99">
          <entity_field_id>altPattern</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALTERNATE_PATTERN</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,100">
          <entity_field_id>altColorAndPattern</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,101">
          <entity_field_id>sizeCode</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SIZE_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,102">
          <entity_field_id>altSizeCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALTERNATE_SIZE_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,103">
          <entity_field_id>weight</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>WEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,104">
          <entity_field_id>weightUOM</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>WEIGHT_UOM</data1>
          <data2/>
          <report_column_name>WEIGHT_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,105">
          <entity_field_id>deliverTo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_DELIVER_TO</data1>
          <data2/>
          <report_column_name>DELIVER_TO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,106">
          <entity_field_id>dueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DUE_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,107">
          <entity_field_id>sentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,108">
          <entity_field_id>sentQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,109">
          <entity_field_id>courier</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>COURIER</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,110">
          <entity_field_id>trackingNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TRACKING_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,111">
          <entity_field_id>vendorComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,112">
          <entity_field_id>receivedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,113">
          <entity_field_id>receivedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,114">
          <entity_field_id>status</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_STATUS</data1>
          <data2/>
          <report_column_name>STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,115">
          <entity_field_id>result</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_RESULT</data1>
          <data2/>
          <report_column_name>RESULT</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,116">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,117">
          <entity_field_id>additionalComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDITIONAL_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,118">
          <entity_field_id>attachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT1</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,119">
          <entity_field_id>attachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT2</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,120">
          <entity_field_id>attachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT3</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,121">
          <entity_field_id>vendorAttachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT1</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,122">
          <entity_field_id>vendorAttachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT2</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,123">
          <entity_field_id>vendorAttachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT3</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,124">
          <entity_field_id>approvalSeq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>APPROVAL_SEQ</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,125">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,126">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,127">
          <entity_field_id>sampleEvaluation</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleEvaluation.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_EVALUATION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,128">
          <entity_field_id>isInactive</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_INACTIVE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,129">
          <entity_field_id>isCopiedInVendor</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="sampleTracker_entity.xlsx,entityDef,133">
          <entity_field_id>colorAndPattern</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-entity</dataType>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
      </elements>
    </Entity>
    <Entity name="MaterialDetail" position="sampleTracker_entity.xlsx,entityDef,136" ref_pattern="" report_table_name="MATERIAL_DETAIL" table_name="CNT_MATERIAL_DETAIL">
      <elements id="reference">
        <element position="sampleTracker_entity.xlsx,entityDef,143">
          <entity_field_id>material</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Component.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>MATERIAL_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,144">
          <entity_field_id>materialNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>material.componentNo</snapshot_field>
          <report_column_name>MATERIAL_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,145">
          <entity_field_id>materialName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>material.materialName</snapshot_field>
          <report_column_name>MATERIAL_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,146">
          <entity_field_id>color</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Color.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>COLOR_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,147">
          <entity_field_id>colorName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>color.colorName</snapshot_field>
          <report_column_name>COLOR_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,148">
          <entity_field_id>pattern</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Pattern.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>PATTERN_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,149">
          <entity_field_id>patternName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>pattern.shortName</snapshot_field>
          <report_column_name>PATTERN_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <description>no used in CNT-24855</description>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,150">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>ITEM_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,151">
          <entity_field_id>itemVersion</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.version</snapshot_field>
          <report_column_name>ITEM_VERSION</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,152">
          <entity_field_id>sampleRequest</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleRequest.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>SAMPLE_REQUEST_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,153">
          <entity_field_id>sampleRequestNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>sampleRequest.sampleRequestNo</snapshot_field>
          <report_column_name>SAMPLE_REQUEST_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,154">
          <entity_field_id>requestedUser</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>REQUESTED_USER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,155">
          <entity_field_id>requestedUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>requestedUser.userName</snapshot_field>
          <report_column_name>REQUESTED_USER_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <description/>
        </element>
      </elements>
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,159">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,160">
          <entity_field_id>seqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,161">
          <entity_field_id>sampleId</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_MATERIAL_DETAIL_ID","system.pattern.materialDetailSampleId", "MAID#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,162">
          <entity_field_id>sampleType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MATERIAL_SAMPLE_TYPE</data1>
          <data2/>
          <report_column_name>SAMPLE_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,163">
          <entity_field_id>materialType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>COMPONENT_MATERIAL_TYPE</data1>
          <data2/>
          <report_column_name>MATERIAL_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,164">
          <entity_field_id>materialSubType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>COMPONENT_MATERIAL_SUB_TYPE</data1>
          <data2/>
          <report_column_name>MATERIAL_SUB_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,165">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,166">
          <entity_field_id>requestedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESTED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,167">
          <entity_field_id>sampleVersion</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VER</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,168">
          <entity_field_id>requestedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESETED_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,169">
          <entity_field_id>requestedUserNamePretend</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,170">
          <entity_field_id>uom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <report_column_name>UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,171">
          <entity_field_id>altColor</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALTERNATE_COLOR</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,172">
          <entity_field_id>altPattern</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALTERNATE_PATTERN</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,173">
          <entity_field_id>altColorAndPattern</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,174">
          <entity_field_id>sizeCode</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SIZE_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,175">
          <entity_field_id>dimension</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DIMENSION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,176">
          <entity_field_id>yardage</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>YARDAGE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,177">
          <entity_field_id>weight</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>WEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,178">
          <entity_field_id>weightUOM</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>WEIGHT_UOM</data1>
          <data2/>
          <report_column_name>WEIGHT_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,179">
          <entity_field_id>deliverTo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_DELIVER_TO</data1>
          <data2/>
          <report_column_name>DELIVER_TO</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,180">
          <entity_field_id>dueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DUE_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,181">
          <entity_field_id>sentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,182">
          <entity_field_id>sentQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,183">
          <entity_field_id>courier</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>COURIER</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,184">
          <entity_field_id>trackingNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TRACKING_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,185">
          <entity_field_id>vendorComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,186">
          <entity_field_id>receivedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,187">
          <entity_field_id>receivedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,188">
          <entity_field_id>status</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_STATUS</data1>
          <data2/>
          <report_column_name>STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,189">
          <entity_field_id>result</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_RESULT</data1>
          <data2/>
          <report_column_name>RESULT</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,190">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,191">
          <entity_field_id>additionalComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDITIONAL_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,192">
          <entity_field_id>attachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT1</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,193">
          <entity_field_id>attachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT2</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,194">
          <entity_field_id>attachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT3</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,195">
          <entity_field_id>vendorAttachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,196">
          <entity_field_id>vendorAttachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,197">
          <entity_field_id>vendorAttachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,198">
          <entity_field_id>approvalSeq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>APPROVAL_SEQ</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,199">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,200">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,201">
          <entity_field_id>sampleEvaluation</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleEvaluation.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_EVALUATION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,202">
          <entity_field_id>isInactive</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_INACTIVE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="sampleTracker_entity.xlsx,entityDef,206">
          <entity_field_id>colorAndPattern</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-entity</dataType>
          <data1/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level/>
        </element>
      </elements>
    </Entity>
    <Entity name="DocumentDetail" position="sampleTracker_entity.xlsx,entityDef,209" ref_pattern="" report_table_name="DOCUMENT_DETAIL" table_name="CNT_DOCUMENT_DETAIL">
      <elements id="reference">
        <element position="sampleTracker_entity.xlsx,entityDef,216">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>ITEM_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,217">
          <entity_field_id>itemVersion</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>item.version</snapshot_field>
          <report_column_name>ITEM_VERSION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,218">
          <entity_field_id>sampleRequest</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>SampleRequest.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>SAMPLE_REQUEST_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,219">
          <entity_field_id>sampleRequestNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>sampleRequest.sampleRequestNo</snapshot_field>
          <report_column_name>SAMPLE_REQUEST_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,220">
          <entity_field_id>requestedUser</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <report_column_name>REQUESTED_USER_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,221">
          <entity_field_id>requestedUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>requestedUser.userName</snapshot_field>
          <report_column_name>REQUESTED_USER_NAME</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,225">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,226">
          <entity_field_id>seqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,227">
          <entity_field_id>sampleId</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_DOCUMENT_DETAIL_ID","system.pattern.documentDetailSampleId", "DRID#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_SID</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,228">
          <entity_field_id>documentType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DOCUMENTS_TYPE</data1>
          <data2/>
          <report_column_name>DOCUMENT_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,229">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,230">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,231">
          <entity_field_id>requestedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESTED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,232">
          <entity_field_id>sampleVersion</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VER</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,233">
          <entity_field_id>requestedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESETED_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,234">
          <entity_field_id>requestedUserNamePretend</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,235">
          <entity_field_id>deliverTo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_DELIVER_TO</data1>
          <data2/>
          <report_column_name>DELIVER_TO</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,236">
          <entity_field_id>dueDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DUE_DATE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,237">
          <entity_field_id>sentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,238">
          <entity_field_id>sentQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SENT_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,239">
          <entity_field_id>courier</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>COURIER</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,240">
          <entity_field_id>trackingNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TRACKING_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,241">
          <entity_field_id>vendorComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,242">
          <entity_field_id>receivedDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,243">
          <entity_field_id>receivedQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RECEIVED_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,244">
          <entity_field_id>status</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_STATUS</data1>
          <data2/>
          <report_column_name>STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,245">
          <entity_field_id>result</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SAMPLE_RESULT</data1>
          <data2/>
          <report_column_name>RESULT</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,246">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,247">
          <entity_field_id>additionalComments</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDITIONAL_COMMENTS</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,248">
          <entity_field_id>attachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT1</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,249">
          <entity_field_id>attachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT2</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,250">
          <entity_field_id>attachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT3</report_column_name>
          <tracking_level>2</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,251">
          <entity_field_id>vendorAttachment1</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT1</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,252">
          <entity_field_id>vendorAttachment2</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT2</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,253">
          <entity_field_id>vendorAttachment3</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>VENDOR_ATTACHMENT3</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,254">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,255">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,256">
          <entity_field_id>approvalSeq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>APPROVAL_SEQ</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,257">
          <entity_field_id>qcFile</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>QC_FILE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,258">
          <entity_field_id>isInactive</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_INACTIVE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
    <Entity name="SampleTrackerImage" position="sampleTracker_entity.xlsx,entityDef,261" ref_pattern="" report_table_name="SAMPLE_TRACKER_IMAGE" table_name="CNT_SAMPLE_TRACKER_IMAGE">
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,268">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,269">
          <entity_field_id>isDefault</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_DEFAULT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,270">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,271">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,272">
          <entity_field_id>qcFile</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>QC_FILE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,273">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,274">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="sampleTracker_entity.xlsx,entityDef,278">
          <entity_field_id>imageTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>IMAGE_TYPE</data1>
          <transitive_fields/>
          <report_column_name>N/A</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="SampleTrackerAttachment" position="sampleTracker_entity.xlsx,entityDef,281" ref_pattern="" report_table_name="SAMPLE_TRACKER_ATTACHMENT" table_name="CNT_SAMPLE_TRACKER_ATTACHMENT">
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,288">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,289">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,290">
          <entity_field_id>attachment</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ATTACHMENT_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,291">
          <entity_field_id>fileAddress</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>FILE_ADDRESS</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,292">
          <entity_field_id>qcFile</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>QC_FILE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,293">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,294">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="sampleTracker_entity.xlsx,entityDef,298">
          <entity_field_id>attachmentTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>ATTACHMENT_TYPE</data1>
          <transitive_fields/>
          <report_column_name>N/A</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="SampleTrackerShareFile" position="sampleTracker_entity.xlsx,entityDef,301" ref_pattern="" report_table_name="SAMPLE_TRACKER_SHARE_FILE" table_name="CNT_SAMPLE_TRACKER_SHARE_FILE">
      <elements id="header">
        <element position="sampleTracker_entity.xlsx,entityDef,308">
          <entity_field_id>sampleTrackerId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SAMPLE_TRACKER_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,309">
          <entity_field_id>shareFileId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShareFile.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHARE_FILE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="sampleTracker_entity.xlsx,entityDef,310">
          <entity_field_id>isAddInDoc</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ADD_IN_DOC</report_column_name>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="sampleTracker_entity.xlsx,status">
    <Status position="sampleTracker_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="sampleTracker_entity.xlsx,status,4">
          <code>draft</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,5">
          <code>requested</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,6">
          <code>submitted</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,7">
          <code>received</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,8">
          <code>inProgress</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,9">
          <code>accepted</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,10">
          <code>rejected</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,11">
          <code>confirmed</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,12">
          <code>completed</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,13">
          <code>customStatus01</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,14">
          <code>customStatus02</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,15">
          <code>customStatus03</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,16">
          <code>customStatus04</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,17">
          <code>customStatus05</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,18">
          <code>customStatus06</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,19">
          <code>customStatus07</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,20">
          <code>customStatus08</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,21">
          <code>customStatus09</code>
        </element>
        <element position="sampleTracker_entity.xlsx,status,22">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
