<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="rfq" position="rfq_validation.xlsx">
  <sheet id="ValidationProfile" position="rfq_validation.xlsx,ValidationProfile">
    <ValidationProfile position="rfq_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="rfq_validation.xlsx,ValidationProfile,4">
          <id>ff973020d5c94ff09a73500f9dacd553</id>
          <profileName>Default Data Validation Profile Rfq[ver:1]</profileName>
          <entityName>Rfq</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05Doc,MarkAsCustomStatus06Doc,<PERSON><PERSON><PERSON>ustom<PERSON>tatus07Doc,MarkAs<PERSON>ustomStatus08Doc,Mark<PERSON><PERSON>ustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:05.100</updatedOn>
        </element>
        <element position="rfq_validation.xlsx,ValidationProfile,5">
          <id>722a4adc0a5f4e0ba9a314d8e96d719c</id>
          <profileName>Default Data Validation Profile Rfq[ver:1]</profileName>
          <entityName>Rfq</entityName>
          <entityVer>1</entityVer>
          <action>RfqSend</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:08.250</updatedOn>
        </element>
        <element position="rfq_validation.xlsx,ValidationProfile,6">
          <id>2ab9acee2e7b4131aa23ce1b722e8556</id>
          <profileName>PopupRfqQtyBreakdown</profileName>
          <entityName>RfqItem</entityName>
          <entityVer>1</entityVer>
          <action>PopupRfqQtyBreakdownOk</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField>Y</ignoreCustomField>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:08.250</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="rfq_validation.xlsx,ValidationRule">
    <ValidationRule position="rfq_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="rfq_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <restapiBeanName>UniqueInSectionValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,7">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <restapiBeanName>EmailValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,8">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,9">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <restapiBeanName>HCLValidator</restapiBeanName>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,10">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <restapiBeanName>ExternalActiveValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,11">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <restapiBeanName>NumericRangeValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="rfq_validation.xlsx,ValidationRule,12">
          <type>ColorSizeUpdatedValidator</type>
          <className>com.core.cbx.rfq.validator.ColorSizeUpdatedValidator</className>
          <restapiBeanName>ColorSizeUpdatedValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="rfq_validation.xlsx,MandatoryValidator">
    <ValidationField position="rfq_validation.xlsx,MandatoryValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,MandatoryValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,9">
          <entityName>Rfq</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,10">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,11">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqItem</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,MandatoryValidator,14" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,MandatoryValidator,21">
          <entityName>Rfq</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,22">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,23">
          <entityName>Rfq</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,24">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="rfq_validation.xlsx,MandatoryValidator,25">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqItem</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,MandatoryValidator,28" profileId="2ab9acee2e7b4131aa23ce1b722e8556" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,MandatoryValidator,35">
          <entityName>RfqItem</entityName>
          <fieldId>planQtyBy</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>planQtyBy</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="rfq_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="rfq_validation.xlsx,UniqueInModuleValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,UniqueInModuleValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInModuleValidator,18">
          <entityName>Rfq</entityName>
          <fieldId>rfqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="rfq_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="rfq_validation.xlsx,UniqueInSectionValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <FIELD_GROUP/>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,9">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <FIELD_GROUP>itemNo,projectNo,sourcingRecordNo</FIELD_GROUP>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <ERROR_ID>REF055</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,UniqueInSectionValidator,12" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,19">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <FIELD_GROUP/>
          <GRID_ID/>
          <LABEL_FIELD_ID>rfqVendor</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
        <element position="rfq_validation.xlsx,UniqueInSectionValidator,20">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <FIELD_GROUP>itemNo,projectNo,sourcingRecordNo</FIELD_GROUP>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <ERROR_ID>REF055</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="rfq_validation.xlsx,EmailValidator">
    <ValidationField position="rfq_validation.xlsx,EmailValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,EmailValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
          <MULTIPLE_EMAILS>TRUE</MULTIPLE_EMAILS>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,EmailValidator,11" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,EmailValidator,18">
          <entityName>RfqVendor</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
          <MULTIPLE_EMAILS>TRUE</MULTIPLE_EMAILS>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="rfq_validation.xlsx,ClassificationValidator">
    <ValidationField position="rfq_validation.xlsx,ClassificationValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ClassificationValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
          <MATCH_TYPE>ALL</MATCH_TYPE>
        </element>
        <element position="rfq_validation.xlsx,ClassificationValidator,9">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>item</TARGET_FIELD>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
          <MATCH_TYPE/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,ClassificationValidator,12" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ClassificationValidator,19">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>vendor</TARGET_FIELD>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
          <MATCH_TYPE>ALL</MATCH_TYPE>
        </element>
        <element position="rfq_validation.xlsx,ClassificationValidator,20">
          <entityName>Rfq</entityName>
          <fieldId>rfqItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD>item</TARGET_FIELD>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
          <MATCH_TYPE/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="rfq_validation.xlsx,HCLValidator">
    <ValidationField position="rfq_validation.xlsx,HCLValidator,1" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,HCLValidator,8">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="rfq_validation.xlsx,HCLValidator,9">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hierarchy</TARGET_FIELD>
          <TYPE>gridSelectBiz</TYPE>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,HCLValidator,12" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,HCLValidator,19">
          <entityName>RfqVendor</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>gridSelectMaster</TYPE>
        </element>
        <element position="rfq_validation.xlsx,HCLValidator,20">
          <entityName>RfqItem</entityName>
          <fieldId>item</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hierarchy</TARGET_FIELD>
          <TYPE>gridSelectBiz</TYPE>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="rfq_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="rfq_validation.xlsx,ExternalActiveValidator,1" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ExternalActiveValidator,8">
          <entityName>Rfq</entityName>
          <fieldId>rfqVendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqVendor</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="rfq_validation.xlsx,NumericRangeValidator">
    <ValidationField position="rfq_validation.xlsx,NumericRangeValidator,1" profileId="2ab9acee2e7b4131aa23ce1b722e8556" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,NumericRangeValidator,8">
          <entityName>RfqItemColorQty</entityName>
          <fieldId>colorPlannedQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItemColorQty</GRID_ID>
          <LABEL_FIELD_ID>colorPlannedQty</LABEL_FIELD_ID>
          <MIN_VALUE/>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>REF159</ERROR_ID>
        </element>
        <element position="rfq_validation.xlsx,NumericRangeValidator,9">
          <entityName>RfqItemSizeQty</entityName>
          <fieldId>sizePlannedQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItemSizeQty</GRID_ID>
          <LABEL_FIELD_ID>sizePlannedQty</LABEL_FIELD_ID>
          <MIN_VALUE/>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>REF159</ERROR_ID>
        </element>
        <element position="rfq_validation.xlsx,NumericRangeValidator,10">
          <entityName>RfqItemColorSizeQty</entityName>
          <fieldId>colorSizePlannedQty</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItemColorSizeQty</GRID_ID>
          <LABEL_FIELD_ID>colorSizePlannedQty</LABEL_FIELD_ID>
          <MIN_VALUE/>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>REF159</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,NumericRangeValidator,13" profileId="ff973020d5c94ff09a73500f9dacd553" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,NumericRangeValidator,20">
          <entityName>RfqItem</entityName>
          <fieldId>targetPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>targetPrice</LABEL_FIELD_ID>
          <MIN_VALUE/>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="rfq_validation.xlsx,NumericRangeValidator,23" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,NumericRangeValidator,30">
          <entityName>RfqItem</entityName>
          <fieldId>targetPrice</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <LABEL_FIELD_ID>targetPrice</LABEL_FIELD_ID>
          <MIN_VALUE/>
          <GREATE_THAN_VALUE>0</GREATE_THAN_VALUE>
          <ERROR_ID>15000022</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ColorSizeUpdatedValidator" position="rfq_validation.xlsx,ColorSizeUpdatedValidator">
    <ValidationField position="rfq_validation.xlsx,ColorSizeUpdatedValidator,1" profileId="722a4adc0a5f4e0ba9a314d8e96d719c" profileName="">
      <elements id="default">
        <element position="rfq_validation.xlsx,ColorSizeUpdatedValidator,8">
          <entityName>RfqItem</entityName>
          <fieldId>planQtyBy</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>rfqItem</GRID_ID>
          <ERROR_ID>REF200</ERROR_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
