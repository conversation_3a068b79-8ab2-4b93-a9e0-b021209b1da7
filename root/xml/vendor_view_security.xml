<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="vendor" position="vendor_view_security.xlsx">
  <sheet id="generalInfo" position="vendor_view_security.xlsx,generalInfo">
    <GeneralInfo position="vendor_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="vendor_view_security.xlsx,condition">
    <ConditionList position="vendor_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="vendor_view_security.xlsx,default">
    <ActionConditionMatrix position="vendor_view_security.xlsx,default,1">
      <elements id="default">
        <element position="vendor_view_security.xlsx,default,4">
          <actionId>importRawData</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,5">
          <actionId>searchActivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,6">
          <actionId>searchCancelDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,7">
          <actionId>searchDeactivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,8">
          <actionId>searchNewDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,9">
          <actionId>searchViewAllExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,10">
          <actionId>searchViewCurExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,11">
          <actionId>searchDraftStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,12">
          <actionId>searchOfficialStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,13">
          <actionId>searchSubmitStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,14">
          <actionId>searchProspectStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,15">
          <actionId>searchNewStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,16">
          <actionId>searchActiveStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,17">
          <actionId>searchInactiveStatus</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,18">
          <actionId>vendorAgreeSearchMarkAsConfirm</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,19">
          <actionId>vendorAgreeSearchMarkAsCanceled</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,20">
          <actionId>batchUpdateField</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,21">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,22">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,23">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,24">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,25">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,26">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,27">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,28">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,29">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,30">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,31">
          <actionId>searchCustomAction01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,32">
          <actionId>searchCustomAction02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,33">
          <actionId>searchCustomAction03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,34">
          <actionId>searchCustomAction04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,35">
          <actionId>searchCustomAction05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,36">
          <actionId>searchCustomAction06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,37">
          <actionId>searchCustomAction07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,38">
          <actionId>searchCustomAction08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,39">
          <actionId>searchCustomAction09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,40">
          <actionId>searchCustomAction10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,41">
          <actionId>vendorSearchViewCompliance</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,42">
          <actionId>inviteVendors</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,43">
          <actionId>inviteVendorUpgradeToPremium</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,44">
          <actionId>searchConvertToOnline</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,45">
          <actionId>openBatchUpdateWin</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,46">
          <actionId>openCpmMode</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,47">
          <actionId>vendorAgreeSearchResend</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,48">
          <actionId>batchAddAgreement</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,49">
          <actionId>vendorAgreeSearchSetInactive</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,50">
          <actionId>vendorBatchSearchNewShareFile</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="vendor_view_security.xlsx,default,51">
          <actionId>vendorBatchSearchNewFactAuditDoc</actionId>
          <ANY>allowed</ANY>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="vendor_view_security.xlsx,default,54">
      <elements id="default"/>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="vendor_view_security.xlsx,acl">
    <ActionRule position="vendor_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="vendor_view_security.xlsx,acl,4">
          <actionId>importRawData</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,5">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,6">
          <actionId>searchCancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,7">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,8">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,9">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,10">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,11">
          <actionId>searchDraftStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,12">
          <actionId>searchOfficialStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,13">
          <actionId>searchSubmitStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,14">
          <actionId>vendorAgreeSearchMarkAsConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,15">
          <actionId>vendorAgreeSearchMarkAsCanceled</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,16">
          <actionId>searchProspectStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,17">
          <actionId>searchNewStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,18">
          <actionId>searchActiveStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,19">
          <actionId>searchInactiveStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,20">
          <actionId>batchUpdateField</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,21">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,22">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,23">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,24">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,25">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,26">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,27">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,28">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,29">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,30">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,31">
          <actionId>searchCustomAction01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,32">
          <actionId>searchCustomAction02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,33">
          <actionId>searchCustomAction03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,34">
          <actionId>searchCustomAction04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,35">
          <actionId>searchCustomAction05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,36">
          <actionId>searchCustomAction06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,37">
          <actionId>searchCustomAction07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,38">
          <actionId>searchCustomAction08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,39">
          <actionId>searchCustomAction09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,40">
          <actionId>searchCustomAction10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,41">
          <actionId>vendorSearchViewCompliance</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,42">
          <actionId>inviteVendors</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,43">
          <actionId>inviteVendorUpgradeToPremium</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,44">
          <actionId>searchConvertToOnline</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,45">
          <actionId>openBatchUpdateWin</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,46">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,47">
          <actionId>vendorAgreeSearchResend</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,48">
          <actionId>vendorAgreeSearchSetInactive</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,49">
          <actionId>batchAddAgreement</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,50">
          <actionId>vendorBatchSearchNewShareFile</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>not-has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vendor_view_security.xlsx,acl,51">
          <actionId>vendorBatchSearchNewFactAuditDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <vendor.Author>has</vendor.Author>
          <vendor.Editor>not-has</vendor.Editor>
          <vendor.ReadOnly>not-has</vendor.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="vendor_view_security.xlsx,acl,54">
      <elements id="default">
        <element position="vendor_view_security.xlsx,acl,57">
          <componentId>ui.productCategory</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vendor_view_security.xlsx,acl,58">
          <componentId>ui.deactivationReason</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vendor_view_security.xlsx,acl,59">
          <componentId>ui.deactivationReasonName</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="vendor_view_security.xlsx,acl,62">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
