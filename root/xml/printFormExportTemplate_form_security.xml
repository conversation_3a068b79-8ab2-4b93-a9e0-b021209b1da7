<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="printFormExportTemplate" position="printFormExportTemplate_form_security.xlsx">
  <sheet id="_system" position="printFormExportTemplate_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="printFormExportTemplate_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="printFormExportTemplate_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,_system,10">
          <updatedOn>2014/Oct/17</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="printFormExportTemplate_form_security.xlsx,generalInfo">
    <GeneralInfo position="printFormExportTemplate_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="printFormExportTemplate_form_security.xlsx,condition">
    <ConditionList position="printFormExportTemplate_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,6">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,7">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,8">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,9">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,10">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,11">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,condition,12">
          <conditionId>isNotLatest</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="printFormExportTemplate_form_security.xlsx,default">
    <ActionConditionMatrix position="printFormExportTemplate_form_security.xlsx,default,1">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,default,4">
          <actionId>amendDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,5">
          <actionId>saveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,6">
          <actionId>discardDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,7">
          <actionId>customExport01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,8">
          <actionId>customExport02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,9">
          <actionId>customExport03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,10">
          <actionId>customExport04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,11">
          <actionId>customExport05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,12">
          <actionId>customExport06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,13">
          <actionId>customExport07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,14">
          <actionId>customExport08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,15">
          <actionId>customExport09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,16">
          <actionId>customExport10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,17">
          <actionId>customPrint01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,18">
          <actionId>customPrint02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,19">
          <actionId>customPrint03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,20">
          <actionId>customPrint04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,21">
          <actionId>customPrint05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,22">
          <actionId>customPrint06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,23">
          <actionId>customPrint07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,24">
          <actionId>customPrint08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,25">
          <actionId>customPrint09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,26">
          <actionId>customPrint10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,27">
          <actionId>loadDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,28">
          <actionId>activateDoc</actionId>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,29">
          <actionId>deactivateDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,30">
          <actionId>printFormExportTemplateDownload</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,31">
          <actionId>printFormExportTemplateUpload</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,32">
          <actionId>PrintFormExportTemplateCustom01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,33">
          <actionId>PrintFormExportTemplateCustom02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,34">
          <actionId>PrintFormExportTemplateCustom03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,35">
          <actionId>PrintFormExportTemplateCustom04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,36">
          <actionId>PrintFormExportTemplateCustom05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,37">
          <actionId>PrintFormExportTemplateCustom06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,38">
          <actionId>PrintFormExportTemplateCustom07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,39">
          <actionId>PrintFormExportTemplateCustom08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,40">
          <actionId>PrintFormExportTemplateCustom09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,41">
          <actionId>PrintFormExportTemplateCustom10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="printFormExportTemplate_form_security.xlsx,default,44">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,default,47">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,48">
          <componentId>ui.printFormExportTemplateLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,49">
          <componentId>ui.printFormExportTemplateLinkbar.approval</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,50">
          <componentId>ui.printFormExportTemplateLinkbar.addToFavorites</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,default,51">
          <componentId>ui.printFormExportTemplateMenubar.amendDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="printFormExportTemplate_form_security.xlsx,acl">
    <ActionRule position="printFormExportTemplate_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,acl,4">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,5">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,6">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,7">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,8">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,9">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,10">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,11">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,12">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,13">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,14">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,15">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,16">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,17">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,18">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,19">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,20">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,21">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,22">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,23">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,24">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,25">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,26">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,27">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,28">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,29">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,30">
          <actionId>printFormExportTemplateDownload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,31">
          <actionId>printFormExportTemplateUpload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,32">
          <actionId>PrintFormExportTemplateCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,33">
          <actionId>PrintFormExportTemplateCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,34">
          <actionId>PrintFormExportTemplateCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,35">
          <actionId>PrintFormExportTemplateCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,36">
          <actionId>PrintFormExportTemplateCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,37">
          <actionId>PrintFormExportTemplateCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,38">
          <actionId>PrintFormExportTemplateCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,39">
          <actionId>PrintFormExportTemplateCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,40">
          <actionId>PrintFormExportTemplateCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,41">
          <actionId>PrintFormExportTemplateCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <printFormExportTemplate.Author>not-has</printFormExportTemplate.Author>
          <printFormExportTemplate.Editor>not-has</printFormExportTemplate.Editor>
          <printFormExportTemplate.ReadOnly>not-has</printFormExportTemplate.ReadOnly>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="printFormExportTemplate_form_security.xlsx,acl,44">
      <elements id="default">
        <element position="printFormExportTemplate_form_security.xlsx,acl,47">
          <componentId>ui.tabPrintForms</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,48">
          <componentId>ui.tabExportTemplates</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="printFormExportTemplate_form_security.xlsx,acl,49">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="printFormExportTemplate_form_security.xlsx,acl,52">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
