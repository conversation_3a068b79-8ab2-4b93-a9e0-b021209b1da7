<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="shareFile" position="shareFile_view.xlsx">
  <sheet id="shareFileActive" position="shareFile_view.xlsx,shareFileActive">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Active View" id="shareFileActive" label="Share File - Active" moduleId="shareFile" position="shareFile_view.xlsx,shareFileActive,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileActive,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,16">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,17">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ShareFile</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,18">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessRefNo:string,attachments:attachments:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileActive,22">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,23">
          <id>searchNewDoc</id>
          <label>New Share File</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,24">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,25">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,26">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,27">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,28">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,29">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,30">
          <id>searchOutOfSignatureStatus</id>
          <label>Out for Signature</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,31">
          <id>searchSignedStatus</id>
          <label>Signed</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,32">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,33">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,34">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,35">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,36">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,37">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,38">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,39">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,40">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,41">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,42">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,43">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,44">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,45">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,46">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,47">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,48">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,49">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,50">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,51">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,52">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,53">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,54">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,55">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileActive,60">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>icons:priority:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,61">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileNo:fileNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,62">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileName:fileName:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,63">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileType:fileType:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,64">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>companyValue:companyValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,65">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>category:category.code:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,66">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,67">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>references:docReferences:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,68">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedBy:requestedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,69">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedDate:requestedDate:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,70">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validFrom:validFrom:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,71">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validTo:validTo:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,72">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notesOrInstructions:notesOrInstructions:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,73">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>hierarchy:hierarchy.code:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,74">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>hierarchyName:hierarchy.name:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,75">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,76">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,77">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,78">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,80">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,81">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,82">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,87">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,88">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,89">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,90">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,91">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileActive,92">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareContentActive" position="shareFile_view.xlsx,shareContentActive">
    <ViewDefinition advancedSearchId="" description="Share Content Active View" id="shareContentActive" label="Share Content - Active" moduleId="shareFile" position="shareFile_view.xlsx,shareContentActive,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareContentActive,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (applyMode.code =|STRING ''SF'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,16">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,17">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string,applyMode.code=SF=string</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,18">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ShareFile</value>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,19">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessRefNo:string,attachments:attachments:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareContentActive,23">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,24">
          <id>sharedFileSearchNewDoc</id>
          <label>New Shared File</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,25">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,26">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,27">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,28">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,29">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,30">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,31">
          <id>searchOutOfSignatureStatus</id>
          <label>Out for Signature</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,32">
          <id>searchSignedStatus</id>
          <label>Signed</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,33">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,34">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,35">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,36">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,37">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,38">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,39">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,40">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,41">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,42">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,43">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,44">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,45">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,46">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,47">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,48">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,49">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,50">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,51">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,52">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,53">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,54">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,55">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,56">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareContentActive,61">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>icons:priority:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,62">
          <id>fileNo</id>
          <label>Ref. No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileNo:fileNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,63">
          <id>fileName</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileName:fileName:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,64">
          <id>fileTypeValue</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_Value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileTypeValue:fileTypeValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,65">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>companyValue:companyValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,66">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,67">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,68">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>references:docReferences:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,69">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedBy:requestedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,70">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedDate:requestedDate:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,71">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validFrom:validFrom:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,72">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validTo:validTo:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,73">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notesOrInstructions:notesOrInstructions:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,74">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,75">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,76">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,77">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,78">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,79">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,80">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,81">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,82">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,83">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,84">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,85">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,86">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,87">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,88">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,89">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,90">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareContentActive,91">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>cpmDocId:cpmDoc.cpmId:string,refDocRefNo:cpmDoc.refDocRefNo:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="boilerplateActive" position="shareFile_view.xlsx,boilerplateActive">
    <ViewDefinition advancedSearchId="" description="Boilerplate Active View" id="boilerplateActive" label="Boilerplate - Active" moduleId="shareFile" position="shareFile_view.xlsx,boilerplateActive,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,boilerplateActive,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (applyMode.code =|STRING ''ST'')</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,16">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string,applyMode.code=ST=string</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,17">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ShareFile</value>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,18">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessRefNo:string,attachments:attachments:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,boilerplateActive,22">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,23">
          <id>sharedTemplateSearchNewDoc</id>
          <label>Shared Template</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,24">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,25">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,26">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,27">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,28">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,29">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,30">
          <id>searchOutOfSignatureStatus</id>
          <label>Out for Signature</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,31">
          <id>searchSignedStatus</id>
          <label>Signed</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,32">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,33">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,34">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,35">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,36">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,37">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,38">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,39">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,40">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,41">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,42">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,43">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,44">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,45">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,46">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,47">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,48">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,49">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,50">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,51">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,52">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,53">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,54">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,55">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,boilerplateActive,60">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>icons:priority:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,61">
          <id>fileNo</id>
          <label>Ref. No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileNo:fileNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,62">
          <id>fileName</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileName:fileName:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,63">
          <id>fileTypeValue</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_Value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileTypeValue:fileTypeValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,64">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>companyValue:companyValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,65">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,66">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,67">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedBy:requestedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,68">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedDate:requestedDate:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,69">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validFrom:validFrom:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,70">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validTo:validTo:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,71">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notesOrInstructions:notesOrInstructions:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,72">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,73">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,74">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,75">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,76">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,77">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,79">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,80">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,81">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,82">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,83">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,84">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,85">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,86">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,87">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,boilerplateActive,88">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileAll" position="shareFile_view.xlsx,shareFileAll">
    <ViewDefinition advancedSearchId="" description="Home ShareFile All View" id="shareFileAll" label="Share File - All" moduleId="shareFile" position="shareFile_view.xlsx,shareFileAll,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileAll,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(Any)</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,16">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ShareFile</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,17">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessRefNo:string,attachments:attachments:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileAll,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,22">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,29">
          <id>searchOutOfSignatureStatus</id>
          <label>Out for Signature</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,30">
          <id>searchSignedStatus</id>
          <label>Signed</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,31">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,32">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,33">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,34">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,35">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,36">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,37">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,38">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,39">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,40">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,41">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,42">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,43">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,44">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,45">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,46">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,47">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,48">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,49">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,50">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,51">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,52">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,53">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,54">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,55">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileAll,60">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>icons:priority:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,61">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileNo:fileNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,62">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileName:fileName:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,63">
          <id>fileTypeValue</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileTypeValue:fileTypeValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,64">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>companyValue:companyValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,65">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>category:category.code:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,66">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,67">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>references:docReferences:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,68">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedBy:requestedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,69">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>requestedDate:requestedDate:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,70">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validFrom:validFrom:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,71">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validTo:validTo:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,72">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notesOrInstructions:notesOrInstructions:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,73">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>hierarchy:hierarchy.code:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,74">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>hierarchyName:hierarchy.name:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,75">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,76">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,77">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,78">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,80">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,81">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,82">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,87">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,88">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,89">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,90">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,91">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,shareFileAll,92">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus01View" position="shareFile_view.xlsx,shareFileCustomStatus01View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus01View" label="Share File - Custom Status 01" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus01View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus01'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus01View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus02View" position="shareFile_view.xlsx,shareFileCustomStatus02View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus02View" label="Share File - Custom Status 02" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus02View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus02'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus02View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus03View" position="shareFile_view.xlsx,shareFileCustomStatus03View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus03View" label="Share File - Custom Status 03" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus03View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus03'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus03View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus04View" position="shareFile_view.xlsx,shareFileCustomStatus04View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus04View" label="Share File - Custom Status 05" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus04View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus04View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus05View" position="shareFile_view.xlsx,shareFileCustomStatus05View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus05View" label="Share File - Custom Status 05" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus05View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus05View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus06View" position="shareFile_view.xlsx,shareFileCustomStatus06View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus06View" label="Share File - Custom Status 06" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus06View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus06'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus06View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus07View" position="shareFile_view.xlsx,shareFileCustomStatus07View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus07View" label="Share File - Custom Status 07" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus07View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus07'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus07View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus08View" position="shareFile_view.xlsx,shareFileCustomStatus08View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus08View" label="Share File - Custom Status 08" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus08View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus08'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus08View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus09View" position="shareFile_view.xlsx,shareFileCustomStatus09View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus09View" label="Share File - Custom Status 09" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus09View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus09'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus09View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="shareFileCustomStatus10View" position="shareFile_view.xlsx,shareFileCustomStatus10View">
    <ViewDefinition advancedSearchId="" description="Home ShareFile Custom Status View" id="shareFileCustomStatus10View" label="Share File - Custom Status 10" moduleId="shareFile" position="shareFile_view.xlsx,shareFileCustomStatus10View,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus10'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,20">
          <id>searchNewDoc</id>
          <label>New ShareFile</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,21">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,22">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,23">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,24">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,25">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,26">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,27">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,28">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,29">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,30">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,31">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,32">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,33">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,34">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,35">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,36">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,37">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,38">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,39">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,40">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,41">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,42">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,43">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,44">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,45">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,46">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,47">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,48">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,49">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,50">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=shareFile&amp;entityName=ShareFile</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,55">
          <id>icons</id>
          <label/>
          <type>com.core.cbx.sharefile.ui.IconsCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>SF.PRIORITY</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,56">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,57">
          <id>fileName</id>
          <label>File Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,58">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,59">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,60">
          <id>category</id>
          <label>Category</label>
          <type>CodeList</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,62">
          <id>references</id>
          <label>References</label>
          <type>com.core.cbx.sharefile.ui.ReferencesCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.doc_references</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,63">
          <id>requestedBy</id>
          <label>Requested by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_by</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,64">
          <id>requestedDate</id>
          <label>Requested Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.requested_date</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,65">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,66">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,67">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,68">
          <id>hierarchy</id>
          <label>Hierarchy (Code)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,69">
          <id>hierarchyName</id>
          <label>Hierarchy (Name)</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.HIERARCHY_FULL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,70">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,71">
          <id>ShareFile</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,72">
          <id>ShareFile</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility/>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,73">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.shareFile.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,74">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,75">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,76">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>SF.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,77">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,78">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,79">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,80">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,81">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,82">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,83">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,84">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,85">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="shareFile_view.xlsx,shareFileCustomStatus10View,86">
          <id>ShareFile</id>
          <label/>
          <type>ApprovalFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>SF</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popShareFileView" position="shareFile_view.xlsx,popShareFileView">
    <ViewDefinition advancedSearchId="" description="Select Share File(s)" id="popShareFileView" label="Share Files Lookup" moduleId="shareFile" position="shareFile_view.xlsx,popShareFileView,1" queryId="listShareFile" searchCriterion="">
      <elements id="options">
        <element position="shareFile_view.xlsx,popShareFileView,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (editingStatus =|STRING ''confirmed'')</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,14">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,15">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>3</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,16">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,17">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string,editingStatus=confirmed=string</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,18">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ShareFile</value>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,19">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessRefNo:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="shareFile_view.xlsx,popShareFileView,27">
          <id>fileNo</id>
          <label>File No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_no</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileNo:fileNo:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,28">
          <id>fileName</id>
          <label>File Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.file_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileName:fileName:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,29">
          <id>fileType</id>
          <label>File Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.file_type_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fileType:fileTypeValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,30">
          <id>companyValue</id>
          <label>Company</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.company_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>companyValue:companyValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,31">
          <id>category</id>
          <label>Category</label>
          <type>Text</type>
          <format>bookName=SHARE_FILE_CATEGORY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>SF.category</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>category:productCategoryValue:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,32">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,33">
          <id>validFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_from</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validFrom:validFrom:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,34">
          <id>validTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>SF.valid_to</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>validTo:validTo:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,35">
          <id>notesOrInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.notes_or_instructions</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notesOrInstructions:notesOrInstructions:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,36">
          <id>attachments</id>
          <label>Attachments</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>SF.attachments</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>attachments:attachments:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,37">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>SF.UPDATED_ON</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,38">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="shareFile_view.xlsx,popShareFileView,39">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>SF.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
