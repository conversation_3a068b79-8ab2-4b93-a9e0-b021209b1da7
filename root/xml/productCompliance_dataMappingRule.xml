<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="productCompliance" position="productCompliance_dataMappingRule.xlsx">
  <sheet id="pcSelectVpoShipDtl" position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl">
    <DataMappingRule description="Mapping for VpoShipDtl to ProductCompliance" domain="/" dstEntityName="ProductCompliance" dstEntityVersion="1" effectiveDate="2012-02-20" id="productComplianceSelectVpoShipDtl" position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,1" srcEntityName="VpoShipDtl" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>complianceShipments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,10">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSizeQty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoId</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Vpo</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipId</srcFieldId>
          <dstFieldId>vpoShip</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>VpoShip</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,13">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>vpoShipDtl</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>VpoShipDtl</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipDtlCsId</srcFieldId>
          <dstFieldId>vpoShipDtlCs</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>VpoShipDtlCs</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,15">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Item</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,16">
          <mappingType>Field</mappingType>
          <srcFieldId>color</srcFieldId>
          <dstFieldId>color</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,17">
          <mappingType>Field</mappingType>
          <srcFieldId>size</srcFieldId>
          <dstFieldId>size</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,18">
          <mappingType>Field</mappingType>
          <srcFieldId>skuNo</srcFieldId>
          <dstFieldId>skuNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,pcSelectVpoShipDtl,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copyProductComplianceAttachment" position="productCompliance_dataMappingRule.xlsx,copyProductComplianceAttachment">
    <DataMappingRule description="Mapping for Copy ProductComplianceAttachment" domain="/" dstEntityName="ProductComplianceActivity" dstEntityVersion="1" effectiveDate="2012-02-20" id="copyProductComplianceAttachment" position="productCompliance_dataMappingRule.xlsx,copyProductComplianceAttachment,1" srcEntityName="ProductComplianceAttachment" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="productCompliance_dataMappingRule.xlsx,copyProductComplianceAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,copyProductComplianceAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>complianceAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="productComplianceSelectItem" position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem">
    <DataMappingRule description="Mapping for ProductCompliance Item Select" domain="/" dstEntityName="ProductCompliance" dstEntityVersion="1" effectiveDate="2012-02-20" id="productComplianceSelectItem" position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,1" srcEntityName="ItemSku" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>complianceItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Item</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>color</srcFieldId>
          <dstFieldId>color</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>size</srcFieldId>
          <dstFieldId>size</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceSelectItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>skuNo</srcFieldId>
          <dstFieldId>skuNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="productComplianceCopyDoc" position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc">
    <DataMappingRule description="Mapping for Copy ProductCompliance" domain="/" dstEntityName="ProductCompliance" dstEntityVersion="1" effectiveDate="2012-02-20" id="productComplianceCopyDoc" position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,1" srcEntityName="ProductCompliance" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>refId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,17">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ComplianceCopyDocPreProcessor</implementationClass>
        </element>
        <element position="productCompliance_dataMappingRule.xlsx,productComplianceCopyDoc,18">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ProductComplianceCopyDocPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
