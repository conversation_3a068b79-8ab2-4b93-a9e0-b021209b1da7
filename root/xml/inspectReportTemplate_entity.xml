<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="inspectReportTemplate" position="inspectReportTemplate_entity.xlsx">
  <sheet id="generalInfo" position="inspectReportTemplate_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="N" main_entity="InspectReportTemplate" module="inspectReportTemplate" position="inspectReportTemplate_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
  </sheet>
  <sheet id="entityDef" position="inspectReportTemplate_entity.xlsx,entityDef">
    <Entity name="InspectReportTemplate" position="inspectReportTemplate_entity.xlsx,entityDef,1" ref_pattern="com.core.cbx.data.generator.DomainPatternSeqGenerator(&quot;CBX_SEQ_INSPT_RPT_TMPL_REF_NO&quot;,&quot;system.pattern.InspectionReportTemplateRefNo&quot;, &quot;IRTMPL#{Date:YY}-#{Seq:6}&quot;)" report_table_name="INSPECT_RPT_TEMPL" table_name="CNT_INSPECT_RPT_TEMPL" unique_field_id="name">
      <elements id="header">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,8">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>NAME</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,9">
          <entity_field_id>requiredSkill</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>INSPECTION_SKILL</data1>
          <data2/>
          <report_column_name>REQUIRED_SKILL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,10">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>INSPECTION_TEMPLATE_PRODUCT_CATEGORY</data1>
          <data2/>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,11">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,12">
          <entity_field_id>shortDescription</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHORT_DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,13">
          <entity_field_id>isAllowRealtimeAddDel</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_REALTIME_ADD_DEL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,14">
          <entity_field_id>inspectionLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECTION_LEVEL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,15">
          <entity_field_id>criticalLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CRITICAL_LEVEL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,16">
          <entity_field_id>majorLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MAJOR_LEVEL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,17">
          <entity_field_id>minorLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MINOR_LEVEL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,18">
          <entity_field_id>isAllowRealtimeUpdateSR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_REALTIME_UPDATE_SR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,19">
          <entity_field_id>isAllowManuallyOverwriteDR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_MANUALLY_OVERWRITE_DR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,20">
          <entity_field_id>isDisableDefectCode</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_DISABLE_DEFECT_CODE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,21">
          <entity_field_id>majorAsOneCriticalDefect</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MAJOR_AS_ONE_CRITICAL_DEFECT</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,22">
          <entity_field_id>minorAsOneMajorDefect</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MINOR_AS_ONE_MAJOR_DEFECT</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,23">
          <entity_field_id>isAggregateDefectCount</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_AGGREGATE_DEFECT_COUNT</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,24">
          <entity_field_id>isDiscardCalMinorInDR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_DISCARD_CAL_MINOR_IN_DR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,25">
          <entity_field_id>majorAsOneCritical</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MAJOR_AS_ONE_CRITICAL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,26">
          <entity_field_id>minorAsOneMajor</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MINOR_AS_ONE_MAJOR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,27">
          <entity_field_id>isDiscardCalMinorInMR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_DISCARD_CAL_MINOR_IN_MR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,28">
          <entity_field_id>measInspection</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEAS_INSPECTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,29">
          <entity_field_id>measCritical</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEAS_CRITICAL</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,30">
          <entity_field_id>measMajor</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEAS_MAJOR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,31">
          <entity_field_id>measMinor</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MEAS_MINOR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,32">
          <entity_field_id>isAllowManuallyOverwriteMR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_REALTIME_UPDATE_MR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,33">
          <entity_field_id>isAllowManuallyOverwriteOR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_MANUALLY_OVERWRITE_OR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,34">
          <entity_field_id>isCountMeasIntoDefect</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_COUNT_MEAS_INTO_DEFECT</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,35">
          <entity_field_id>allowUpdateMeasSR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ALLOW_UPDATE_MEAS_SR</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,36">
          <entity_field_id>factoryAuditTemplate</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>FactoryAuditTemplate.id</entity_lookup>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>FACTORY_AUDIT_TEMPLATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,37">
          <entity_field_id>factoryAuditTemplName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>FACTORY_AUDIT_TEMPL_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,38">
          <entity_field_id>isAllowAddDeleteReq</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_ADD_DEL_REQ</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,39">
          <entity_field_id>custId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Cust.id</entity_lookup>
          <entity_lookup_type>Dynamic</entity_lookup_type>
          <snapshot_field/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CUSTOMER_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,43">
          <entity_field_id>inspectReportTemplateSampleRules</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InspectReportTemplateSampleRule.inspectReportTemplateId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>1</tracking_level>
          <report_column_name/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,44">
          <entity_field_id>inspectReportTemplateSections</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InspectReportTemplateSection.inspectReportTemplateId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>1</tracking_level>
          <report_column_name/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,45">
          <entity_field_id>inspectReportTemplateItems</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InspectReportTemplateItem.inspectReportTemplateId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>1</tracking_level>
          <report_column_name/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,46">
          <entity_field_id>inspectReportTemplateRules</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InspectReportTemplateRule.inspectReportTemplateId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>1</tracking_level>
          <report_column_name/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,47">
          <entity_field_id>productCategoryList</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>2</tracking_level>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <data2/>
        </element>
      </elements>
    </Entity>
    <Entity name="InspectReportTemplateSampleRule" position="inspectReportTemplate_entity.xlsx,entityDef,50" ref_pattern="${ruleName}" report_table_name="INSPECT_RPT_T_SECTION" table_name="CNT_INSPECT_RPT_T_SAMPLE_RULE">
      <elements id="header">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,57">
          <entity_field_id>inspectReportTemplateId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_TEMPL_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,58">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,59">
          <entity_field_id>ruleName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RULE_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,60">
          <entity_field_id>isAllowRealtimeUpdateSR</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ALLOW_REALTIME_UPDATE_SR</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,61">
          <entity_field_id>inspectionLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECTION_LEVEL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,62">
          <entity_field_id>criticalLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CRITICAL_LEVEL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,63">
          <entity_field_id>majorLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MAJOR_LEVEL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,64">
          <entity_field_id>minorLevel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MINOR_LEVEL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="InspectReportTemplateSection" position="inspectReportTemplate_entity.xlsx,entityDef,67" ref_pattern="${name}" report_table_name="INSPECT_RPT_T_SECTION" table_name="CNT_INSPECT_RPT_T_SECTION">
      <elements id="header">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,74">
          <entity_field_id>inspectReportTemplateId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_TEMPL_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,75">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,76">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,77">
          <entity_field_id>isNotAvailable</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_NOT_AVAILABLE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,78">
          <entity_field_id>isCriticalShown</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_CRITICAL_SHOWN</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,79">
          <entity_field_id>isMajorShown</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_MAJOR_SHOWN</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,80">
          <entity_field_id>isMinorShown</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_MINOR_SHOWN</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,81">
          <entity_field_id>isCountForMeasDefects</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_COUNT_FOR_MEAS_DEFECTS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,82">
          <entity_field_id>sampleRuleApplied</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>InspectReportTemplateSampleRule.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_T_SAMPLE_RULE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="InspectReportTemplateItem" position="inspectReportTemplate_entity.xlsx,entityDef,85" ref_pattern="${name}" report_table_name="INSPECT_RPT_T_ITEM" table_name="CNT_INSPECT_RPT_T_ITEM">
      <elements id="header">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,92">
          <entity_field_id>inspectReportTemplateId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_TEMPL_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,93">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,94">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,95">
          <entity_field_id>section</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>InspectReportTemplateSection.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_T_SECTION_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,96">
          <entity_field_id>isNotAvailable</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_NOT_AVAILABLE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,97">
          <entity_field_id>isMeasDefectItems</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_MEAS_DEFECT_ITEMS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="InspectReportTemplateRule" position="inspectReportTemplate_entity.xlsx,entityDef,100" ref_pattern="${condition.name}" report_table_name="INSPECT_RPT_T_RULE" table_name="CNT_INSPECT_RPT_T_RULE">
      <elements id="header">
        <element position="inspectReportTemplate_entity.xlsx,entityDef,107">
          <entity_field_id>inspectReportTemplateId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>INSPECT_RPT_TEMPL_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,108">
          <entity_field_id>priority</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PRIORITY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,entityDef,109">
          <entity_field_id>condition</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Condition.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CONDITION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="inspectReportTemplate_entity.xlsx,status">
    <Status position="inspectReportTemplate_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="inspectReportTemplate_entity.xlsx,status,4">
          <code>customStatus01</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,5">
          <code>customStatus02</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,6">
          <code>customStatus03</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,7">
          <code>customStatus04</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,8">
          <code>customStatus05</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,9">
          <code>customStatus06</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,10">
          <code>customStatus07</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,11">
          <code>customStatus08</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,12">
          <code>customStatus09</code>
        </element>
        <element position="inspectReportTemplate_entity.xlsx,status,13">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
