<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="offersheet" position="offersheet_validation.xlsx">
  <sheet id="ValidationProfile" position="offersheet_validation.xlsx,ValidationProfile">
    <ValidationProfile position="offersheet_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="offersheet_validation.xlsx,ValidationProfile,4">
          <id>c5a97a7ac8ce4c99818ed1a2ce67cc53</id>
          <profileName>Default Data Validation Profile Offersheet[ver:1]</profileName>
          <entityName>Offersheet</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SaveAndConfirm,DraftStatus,OfficialStatus,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,MarkAsCustomStatus04Doc,MarkAsCustomStatus05D<PERSON>,Mark<PERSON><PERSON>ustomStatus06D<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tatus07Doc,MarkAsCustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:07.035</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="offersheet_validation.xlsx,ValidationRule">
    <ValidationRule position="offersheet_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="offersheet_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="offersheet_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="offersheet_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="offersheet_validation.xlsx,ValidationRule,7">
          <type>EmailValidator</type>
          <className>com.core.cbx.validation.validator.EmailValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="offersheet_validation.xlsx,ValidationRule,8">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="offersheet_validation.xlsx,ValidationRule,9">
          <type>HCLValidator</type>
          <className>com.core.cbx.validation.validator.HCLValidator</className>
          <condition>isHclSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="offersheet_validation.xlsx,MandatoryValidator">
    <ValidationField position="offersheet_validation.xlsx,MandatoryValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,MandatoryValidator,8">
          <entityName>Offersheet</entityName>
          <fieldId>osDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>osDate</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,9">
          <entityName>Offersheet</entityName>
          <fieldId>expiryDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>expiryDate</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,10">
          <entityName>Offersheet</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,11">
          <entityName>Offersheet</entityName>
          <fieldId>custCurrency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custCurrency</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,12">
          <entityName>Offersheet</entityName>
          <fieldId>hierarchy</fieldId>
          <condition>isHclSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>hierarchy</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,13">
          <entityName>Offersheet</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,14">
          <entityName>OsItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,15">
          <entityName>OsItem</entityName>
          <fieldId>lotNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>lotNo</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,16">
          <entityName>OsItem</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>itemDescription</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,17">
          <entityName>OsItem</entityName>
          <fieldId>uom</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>uom</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,18">
          <entityName>OsItem</entityName>
          <fieldId>weightUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>weightUOM</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,19">
          <entityName>OsItem</entityName>
          <fieldId>dimensionUOM</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>dimensionUOM</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,20">
          <entityName>OsCharge</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,21">
          <entityName>OsCharge</entityName>
          <fieldId>osItemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osCharge</GRID_ID>
          <LABEL_FIELD_ID>itemNo</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,22">
          <entityName>OsCharge</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,23">
          <entityName>OsCharge</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osCharge</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,24">
          <entityName>OsCharge</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osCharge</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,25">
          <entityName>OsChargeOnDoc</entityName>
          <fieldId>chargeType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeType</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,26">
          <entityName>OsChargeOnDoc</entityName>
          <fieldId>chargeDesc</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeDesc</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,27">
          <entityName>OsChargeOnDoc</entityName>
          <fieldId>calculateType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>calculateType</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,28">
          <entityName>OsChargeOnDoc</entityName>
          <fieldId>chargeValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osChargeOnDoc</GRID_ID>
          <LABEL_FIELD_ID>chargeValue</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,29">
          <entityName>OsAddress</entityName>
          <fieldId>addressTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osAddress</GRID_ID>
          <LABEL_FIELD_ID>addressTypeId</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,30">
          <entityName>OsAddress</entityName>
          <fieldId>address1</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osAddress</GRID_ID>
          <LABEL_FIELD_ID>address1</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,31">
          <entityName>OsAddress</entityName>
          <fieldId>country</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osAddress</GRID_ID>
          <LABEL_FIELD_ID>country</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,32">
          <entityName>OsContact</entityName>
          <fieldId>contactTypeId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osContact</GRID_ID>
          <LABEL_FIELD_ID>contactTypeId</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,33">
          <entityName>OsContact</entityName>
          <fieldId>firstName</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osContact</GRID_ID>
          <LABEL_FIELD_ID>firstName</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,34">
          <entityName>OsContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
        <element position="offersheet_validation.xlsx,MandatoryValidator,35">
          <entityName>OsAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osAttachment</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="offersheet_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="offersheet_validation.xlsx,UniqueInSectionValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>OsItem</entityName>
          <fieldId>itemId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>08010089</ERROR_ID>
          <FIELD_GROUP>itemId,lotNo</FIELD_GROUP>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="offersheet_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="offersheet_validation.xlsx,UniqueInModuleValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>Offersheet</entityName>
          <fieldId>osNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="EmailValidator" position="offersheet_validation.xlsx,EmailValidator">
    <ValidationField position="offersheet_validation.xlsx,EmailValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,EmailValidator,8">
          <entityName>OsContact</entityName>
          <fieldId>email</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osContact</GRID_ID>
          <LABEL_FIELD_ID>email</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="offersheet_validation.xlsx,ClassificationValidator">
    <ValidationField position="offersheet_validation.xlsx,ClassificationValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,ClassificationValidator,8">
          <entityName>Offersheet</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="offersheet_validation.xlsx,ClassificationValidator,9">
          <entityName>Offersheet</entityName>
          <fieldId>osItem</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>osItem</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="HCLValidator" position="offersheet_validation.xlsx,HCLValidator">
    <ValidationField position="offersheet_validation.xlsx,HCLValidator,1" profileId="c5a97a7ac8ce4c99818ed1a2ce67cc53" profileName="">
      <elements id="default">
        <element position="offersheet_validation.xlsx,HCLValidator,8">
          <entityName>Offersheet</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custId</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>custHc</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>custCode</DOCUMENT_NO>
        </element>
        <element position="offersheet_validation.xlsx,HCLValidator,9">
          <entityName>OsItem</entityName>
          <fieldId>hierarchy</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD/>
          <TYPE>gridSelectBiz</TYPE>
          <DOCUMENT_NO>itemNo</DOCUMENT_NO>
        </element>
        <element position="offersheet_validation.xlsx,HCLValidator,10">
          <entityName>OsItem</entityName>
          <fieldId>vendorId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
          <HEADER_HCL_FIELD>hierarchy</HEADER_HCL_FIELD>
          <TARGET_FIELD>hcs</TARGET_FIELD>
          <TYPE>isMasterSelection</TYPE>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
