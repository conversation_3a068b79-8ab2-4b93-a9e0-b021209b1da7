<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="termsAndConditions" position="termsAndConditions_entity.xlsx">
  <sheet id="_system" position="termsAndConditions_entity.xlsx,_system">
    <ProjectInfo client="Base" position="termsAndConditions_entity.xlsx,_system,1" project="SCMS" release_no="1.00"/>
    <ProductVersion position="termsAndConditions_entity.xlsx,_system,7">
      <elements id="default">
        <element position="termsAndConditions_entity.xlsx,_system,10">
          <updated_on>21-Jul-2020</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="termsAndConditions_entity.xlsx,generalInfo">
    <GeneralInfo is_system_entity="N" main_entity="TermsAndConditions" module="termsAndConditions" position="termsAndConditions_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
  </sheet>
  <sheet id="entityDef" position="termsAndConditions_entity.xlsx,entityDef">
    <Entity name="TermsAndConditions" position="termsAndConditions_entity.xlsx,entityDef,1" ref_pattern="${tcNo}" report_table_name="TERMS_AND_CONDITIONS" table_name="CNT_TERMS_AND_CONDITIONS" unique_field_id="tcNo">
      <elements id="header">
        <element position="termsAndConditions_entity.xlsx,entityDef,8">
          <entity_field_id>tcNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_TC","system.pattern.tcNo", "TC#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,9">
          <entity_field_id>tcName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,10">
          <entity_field_id>tcType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>TC_TYPE</data1>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,11">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,12">
          <entity_field_id>otherDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,13">
          <entity_field_id>effectiveFrom</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,14">
          <entity_field_id>effectiveTo</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,15">
          <entity_field_id>remarks</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="termsAndConditions_entity.xlsx,entityDef,16">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
      </elements>
      <elements id="collection"/>
    </Entity>
  </sheet>
  <sheet id="status" position="termsAndConditions_entity.xlsx,status">
    <Status position="termsAndConditions_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="termsAndConditions_entity.xlsx,status,4">
          <code>customStatus01</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,5">
          <code>customStatus02</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,6">
          <code>customStatus03</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,7">
          <code>customStatus04</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,8">
          <code>customStatus05</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,9">
          <code>customStatus06</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,10">
          <code>customStatus07</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,11">
          <code>customStatus08</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,12">
          <code>customStatus09</code>
        </element>
        <element position="termsAndConditions_entity.xlsx,status,13">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
