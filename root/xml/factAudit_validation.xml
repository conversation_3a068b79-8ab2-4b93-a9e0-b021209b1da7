<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="factAudit" position="factAudit_validation.xlsx">
  <sheet id="ValidationProfile" position="factAudit_validation.xlsx,ValidationProfile">
    <ValidationProfile position="factAudit_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="factAudit_validation.xlsx,ValidationProfile,4">
          <id>960c6e3d04db4a928cc9aecbd5f32383</id>
          <profileName>Default Data Validation Profile FactAudit[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactAudit</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SaveAnd<PERSON>onfirm,SendToVendor,<PERSON>To<PERSON><PERSON>er,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus04Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus05D<PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tatus06D<PERSON>,Mark<PERSON><PERSON>ustomStatus07Doc,<PERSON><PERSON><PERSON><PERSON>omStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks>FactAudit Validaation</remarks>
          <updatedOn>2012-09-14 15:05:04.643</updatedOn>
        </element>
        <element position="factAudit_validation.xlsx,ValidationProfile,5">
          <id>960c6e3d04db4a928cc9aecbd5f3aaaa</id>
          <profileName>Default Data Validation Profile FactAudit[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactAudit</entityName>
          <entityVer>1</entityVer>
          <action>ScheduledStatus</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks>FactAudit Validaation</remarks>
          <updatedOn>2012-09-14 15:05:04.643</updatedOn>
        </element>
        <element position="factAudit_validation.xlsx,ValidationProfile,6">
          <id>960c6e3d04db4a928cc9aecbd5f3bbbb</id>
          <profileName>Default Data Validation Profile FactAudit[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactAudit</entityName>
          <entityVer>1</entityVer>
          <action>CompletedStatus</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks>FactAudit Validaation</remarks>
          <updatedOn>2012-09-14 15:05:04.643</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="factAudit_validation.xlsx,ValidationRule">
    <ValidationRule position="factAudit_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="factAudit_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,6">
          <type>ClassificationValidator</type>
          <className>com.core.cbx.validation.validator.ClassificationValidator</className>
          <restapiBeanName>ClassificationValidator</restapiBeanName>
          <condition>isClassificationSecurityMode</condition>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,7">
          <type>FactoryAuditTemplateValidator</type>
          <className>com.core.cbx.factaudit.validator.FactoryAuditTemplateValidator</className>
          <restapiBeanName>FactoryAuditTemplateValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,8">
          <type>FactAuditTmpExistValidator</type>
          <className>com.core.cbx.factaudit.validator.FactAuditTmpExistValidator</className>
          <restapiBeanName>FactAuditTmpExistValidator</restapiBeanName>
          <condition>statusCompleted</condition>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,9">
          <type>GpsFieldValueValidator</type>
          <className>com.core.cbx.validation.validator.GpsFieldValueValidator</className>
          <restapiBeanName>RegexValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,10">
          <type>AttachLinkMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.AttachLinkMandatoryValidator</className>
          <restapiBeanName>AttachLinkMandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,11">
          <type>FactoryAuditNumberValidator</type>
          <className>com.core.cbx.factaudit.validator.FactoryAuditNumberValidator</className>
          <restapiBeanName>FactoryAuditNumberValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factAudit_validation.xlsx,ValidationRule,12">
          <type>ChecklistTmplMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.ChecklistTemplateMandatoryValidator</className>
          <restapiBeanName>ChecklistTmplMandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="factAudit_validation.xlsx,MandatoryValidator">
    <ValidationField position="factAudit_validation.xlsx,MandatoryValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,MandatoryValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>reportType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>reportType</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,9">
          <entityName>FactAudit</entityName>
          <fieldId>factId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>factId</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,10">
          <entityName>FactAudit</entityName>
          <fieldId>productCategory</fieldId>
          <condition>isClassificationSecurityMode</condition>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>productCategory</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,11">
          <entityName>FactoryAuditCost</entityName>
          <fieldId>costType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditCosts</GRID_ID>
          <LABEL_FIELD_ID>costType</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,12">
          <entityName>FactoryAuditCost</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditCosts</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,13">
          <entityName>FactoryAuditCost</entityName>
          <fieldId>cost</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditCosts</GRID_ID>
          <LABEL_FIELD_ID>cost</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,14">
          <entityName>FactoryAuditCost</entityName>
          <fieldId>costCurrency</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditCosts</GRID_ID>
          <LABEL_FIELD_ID>costCurrency</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,15">
          <entityName>FactAuditImage</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditImages</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,16">
          <entityName>FactAudit</entityName>
          <fieldId>plannedAuditDate</fieldId>
          <condition>statusIsCompletedOrScheduled</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>plannedAuditDate</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,17">
          <entityName>FactAudit</entityName>
          <fieldId>userId</fieldId>
          <condition>statusIsCompletedOrScheduled</condition>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>auditorUser</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,18">
          <entityName>FactAudit</entityName>
          <fieldId>auditResult</fieldId>
          <condition>statusCompleted</condition>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>auditResult</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,19">
          <entityName>FactAudit</entityName>
          <fieldId>signedAuditor</fieldId>
          <condition>statusCompleted</condition>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>signedAuditor</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="factAudit_validation.xlsx,MandatoryValidator,22" profileId="960c6e3d04db4a928cc9aecbd5f3aaaa" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,MandatoryValidator,29">
          <entityName>FactAudit</entityName>
          <fieldId>plannedAuditDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>plannedAuditDate</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,30">
          <entityName>FactAudit</entityName>
          <fieldId>userId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>auditorUser</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="factAudit_validation.xlsx,MandatoryValidator,33" profileId="960c6e3d04db4a928cc9aecbd5f3bbbb" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,MandatoryValidator,40">
          <entityName>FactAudit</entityName>
          <fieldId>plannedAuditDate</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>plannedAuditDate</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,41">
          <entityName>FactAudit</entityName>
          <fieldId>userId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>auditorUser</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,42">
          <entityName>FactAudit</entityName>
          <fieldId>auditResult</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>auditResult</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
        <element position="factAudit_validation.xlsx,MandatoryValidator,43">
          <entityName>FactAudit</entityName>
          <fieldId>signedAuditor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>N</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>signedAuditor</LABEL_FIELD_ID>
          <LABEL_PREFIX/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ClassificationValidator" position="factAudit_validation.xlsx,ClassificationValidator">
    <ValidationField position="factAudit_validation.xlsx,ClassificationValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,ClassificationValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>custId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>custIdList</LABEL_FIELD_ID>
          <TARGET_FIELD/>
          <DOCUMENT_NO>refRef</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FactoryAuditTemplateValidator" position="factAudit_validation.xlsx,FactoryAuditTemplateValidator">
    <ValidationField position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId</LABEL_FIELD_ID>
        </element>
        <element position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,9">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId2</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId2</LABEL_FIELD_ID>
        </element>
        <element position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,10">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId3</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId3</LABEL_FIELD_ID>
        </element>
        <element position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,11">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId4</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId4</LABEL_FIELD_ID>
        </element>
        <element position="factAudit_validation.xlsx,FactoryAuditTemplateValidator,12">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId5</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId5</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FactAuditTmpExistValidator" position="factAudit_validation.xlsx,FactAuditTmpExistValidator">
    <ValidationField position="factAudit_validation.xlsx,FactAuditTmpExistValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,FactAuditTmpExistValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>factoryAuditTemplateId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>factoryAuditTemplateId</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="GpsFieldValueValidator" position="factAudit_validation.xlsx,GpsFieldValueValidator">
    <ValidationField position="factAudit_validation.xlsx,GpsFieldValueValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,GpsFieldValueValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>gpsCoodinateLng</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>gpsCoodinateLng</LABEL_FIELD_ID>
          <REGEX>^[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$</REGEX>
          <ERROR_ID>REF160</ERROR_ID>
        </element>
        <element position="factAudit_validation.xlsx,GpsFieldValueValidator,9">
          <entityName>FactAudit</entityName>
          <fieldId>gpsCoodinateLat</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>gpsCoodinateLat</LABEL_FIELD_ID>
          <REGEX>^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?)$</REGEX>
          <ERROR_ID>REF160</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="AttachLinkMandatoryValidator" position="factAudit_validation.xlsx,AttachLinkMandatoryValidator">
    <ValidationField position="factAudit_validation.xlsx,AttachLinkMandatoryValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,AttachLinkMandatoryValidator,8">
          <entityName>FactAuditAttachment</entityName>
          <fieldId>fileId</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditAttachments</GRID_ID>
          <LABEL_FIELD_ID>fileId</LABEL_FIELD_ID>
          <validateBaseField>fileAddress</validateBaseField>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FactoryAuditNumberValidator" position="factAudit_validation.xlsx,FactoryAuditNumberValidator">
    <ValidationField position="factAudit_validation.xlsx,FactoryAuditNumberValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,FactoryAuditNumberValidator,8">
          <entityName>FactoryAuditScorings</entityName>
          <fieldId>statusScore</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LESS_THAN_FIELD_ID>maximumScore</LESS_THAN_FIELD_ID>
          <ERROR_ID>13040011</ERROR_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ChecklistTmplMandatoryValidator" position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator">
    <ValidationField position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,8">
          <entityName>FactoryAuditChecklists</entityName>
          <fieldId>factAuditObservationsComments</fieldId>
          <condition/>
          <realField>Observations</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditChecklists</GRID_ID>
          <LABEL_FIELD_ID>factAuditObservationsComments</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,9">
          <entityName>FactoryAuditScorings</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Status Score</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditScorings</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,10">
          <entityName>FactoryAuditRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Requirement Options</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditRequirements</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,11">
          <entityName>FactoryAuditRequirement2</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Requirement Options</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditRequirements2</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,12">
          <entityName>FactoryAuditRequirement3</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Requirement Options</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditRequirements3</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,13">
          <entityName>FactoryAuditRequirement4</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Requirement Options</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditRequirements4</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
        <element position="factAudit_validation.xlsx,ChecklistTmplMandatoryValidator,14">
          <entityName>FactoryAuditRequirement5</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <realField>Requirement Options</realField>
          <descField>description</descField>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditRequirements5</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
          <HIDE_LABEL>TRUE</HIDE_LABEL>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="factAudit_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="factAudit_validation.xlsx,UniqueInModuleValidator,1" profileId="960c6e3d04db4a928cc9aecbd5f32383" profileName="">
      <elements id="default">
        <element position="factAudit_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>FactAudit</entityName>
          <fieldId>reportNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
