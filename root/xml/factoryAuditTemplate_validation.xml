<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="factoryAuditTemplate" position="factoryAuditTemplate_validation.xlsx">
  <sheet id="ValidationProfile" position="factoryAuditTemplate_validation.xlsx,ValidationProfile">
    <ValidationProfile position="factoryAuditTemplate_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,ValidationProfile,4">
          <id>7ff23d6145a34f6da497b516e4fa5d7e</id>
          <profileName>Default Data Validation Profile FactAuditTemplate[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactoryAuditTemplate</entityName>
          <entityVer>1</entityVer>
          <action>SaveAndConfirm,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,<PERSON><PERSON><PERSON>ustomStatus04Doc,Mark<PERSON><PERSON>ustomStatus05Doc,<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus06Doc,MarkAsCustomStatus07Doc,<PERSON><PERSON><PERSON>ustomStatus08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority>0</priority>
          <maxError>0</maxError>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:07.230</updatedOn>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationProfile,5">
          <id>7ff33d6145a34f6da497b516e4fa5d4e</id>
          <profileName>Default Data Validation Profile FactAuditTemplate[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactoryAuditTemplate</entityName>
          <entityVer>1</entityVer>
          <action>PopFactoryAuditTemplateDefineColumnOkAction</action>
          <condition/>
          <priority>0</priority>
          <maxError>0</maxError>
          <consolidateError/>
          <ignoreCustomField>Y</ignoreCustomField>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2012-09-14 15:05:07.231</updatedOn>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationProfile,6">
          <id>d015c87621b446c986674de32047beff</id>
          <profileName>Default Data Validation Profile FactAuditTemplate[ver:1]</profileName>
          <inheritFrom/>
          <entityName>FactoryAuditTemplate</entityName>
          <entityVer>1</entityVer>
          <action>PopFactoryAuditTemplateDefineOptionOkAction</action>
          <condition/>
          <priority>0</priority>
          <maxError>0</maxError>
          <consolidateError/>
          <ignoreCustomField>Y</ignoreCustomField>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>2018-03-21 15:05:07.231</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="factoryAuditTemplate_validation.xlsx,ValidationRule">
    <ValidationRule position="factoryAuditTemplate_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,7">
          <type>FactAuditTmpTableValidator</type>
          <className>com.core.cbx.factoryaudittemplate.validator.FactAuditTmpTableValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,8">
          <type>FormPopUpMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,9">
          <type>NumericRangeValidator</type>
          <className>com.core.cbx.validation.validator.NumericRangeValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,10">
          <type>NumberGreaterThanMinValidator</type>
          <className>com.core.cbx.validation.validator.NumberGreaterThanMinValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,ValidationRule,11">
          <type>FactAuditTmplDetailsValidator</type>
          <className>com.core.cbx.factoryaudittemplate.validator.FactAuditTmplDetailsValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="factoryAuditTemplate_validation.xlsx,MandatoryValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,8">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>name</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>name</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,9">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>applyTo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>applyTo</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,10">
          <entityName>FactoryAuditTemplateSection</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateSections</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,11">
          <entityName>FactoryAuditTemplateSection</entityName>
          <fieldId>name</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateSections</GRID_ID>
          <LABEL_FIELD_ID>name</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,12">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>factoryAuditTemplateSections</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>factoryAuditTemplateSections</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,13">
          <entityName>FactoryAuditTemplateRequirement</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRequirements</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,14">
          <entityName>FactoryAuditTemplateRequirement</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRequirements</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,15">
          <entityName>FactoryAuditTemplateRequirement</entityName>
          <fieldId>factoryAuditTemplateSection</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRequirements</GRID_ID>
          <LABEL_FIELD_ID>factoryAuditTemplateSection</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,16">
          <entityName>FactoryAuditTemplateRequirement</entityName>
          <fieldId>maximumScore</fieldId>
          <condition>isScoreCalculationManualInput</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRequirements</GRID_ID>
          <LABEL_FIELD_ID>maximumScore</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,17">
          <entityName>FactoryAuditTemplateChecklist</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateChecklists</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,18">
          <entityName>FactoryAuditTemplateChecklist</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateChecklists</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,19">
          <entityName>FactoryAuditTemplateChecklist</entityName>
          <fieldId>factoryAuditTemplateSection</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateChecklists</GRID_ID>
          <LABEL_FIELD_ID>section</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,20">
          <entityName>FactoryAuditTemplateScoring</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateScorings</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,21">
          <entityName>FactoryAuditTemplateScoring</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateScorings</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,22">
          <entityName>FactoryAuditTemplateScoring</entityName>
          <fieldId>factoryAuditTemplateSection</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateScorings</GRID_ID>
          <LABEL_FIELD_ID>factoryAuditTemplateSection</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,23">
          <entityName>FactoryAuditTemplateScoring</entityName>
          <fieldId>maximumScore</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateScorings</GRID_ID>
          <LABEL_FIELD_ID>maximumScore</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,26" profileId="7ff33d6145a34f6da497b516e4fa5d4e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,MandatoryValidator,33">
          <entityName>FactoryAuditTemplateColumn</entityName>
          <fieldId>label</fieldId>
          <condition>isDisabled</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateColumns</GRID_ID>
          <LABEL_FIELD_ID>label</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="factoryAuditTemplate_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,UniqueInModuleValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>name</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumericRangeValidator" position="factoryAuditTemplate_validation.xlsx,NumericRangeValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,NumericRangeValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,NumericRangeValidator,8">
          <entityName>FactoryAuditTemplateScoring</entityName>
          <fieldId>maximumScore</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <MIN_VALUE>0</MIN_VALUE>
          <GREATE_THAN_VALUE/>
          <GRID_ID>maximumScore</GRID_ID>
          <LABEL_FIELD_ID>maximumScore</LABEL_FIELD_ID>
          <ERROR_ID/>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="factoryAuditTemplate_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,UniqueInSectionValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>FactoryAuditTemplateRule</entityName>
          <fieldId>condition</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRules</GRID_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FactAuditTmpTableValidator" position="factoryAuditTemplate_validation.xlsx,FactAuditTmpTableValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,FactAuditTmpTableValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,FactAuditTmpTableValidator,8">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>tableReqChecklist</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>tableReqChecklist</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FormPopUpMandatoryValidator" position="factoryAuditTemplate_validation.xlsx,FormPopUpMandatoryValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,FormPopUpMandatoryValidator,1" profileId="d015c87621b446c986674de32047beff" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,FormPopUpMandatoryValidator,8">
          <entityName>FactoryAuditTemplateOption</entityName>
          <fieldId>description</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateOptions</GRID_ID>
          <LABEL_FIELD_ID>description</LABEL_FIELD_ID>
        </element>
        <element position="factoryAuditTemplate_validation.xlsx,FormPopUpMandatoryValidator,9">
          <entityName>FactoryAuditTemplateOption</entityName>
          <fieldId>seqNo</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateOptions</GRID_ID>
          <LABEL_FIELD_ID>seqNo</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="NumberGreaterThanMinValidator" position="factoryAuditTemplate_validation.xlsx,NumberGreaterThanMinValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,NumberGreaterThanMinValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,NumberGreaterThanMinValidator,8">
          <entityName>FactoryAuditTemplate</entityName>
          <fieldId>totalMaximumScore</fieldId>
          <condition/>
          <conditionType/>
          <ERROR_ID>CustomizedErrorId</ERROR_ID>
          <MIN_VALUE>0</MIN_VALUE>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="FactAuditTmplDetailsValidator" position="factoryAuditTemplate_validation.xlsx,FactAuditTmplDetailsValidator">
    <ValidationField position="factoryAuditTemplate_validation.xlsx,FactAuditTmplDetailsValidator,1" profileId="7ff23d6145a34f6da497b516e4fa5d7e" profileName="">
      <elements id="default">
        <element position="factoryAuditTemplate_validation.xlsx,FactAuditTmplDetailsValidator,8">
          <entityName>FactoryAuditTemplateRequirement</entityName>
          <fieldId>defaultValue</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>factoryAuditTemplateRequirements</GRID_ID>
          <LABEL_FIELD_ID>defaultValue</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
