<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataInheritanceProfile module="testMethod" position="testMethod_dataInheritanceProfile.xlsx">
  <sheet id="DIP_test_dip_profile" position="testMethod_dataInheritanceProfile.xlsx,DIP_test_dip_profile">
    <DataInheritanceProfile position="testMethod_dataInheritanceProfile.xlsx,DIP_test_dip_profile,1">
      <elements id="dataInheritanceProfile">
        <element position="testMethod_dataInheritanceProfile.xlsx,DIP_test_dip_profile,4">
          <id>testMethodCopyDoc</id>
          <actionId>testMethodCopyDoc</actionId>
          <dmrName>testMethodCopyDoc</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>TestMethod</srcEntityName>
          <dstEntityName>TestMethod</dstEntityName>
          <domain>/</domain>
          <dstCondition/>
          <updatedDate>2012-10-19</updatedDate>
          <sequence>5.1</sequence>
        </element>
        <element position="testMethod_dataInheritanceProfile.xlsx,DIP_test_dip_profile,5">
          <id>testProtocolSelectTestMethod</id>
          <actionId>testProtocolSelectTestMethod</actionId>
          <dmrName>testProtocolSelectTestMethod</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>TestMethod</srcEntityName>
          <dstEntityName>TestProtocolTemplate</dstEntityName>
          <domain>/</domain>
          <dstCondition/>
          <updatedDate>2012-10-19</updatedDate>
          <sequence>5.1</sequence>
        </element>
        <element position="testMethod_dataInheritanceProfile.xlsx,DIP_test_dip_profile,6">
          <id>testReportSelectTestMethod</id>
          <actionId>testReportSelectTestMethod</actionId>
          <dmrName>testReportSelectTestMethod</dmrName>
          <dmrVersion>1</dmrVersion>
          <srcEntityName>TestMethod</srcEntityName>
          <dstEntityName>TestReport</dstEntityName>
          <domain>/</domain>
          <dstCondition/>
          <updatedDate>2012-10-19</updatedDate>
          <sequence>5.1</sequence>
        </element>
      </elements>
    </DataInheritanceProfile>
  </sheet>
</dataInheritanceProfile>
