<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="testProtocolTemplate" position="testProtocolTemplate_entity.xlsx">
  <sheet id="_system" position="testProtocolTemplate_entity.xlsx,_system">
    <ProjectInfo client="Base" position="testProtocolTemplate_entity.xlsx,_system,1" project="" release_no="1.00"/>
    <ProductVersion position="testProtocolTemplate_entity.xlsx,_system,7">
      <elements id="default">
        <element position="testProtocolTemplate_entity.xlsx,_system,10">
          <updated_on>14-七月-2015</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="testProtocolTemplate_entity.xlsx,generalInfo">
    <GeneralInfo custom_table_name="CTM_TEST_PROTOCOL_TEMPLATE" is_for_external="Y" is_system_entity="N" main_entity="TestProtocolTemplate" module="testProtocolTemplate" position="testProtocolTemplate_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
    <CustomField position="testProtocolTemplate_entity.xlsx,generalInfo,8">
      <elements id="default">
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,11">
          <custom_field_type>Text</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,12">
          <custom_field_type>MemoText</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,13">
          <custom_field_type>Codelist</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,14">
          <custom_field_type>Number</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,15">
          <custom_field_type>Decimal</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,16">
          <custom_field_type>Date</custom_field_type>
          <count>7</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,17">
          <custom_field_type>Hcl</custom_field_type>
          <count>5</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,18">
          <custom_field_type>Checkbox</custom_field_type>
          <count>10</count>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,generalInfo,19">
          <custom_field_type>Selection</custom_field_type>
          <count>5</count>
        </element>
      </elements>
    </CustomField>
  </sheet>
  <sheet id="entityDef" position="testProtocolTemplate_entity.xlsx,entityDef">
    <Entity name="TestProtocolTemplate" position="testProtocolTemplate_entity.xlsx,entityDef,1" ref_pattern="com.core.cbx.data.generator.DomainPatternSeqGenerator(&quot;CBX_SEQ_TEST_TEMPL_REF_NO&quot;,&quot;system.pattern.TestProtocolTemplateRefNo&quot;, &quot;TPT#{Date:YY}-#{Seq:6}&quot;)" report_table_name="TEST_PROTOCOL_TEMPLATE" table_name="CNT_TEST_PROTOCOL_TEMPLATE" unique_field_id="name">
      <elements id="reference"/>
      <elements id="header">
        <element position="testProtocolTemplate_entity.xlsx,entityDef,11">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,12">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
        </element>
      </elements>
      <elements id="collection">
        <element position="testProtocolTemplate_entity.xlsx,entityDef,16">
          <entity_field_id>testProtocolTemplateDetail</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>TestProtocolTemplateDetail.testProtocolTemplateId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>TEST_TEMPLATE_DETAIL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="TestProtocolTemplateDetail" position="testProtocolTemplate_entity.xlsx,entityDef,19" ref_pattern="" report_table_name="TEST_PROTOCOL_TEMPLATE_DETAIL" table_name="CNT_TEST_PROTOCOL_TEMPLATE_DETAIL">
      <elements id="header">
        <element position="testProtocolTemplate_entity.xlsx,entityDef,26">
          <entity_field_id>testProtocolTemplateId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TEST_PROTOCO_TEMPLATE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,27">
          <entity_field_id>testMethod</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>TestMethod.id</entity_lookup>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TEST_METHOD_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields>testNo,testName,description,testMethod,testType,testRequirements,testResultType,targetTestValue,minimumTolerance,maximumTolerance</transitive_fields>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,28">
          <entity_field_id>isMandatory</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_MANDATORY</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,29">
          <entity_field_id>testResultType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>LAB_TEST_RESULT_TYPE</data1>
          <data2/>
          <report_column_name>TEST_RESULT_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,30">
          <entity_field_id>targetTestValue</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TARGET_TEST_VALUE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,31">
          <entity_field_id>minimumTolerance</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MINIMUM_TOLERANCE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,32">
          <entity_field_id>maxiumumTolerance</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MAXIUMUM_TOLERANCE</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,33">
          <entity_field_id>isDefault</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_DEFAULT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,entityDef,34">
          <entity_field_id>testResultUnit</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>LAB_TEST_RESULT_UNIT</data1>
          <data2/>
          <report_column_name>TEST_RESULT_UNIT</report_column_name>
          <tracking_level>1</tracking_level>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="testProtocolTemplate_entity.xlsx,status">
    <Status position="testProtocolTemplate_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="testProtocolTemplate_entity.xlsx,status,4">
          <code>customStatus01</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,5">
          <code>customStatus02</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,6">
          <code>customStatus03</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,7">
          <code>customStatus04</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,8">
          <code>customStatus05</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,9">
          <code>customStatus06</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,10">
          <code>customStatus07</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,11">
          <code>customStatus08</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,12">
          <code>customStatus09</code>
        </element>
        <element position="testProtocolTemplate_entity.xlsx,status,13">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
