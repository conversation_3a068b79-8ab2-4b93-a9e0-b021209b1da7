<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity_security module="materialPalette" position="materialPalette_entity_security.xlsx">
  <sheet id="_system" position="materialPalette_entity_security.xlsx,_system">
    <ProjectInfo domain="$root" position="materialPalette_entity_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="materialPalette_entity_security.xlsx,_system,7">
      <elements id="default">
        <element position="materialPalette_entity_security.xlsx,_system,10">
          <updatedOn>2014/Jan/14</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="materialPalette_entity_security.xlsx,generalInfo">
    <GeneralInfo position="materialPalette_entity_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="materialPalette_entity_security.xlsx,condition">
    <ConditionList position="materialPalette_entity_security.xlsx,condition,1">
      <elements id="default">
        <element position="materialPalette_entity_security.xlsx,condition,4">
          <conditionId>$ANY</conditionId>
        </element>
        <element position="materialPalette_entity_security.xlsx,condition,5">
          <conditionId>matchAnyClassification</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="materialPalette_entity_security.xlsx,default">
    <DataConditionMatrix position="materialPalette_entity_security.xlsx,default,1">
      <elements id="default">
        <element position="materialPalette_entity_security.xlsx,default,4">
          <matchAnyClassification>editable</matchAnyClassification>
        </element>
      </elements>
    </DataConditionMatrix>
  </sheet>
  <sheet id="acl" position="materialPalette_entity_security.xlsx,acl">
    <DataRule position="materialPalette_entity_security.xlsx,acl,1">
      <elements id="default">
        <element position="materialPalette_entity_security.xlsx,acl,4">
          <conditionId>$ANY</conditionId>
          <SuperAdministratorGroup>editable</SuperAdministratorGroup>
          <GeneralAdministratorGroup>editable</GeneralAdministratorGroup>
          <ClientAdministratorGroup>editable</ClientAdministratorGroup>
          <MANAGERS>editable</MANAGERS>
          <USERS>editable</USERS>
          <BUYER_GRP>editable</BUYER_GRP>
          <DESIG_GRP>editable</DESIG_GRP>
          <QA_GRP>editable</QA_GRP>
          <SMER_GRP>editable</SMER_GRP>
          <MER_GRP>editable</MER_GRP>
          <FIN_GRP>editable</FIN_GRP>
          <SYS_GRP>editable</SYS_GRP>
          <DOMS_GRP>editable</DOMS_GRP>
          <DOMA_GRP>editable</DOMA_GRP>
          <LOGIS_GRP>editable</LOGIS_GRP>
          <MD_GRP>editable</MD_GRP>
          <GAL_GRP>editable</GAL_GRP>
          <MAL_GRP>editable</MAL_GRP>
          <vendors>readonly</vendors>
          <facts>editable</facts>
          <custs>editable</custs>
          <forwarders>editable</forwarders>
          <READONLY_GRP>readonly</READONLY_GRP>
          <EDITOR>editable</EDITOR>
          <READER>readonly</READER>
        </element>
      </elements>
    </DataRule>
  </sheet>
</entity_security>
