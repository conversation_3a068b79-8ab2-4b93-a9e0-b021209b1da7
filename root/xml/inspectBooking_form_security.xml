<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="inspectBooking" position="inspectBooking_form_security.xlsx">
  <sheet id="_system" position="inspectBooking_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="inspectBooking_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="inspectBooking_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/20</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="inspectBooking_form_security.xlsx,generalInfo">
    <GeneralInfo position="inspectBooking_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="inspectBooking_form_security.xlsx,condition">
    <ConditionList position="inspectBooking_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,condition,4">
          <conditionId>statusDraft</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,5">
          <conditionId>statusCompleted</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,6">
          <conditionId>statusScheduled</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,7">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,8">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,9">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,10">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,11">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,12">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,13">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,14">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,15">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,16">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,17">
          <conditionId>isPlannedInpectDateCondition</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,18">
          <conditionId>isExternalDomain</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,19">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="inspectBooking_form_security.xlsx,condition,20">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="inspectBooking_form_security.xlsx,default">
    <ActionConditionMatrix position="inspectBooking_form_security.xlsx,default,1">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,5">
          <actionId>newInspectReport</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCompleted>disallowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,6">
          <actionId>editDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,7">
          <actionId>amendDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,8">
          <actionId>saveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,9">
          <actionId>baseSaveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,10">
          <actionId>saveAndConfirm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,11">
          <actionId>sendToVendor</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,12">
          <actionId>sendToBuyer</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,13">
          <actionId>inspectBookingViewCapa</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,14">
          <actionId>discardDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,15">
          <actionId>updateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>disallowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,16">
          <actionId>scheduledStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>disallowed</statusCompleted>
          <statusScheduled>disallowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,17">
          <actionId>completedStatus</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCompleted>disallowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,18">
          <actionId>copyDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,19">
          <actionId>activateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,20">
          <actionId>deactivateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,21">
          <actionId>cancelDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>disallowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,22">
          <actionId>loadDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,23">
          <actionId>initializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>disallowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,24">
          <actionId>downloadQcPack</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>disallowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,25">
          <actionId>loadAQLDDStore</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,26">
          <actionId>loadInspectionLevelDDStore</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,27">
          <actionId>customPrint01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,28">
          <actionId>customPrint02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,29">
          <actionId>customPrint03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,30">
          <actionId>customPrint04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,31">
          <actionId>customPrint05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,32">
          <actionId>customPrint06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,33">
          <actionId>customPrint07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,34">
          <actionId>customPrint08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,35">
          <actionId>customPrint09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,36">
          <actionId>customPrint10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,37">
          <actionId>customExport01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,38">
          <actionId>customExport02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,39">
          <actionId>customExport03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,40">
          <actionId>customExport04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,41">
          <actionId>customExport05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,42">
          <actionId>customExport06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,43">
          <actionId>customExport07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,44">
          <actionId>customExport08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,45">
          <actionId>customExport09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,46">
          <actionId>customExport10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,47">
          <actionId>inspectBookingCustom01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,48">
          <actionId>inspectBookingCustom02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,49">
          <actionId>inspectBookingCustom03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,50">
          <actionId>inspectBookingCustom04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,51">
          <actionId>inspectBookingCustom05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,52">
          <actionId>inspectBookingCustom06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,53">
          <actionId>inspectBookingCustom07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,54">
          <actionId>inspectBookingCustom08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,55">
          <actionId>inspectBookingCustom09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,56">
          <actionId>inspectBookingCustom10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,57">
          <actionId>markAsCustomStatus01Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,58">
          <actionId>markAsCustomStatus02Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,59">
          <actionId>markAsCustomStatus03Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,60">
          <actionId>markAsCustomStatus04Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,61">
          <actionId>markAsCustomStatus05Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,62">
          <actionId>markAsCustomStatus06Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,63">
          <actionId>markAsCustomStatus07Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,64">
          <actionId>markAsCustomStatus08Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,65">
          <actionId>markAsCustomStatus09Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,66">
          <actionId>markAsCustomStatus10Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,67">
          <actionId>reinitializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,68">
          <actionId>refreshCpmTemplate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,69">
          <actionId>refreshCpmPlanDate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCompleted>allowed</statusCompleted>
          <statusScheduled>allowed</statusScheduled>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isPlannedInpectDateCondition>allowed</isPlannedInpectDateCondition>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="inspectBooking_form_security.xlsx,default,72">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,default,75">
          <componentId>ui</componentId>
          <statusDraft>editable</statusDraft>
          <statusCompleted>editable</statusCompleted>
          <statusScheduled>editable</statusScheduled>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,76">
          <componentId>ui.tabHeader.hierarchySection</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,77">
          <componentId>ui.tabHeader.inspectBookingItems.hierarchy</componentId>
          <statusDraft>readonly</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>readonly</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,78">
          <componentId>ui.inspectBookingLinkbar.approval</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,79">
          <componentId>ui.inspectBookingLinkbar.duplicateWindow</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,80">
          <componentId>ui.inspectBookingLinkbar.openForum</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,81">
          <componentId>ui.inspectBookingLinkbar.addToFavorites</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,82">
          <componentId>ui.inspectBookingLinkbar.followDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,83">
          <componentId>ui.inspectBookingLinkbar.unfollowDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,84">
          <componentId>ui.inspectBookingMenubar.printDoc</componentId>
          <statusDraft>hidden</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>hidden</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,85">
          <componentId>ui.inspectBookingMenubar.editDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="inspectBooking_form_security.xlsx,default,86">
          <componentId>ui.inspectBookingMenubar.amendDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCompleted>inherit</statusCompleted>
          <statusScheduled>inherit</statusScheduled>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="inspectBooking_form_security.xlsx,acl">
    <ActionRule position="inspectBooking_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,5">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,6">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,7">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,8">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,9">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,10">
          <actionId>sendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,11">
          <actionId>sendToBuyer</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,12">
          <actionId>inspectBookingViewCapa</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,13">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,14">
          <actionId>updateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,15">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,16">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,17">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,18">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,19">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,20">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,21">
          <actionId>loadAQLDDStore</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,22">
          <actionId>loadInspectionLevelDDStore</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,23">
          <actionId>scheduledStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,24">
          <actionId>completedStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,25">
          <actionId>newInspectReport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>has</inspectBooking.Author>
          <inspectBooking.Editor>has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,26">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,27">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,28">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,29">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,30">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,31">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,32">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,33">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,34">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,35">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,36">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,37">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,38">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,39">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,40">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,41">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,42">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,43">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,44">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,45">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,46">
          <actionId>inspectBookingCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,47">
          <actionId>inspectBookingCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,48">
          <actionId>inspectBookingCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,49">
          <actionId>inspectBookingCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,50">
          <actionId>inspectBookingCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,51">
          <actionId>inspectBookingCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,52">
          <actionId>inspectBookingCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,53">
          <actionId>inspectBookingCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,54">
          <actionId>inspectBookingCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,55">
          <actionId>inspectBookingCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,56">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,57">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,58">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,59">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,60">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,61">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,62">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,63">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,64">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,65">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,66">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,67">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,68">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectBooking.Author>not-has</inspectBooking.Author>
          <inspectBooking.Editor>not-has</inspectBooking.Editor>
          <inspectBooking.ReadOnly>not-has</inspectBooking.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="inspectBooking_form_security.xlsx,acl,71">
      <elements id="default">
        <element position="inspectBooking_form_security.xlsx,acl,74">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,75">
          <componentId>ui.tabAttach</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,76">
          <componentId>ui.tabHeader.vendorInformationSection.vendor</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,77">
          <componentId>ui.inspectBookingMenubar.saveAndConfirm</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,78">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectBooking_form_security.xlsx,acl,79">
          <componentId>ui.tabParties</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="inspectBooking_form_security.xlsx,acl,82">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
