<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="personalizeView" position="personalizeView_form_security.xlsx">
  <sheet id="_system" position="personalizeView_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="personalizeView_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="personalizeView_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,_system,10">
          <updatedOn>2013/April/02</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="personalizeView_form_security.xlsx,generalInfo">
    <GeneralInfo position="personalizeView_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="personalizeView_form_security.xlsx,condition">
    <ConditionList position="personalizeView_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,6">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,7">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,8">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,9">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,10">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,11">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="personalizeView_form_security.xlsx,condition,12">
          <conditionId>isListingView</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="personalizeView_form_security.xlsx,default">
    <ActionConditionMatrix position="personalizeView_form_security.xlsx,default,1">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,default,4">
          <actionId>amendDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,5">
          <actionId>saveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,6">
          <actionId>discardDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,7">
          <actionId>loadDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,8">
          <actionId>personalizeViewCustom01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,9">
          <actionId>personalizeViewCustom02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,10">
          <actionId>personalizeViewCustom03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,11">
          <actionId>personalizeViewCustom04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,12">
          <actionId>personalizeViewCustom05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,13">
          <actionId>personalizeViewCustom06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,14">
          <actionId>personalizeViewCustom07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,15">
          <actionId>personalizeViewCustom08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,16">
          <actionId>personalizeViewCustom09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,17">
          <actionId>personalizeViewCustom10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,18">
          <actionId>customExport01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,19">
          <actionId>customExport02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,20">
          <actionId>customExport03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,21">
          <actionId>customExport04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,22">
          <actionId>customExport05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,23">
          <actionId>customExport06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,24">
          <actionId>customExport07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,25">
          <actionId>customExport08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,26">
          <actionId>customExport09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,27">
          <actionId>customExport10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,28">
          <actionId>customPrint01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,29">
          <actionId>customPrint02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,30">
          <actionId>customPrint03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,31">
          <actionId>customPrint04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,32">
          <actionId>customPrint05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,33">
          <actionId>customPrint06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,34">
          <actionId>customPrint07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,35">
          <actionId>customPrint08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,36">
          <actionId>customPrint09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,37">
          <actionId>customPrint10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,38">
          <actionId>reinitializeCpm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,39">
          <actionId>refreshCpmTemplate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="personalizeView_form_security.xlsx,default,40">
          <actionId>refreshCpmPlanDate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="personalizeView_form_security.xlsx,default,43">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,default,46">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isListingView>editable</isListingView>
        </element>
        <element position="personalizeView_form_security.xlsx,default,47">
          <componentId>ui.personalizeViewLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isListingView>inherit</isListingView>
        </element>
        <element position="personalizeView_form_security.xlsx,default,48">
          <componentId>ui.tabHeader.personalizeViewRules.targetModule</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isListingView>readonly</isListingView>
        </element>
        <element position="personalizeView_form_security.xlsx,default,49">
          <componentId>ui.tabHeader.personalizeViewRules.targetElement</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isListingView>readonly</isListingView>
        </element>
        <element position="personalizeView_form_security.xlsx,default,50">
          <componentId>ui.personalizeViewMenubar.amendDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isListingView>inherit</isListingView>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="personalizeView_form_security.xlsx,acl">
    <ActionRule position="personalizeView_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,acl,4">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>has</personalizeView.Author>
          <personalizeView.Editor>has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,5">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>has</personalizeView.Author>
          <personalizeView.Editor>has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,6">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>has</personalizeView.Author>
          <personalizeView.Editor>has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,7">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <personalizeView.Author>has</personalizeView.Author>
          <personalizeView.Editor>has</personalizeView.Editor>
          <personalizeView.ReadOnly>has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,8">
          <actionId>personalizeViewCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,9">
          <actionId>personalizeViewCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,10">
          <actionId>personalizeViewCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,11">
          <actionId>personalizeViewCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,12">
          <actionId>personalizeViewCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,13">
          <actionId>personalizeViewCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,14">
          <actionId>personalizeViewCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,15">
          <actionId>personalizeViewCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,16">
          <actionId>personalizeViewCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,17">
          <actionId>personalizeViewCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,18">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,19">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,20">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,21">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,22">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,23">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,24">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,25">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,26">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,27">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,28">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,29">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,30">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,31">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,32">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,33">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,34">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,35">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,36">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,37">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,38">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,39">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,40">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <personalizeView.Author>not-has</personalizeView.Author>
          <personalizeView.Editor>not-has</personalizeView.Editor>
          <personalizeView.ReadOnly>not-has</personalizeView.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="personalizeView_form_security.xlsx,acl,43">
      <elements id="default">
        <element position="personalizeView_form_security.xlsx,acl,46">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="personalizeView_form_security.xlsx,acl,47">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="personalizeView_form_security.xlsx,acl,50">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
