<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form module="user" position="user_form.xlsx">
  <sheet id="_system" position="user_form.xlsx,_system">
    <ProjectInfo client="cnt" position="user_form.xlsx,_system,1" project="base" releaseNo="1.0a"/>
    <ProductVersion position="user_form.xlsx,_system,7">
      <elements id="default">
        <element position="user_form.xlsx,_system,10">
          <updatedOn>22/2/2012</updatedOn>
          <summary>Creation</summary>
          <releaseNo>01-Jan-1900</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="form" position="user_form.xlsx,form">
    <Form id="userForm" label="User" module="user" position="user_form.xlsx,form,1" version="1"/>
    <TabGroup id="userTabGroup" label="" position="user_form.xlsx,form,7">
      <elements id="default">
        <element position="user_form.xlsx,form,14">
          <id>tabHeader</id>
          <label>Header</label>
          <type>Tab</type>
        </element>
        <element position="user_form.xlsx,form,15">
          <id>tabImage</id>
          <label>Images &amp; Attachments</label>
          <type>Tab</type>
        </element>
      </elements>
    </TabGroup>
    <Toolbar id="userToolbar" label="" position="user_form.xlsx,form,18">
      <elements id="default">
        <element position="user_form.xlsx,form,25">
          <id>userMenubar</id>
          <label/>
          <type>Menubar</type>
        </element>
      </elements>
    </Toolbar>
    <Menubar align="left" cssClass="cbx-userMenubar" id="userMenubar" label="" position="user_form.xlsx,form,28">
      <elements id="default">
        <element position="user_form.xlsx,form,35">
          <id>newDoc</id>
          <label>Create</label>
          <type>MenuItem</type>
          <action>NewDocAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,36">
          <id>amendDoc</id>
          <label>Amend</label>
          <type>MenuItem</type>
          <action>AmendDocAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,37">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>MenuItem</type>
          <action>UserSaveAndConfirmAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,38">
          <id>discardDoc</id>
          <label>Cancel</label>
          <type>MenuItem</type>
          <action>DiscardDocAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,39">
          <id>printGroup</id>
          <label>Print</label>
          <type>MenuGroup</type>
          <action>printGroup</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,40">
          <id>exportGroup</id>
          <label>Export</label>
          <type>MenuGroup</type>
          <action>exportGroup</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,41">
          <id>actionsGroup</id>
          <label>Actions</label>
          <type>MenuGroup</type>
          <action>actionsGroup</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,42">
          <id>initializeCpm</id>
          <label>Initialize CPM</label>
          <type>MenuItem</type>
          <action>InitializeCpmAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,form,43">
          <id>moreGroup</id>
          <label>More</label>
          <type>MenuGroup</type>
          <action>moreGroup</action>
          <actionParams/>
        </element>
      </elements>
    </Menubar>
    <Header position="user_form.xlsx,form,46">
      <elements id="default">
        <element position="user_form.xlsx,form,49">
          <id>docStatus</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>inactive:(inactive),active:,canceled:(canceled)</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,form,50">
          <id>userId</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>{loginId} - {firstName} {lastName}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,form,51">
          <id>version</id>
          <label>Version</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format>{version}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,form,52">
          <id>headerIntegration</id>
          <label/>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,form,53">
          <id>userLinkbar</id>
          <label/>
          <type>Linkbar</type>
          <align/>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
      </elements>
    </Header>
    <MenuGroup id="actionsGroup" label="Actions" position="user_form.xlsx,form,56">
      <elements id="default">
        <element position="user_form.xlsx,form,63">
          <id>copyDoc</id>
          <label>Copy</label>
          <type>MenuItem</type>
          <action>CopyDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,64">
          <id>[BLANK]</id>
          <label/>
          <type>MenuSeparator</type>
          <action/>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,65">
          <id>activateDoc</id>
          <label>Active</label>
          <type>MenuItem</type>
          <action>ActivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,66">
          <id>deactivateDoc</id>
          <label>Inactive</label>
          <type>MenuItem</type>
          <action>DeactivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <Linkbar align="right" cssClass="" id="userLinkbar" label="" position="user_form.xlsx,form,69">
      <elements id="default">
        <element position="user_form.xlsx,form,76">
          <id>duplicateWindow</id>
          <label>Duplicate Window</label>
          <action/>
          <actionParams/>
          <rendererClass/>
          <image>duplicateWindow.png</image>
        </element>
        <element position="user_form.xlsx,form,77">
          <id>addToFavorites</id>
          <label>Add to Favorites</label>
          <action>AddDocToFavoriteAction</action>
          <actionParams/>
          <rendererClass/>
          <image>favorites.png</image>
        </element>
        <element position="user_form.xlsx,form,78">
          <id>approval</id>
          <label>Approval</label>
          <action>OpenApprovalByDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>approval.gif</image>
        </element>
        <element position="user_form.xlsx,form,79">
          <id>relatedActivities</id>
          <label>Related Activities</label>
          <action>ShowRelatedDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>activities.png</image>
        </element>
      </elements>
    </Linkbar>
    <MenuGroup id="moreGroup" label="More" position="user_form.xlsx,form,82">
      <elements id="default">
        <element position="user_form.xlsx,form,89">
          <id>customDocAction01</id>
          <label>Custom Action 1</label>
          <type>MenuItem</type>
          <action>UserCustom01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,90">
          <id>customDocAction02</id>
          <label>Custom Action 2</label>
          <type>MenuItem</type>
          <action>UserCustom02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,91">
          <id>customDocAction03</id>
          <label>Custom Action 3</label>
          <type>MenuItem</type>
          <action>UserCustom03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,92">
          <id>customDocAction04</id>
          <label>Custom Action 4</label>
          <type>MenuItem</type>
          <action>UserCustom04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,93">
          <id>customDocAction05</id>
          <label>Custom Action 5</label>
          <type>MenuItem</type>
          <action>UserCustom05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,94">
          <id>customDocAction06</id>
          <label>Custom Action 6</label>
          <type>MenuItem</type>
          <action>UserCustom06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,95">
          <id>customDocAction07</id>
          <label>Custom Action 7</label>
          <type>MenuItem</type>
          <action>UserCustom07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,96">
          <id>customDocAction08</id>
          <label>Custom Action 8</label>
          <type>MenuItem</type>
          <action>UserCustom08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,97">
          <id>customDocAction09</id>
          <label>Custom Action 9</label>
          <type>MenuItem</type>
          <action>UserCustom09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="user_form.xlsx,form,98">
          <id>customDocAction10</id>
          <label>Custom Action 10</label>
          <type>MenuItem</type>
          <action>UserCustom10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="printGroup" label="Print" position="user_form.xlsx,form,101">
      <elements id="default">
        <element position="user_form.xlsx,form,108">
          <id>customPrint01</id>
          <label>Custom Print 01</label>
          <type>MenuItem</type>
          <action>CustomPrint01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,109">
          <id>customPrint02</id>
          <label>Custom Print 02</label>
          <type>MenuItem</type>
          <action>CustomPrint02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,110">
          <id>customPrint03</id>
          <label>Custom Print 03</label>
          <type>MenuItem</type>
          <action>CustomPrint03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,111">
          <id>customPrint04</id>
          <label>Custom Print 04</label>
          <type>MenuItem</type>
          <action>CustomPrint04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,112">
          <id>customPrint05</id>
          <label>Custom Print 05</label>
          <type>MenuItem</type>
          <action>CustomPrint05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,113">
          <id>customPrint06</id>
          <label>Custom Print 06</label>
          <type>MenuItem</type>
          <action>CustomPrint06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,114">
          <id>customPrint07</id>
          <label>Custom Print 07</label>
          <type>MenuItem</type>
          <action>CustomPrint07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,115">
          <id>customPrint08</id>
          <label>Custom Print 08</label>
          <type>MenuItem</type>
          <action>CustomPrint08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,116">
          <id>customPrint09</id>
          <label>Custom Print 09</label>
          <type>MenuItem</type>
          <action>CustomPrint09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,117">
          <id>customPrint10</id>
          <label>Custom Print 10</label>
          <type>MenuItem</type>
          <action>CustomPrint10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="exportGroup" label="Export" position="user_form.xlsx,form,120">
      <elements id="default">
        <element position="user_form.xlsx,form,127">
          <id>customExport01</id>
          <label>Custom Export 01</label>
          <type>MenuItem</type>
          <action>CustomExport01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,128">
          <id>customExport02</id>
          <label>Custom Export 02</label>
          <type>MenuItem</type>
          <action>CustomExport02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,129">
          <id>customExport03</id>
          <label>Custom Export 03</label>
          <type>MenuItem</type>
          <action>CustomExport03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,130">
          <id>customExport04</id>
          <label>Custom Export 04</label>
          <type>MenuItem</type>
          <action>CustomExport04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,131">
          <id>customExport05</id>
          <label>Custom Export 05</label>
          <type>MenuItem</type>
          <action>CustomExport05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,132">
          <id>customExport06</id>
          <label>Custom Export 06</label>
          <type>MenuItem</type>
          <action>CustomExport06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,133">
          <id>customExport07</id>
          <label>Custom Export 07</label>
          <type>MenuItem</type>
          <action>CustomExport07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,134">
          <id>customExport08</id>
          <label>Custom Export 08</label>
          <type>MenuItem</type>
          <action>CustomExport08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,135">
          <id>customExport09</id>
          <label>Custom Export 09</label>
          <type>MenuItem</type>
          <action>CustomExport09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,form,136">
          <id>customExport10</id>
          <label>Custom Export 10</label>
          <type>MenuItem</type>
          <action>CustomExport10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
  </sheet>
  <sheet id="tabHeader" position="user_form.xlsx,tabHeader">
    <Tab id="tabHeader" label="Header" position="user_form.xlsx,tabHeader,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="user_form.xlsx,tabHeader,8">
          <id>generalSection</id>
          <label>General Information</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,9">
          <id>photoSection</id>
          <label>Photos</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,10">
          <id>subsitutionSection</id>
          <label>Substitution</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,11">
          <id>securitySection</id>
          <label>Security</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,12">
          <id>internationalSection</id>
          <label>International</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,13">
          <id>sysCustFields</id>
          <label>Additional Information</label>
          <type>SysCustGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,14">
          <id>autoSchedulingSection</id>
          <label>Auto Scheduling Informaction</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,15">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,16">
          <id>settingSection</id>
          <label>Setting</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,17">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,18">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,19">
          <id>responsibleForSection</id>
          <label>Responsible for</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,20">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,21">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="user_form.xlsx,tabHeader,22">
          <id>classificationSection</id>
          <label>Classification</label>
          <type>Section</type>
        </element>
        <element position="user_form.xlsx,tabHeader,23">
          <id>hierarchyGrid</id>
          <label>Hierarchies</label>
          <type>Grid</type>
        </element>
        <element position="user_form.xlsx,tabHeader,24">
          <id>userAttribute</id>
          <label>Attributes</label>
          <type>Grid</type>
        </element>
        <element position="user_form.xlsx,tabHeader,25">
          <id>inspectorUnavailableDatetime</id>
          <label>Leaves / Activities</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="photoSection" label="Photos" position="user_form.xlsx,tabHeader,28" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,38">
          <id>photoId</id>
          <label/>
          <type>Photo</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
        </element>
      </elements>
    </Section>
    <Section id="subsitutionSection" label="Substitution" position="user_form.xlsx,tabHeader,41" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,51">
          <id>substitutes</id>
          <label>Substitute User</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{userName}</format>
          <comboKey/>
          <viewName>lookupUserView</viewName>
          <viewParams>currentUserId={id}</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{userName}</popupFormat>
          <single>FALSE</single>
          <winTitle>User Lookup</winTitle>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,52">
          <id>effectiveFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,53">
          <id>effectiveTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,54">
          <id>isReceiveNotification</id>
          <label>Do Not Receive Notification</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Section id="generalSection" label="General Information" position="user_form.xlsx,tabHeader,57" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,67">
          <id>accountType</id>
          <label>Account Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetAccountTypeCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,68">
          <id>accountCompany</id>
          <label>Company</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format>{name}</format>
          <comboKey/>
          <viewName>lookupUserCompanyView</viewName>
          <viewParams>name=USER_ACCOUNT_COMPANY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <single/>
          <winTitle>Company Lookup</winTitle>
        </element>
        <element position="user_form.xlsx,tabHeader,69">
          <id>loginId</id>
          <label>User ID</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,70">
          <id>office</id>
          <label>Office</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,71">
          <id>alias</id>
          <label>Alias</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,72">
          <id>userName</id>
          <label>User Name</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,73">
          <id>firstName</id>
          <label>First Name</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,74">
          <id>lastName</id>
          <label>Last Name</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,75">
          <id>email</id>
          <label>Email</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,76">
          <id>shortCode</id>
          <label>Short Code</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,77">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,78">
          <id>oldPassword</id>
          <label>Old Password</label>
          <type>Password</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip>TRUE</tooltip>
          <tooltipAction>ShowOldPasswordTooltipAction</tooltipAction>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,79">
          <id>password</id>
          <label>New Password</label>
          <type>Password</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId>passwordMeter</passwordMeterId>
          <tooltip>TRUE</tooltip>
          <tooltipAction>ShowPasswordTooltipAction</tooltipAction>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,80">
          <id>confirmPassword</id>
          <label>Confirm New Password</label>
          <type>Password</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,81">
          <id>passwordMeter</id>
          <label>Password Strength</label>
          <type>PasswordMeter</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,82">
          <id>phone</id>
          <label>Phone</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,83">
          <id>mobile</id>
          <label>Mobile</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,84">
          <id>fax</id>
          <label>Fax</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,85">
          <id>country</id>
          <label>Country/Region</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,86">
          <id>state</id>
          <label>State</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,87">
          <id>city</id>
          <label>City</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,88">
          <id>address1</id>
          <label>Address 1</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,89">
          <id>address2</id>
          <label>Address 2</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,90">
          <id>address3</id>
          <label>Address 3</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,91">
          <id>address4</id>
          <label>Address 4</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,92">
          <id>postalCode</id>
          <label>Postal Code</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,93">
          <id>gpsLng</id>
          <label>GPS coordinate Lng</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,94">
          <id>gpsLat</id>
          <label>GPS coordinate Lat</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
        <element position="user_form.xlsx,tabHeader,95">
          <id>lastLogonTime</id>
          <label>Last Login</label>
          <type>Datetime</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <passwordMeterId/>
          <tooltip/>
          <tooltipAction/>
          <readonly>TRUE</readonly>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
        </element>
      </elements>
    </Section>
    <Section id="securitySection" label="Security" position="user_form.xlsx,tabHeader,98" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,108">
          <id>memberOfId</id>
          <label>Member of (Group)</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>memberOfId</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName>popGroupView</viewName>
          <viewParams/>
          <hideLabel/>
          <winTitle>Group Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,109">
          <id>blankOne</id>
          <label/>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <viewParams/>
          <hideLabel>TRUE</hideLabel>
          <winTitle/>
          <allowDateFilter/>
          <popupFormat/>
        </element>
        <element position="user_form.xlsx,tabHeader,110">
          <id>grantRoleId</id>
          <label>Granted Roles</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>grantRoleId</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popRoleView</viewName>
          <viewParams/>
          <hideLabel/>
          <winTitle>Role Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,111">
          <id>blankTwo</id>
          <label/>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <viewParams/>
          <hideLabel>TRUE</hideLabel>
          <winTitle/>
          <allowDateFilter/>
          <popupFormat/>
        </element>
        <element position="user_form.xlsx,tabHeader,112">
          <id>denyRoleId</id>
          <label>Denied Roles</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>denyRoleId</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popRoleView</viewName>
          <viewParams/>
          <hideLabel/>
          <winTitle>Role Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
      </elements>
    </Section>
    <Section id="internationalSection" label="Preference" position="user_form.xlsx,tabHeader,115" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,125">
          <id>language</id>
          <label>Language</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
        </element>
        <element position="user_form.xlsx,tabHeader,126">
          <id>timeZone</id>
          <label>Time Zone</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
        </element>
        <element position="user_form.xlsx,tabHeader,127">
          <id>newuiTimeZone</id>
          <label>Default TimeZone</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
        </element>
        <element position="user_form.xlsx,tabHeader,128">
          <id>defaultModule</id>
          <label>Default Module</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
        </element>
      </elements>
    </Section>
    <Section id="settingSection" label="Setting" position="user_form.xlsx,tabHeader,131" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,141">
          <id>accountLocked</id>
          <label>Account is locked</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <format>true:Yes, false:none</format>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,142">
          <id>accountExpire</id>
          <label>Password never expires</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <format/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,143">
          <id>passwordModified</id>
          <label>User must change password</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <format/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,144">
          <id>sendEmail</id>
          <label>Send emails to the user you have just created to notify their login &amp; password</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <format/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Section id="responsibleForSection" label="Responsible for" position="user_form.xlsx,tabHeader,147" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,157">
          <id>responsibleProductCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,158">
          <id>vendorCountry</id>
          <label>Vendor Country</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=COUNTRY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,159">
          <id>factoryCountry</id>
          <label>Factory Country</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=COUNTRY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,160">
          <id>portOfLoading</id>
          <label>Port of Loading</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PORT</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,161">
          <id>sourcingAgent</id>
          <label>Sourcing Agent</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=SOURCING_AGENT</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabHeader,162">
          <id>inspectionOffice</id>
          <label>Inspection Office</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=INSPECTION_OFFICE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
      </elements>
    </Section>
    <Section id="classificationSection" label="Classification" position="user_form.xlsx,tabHeader,165" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,175">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
      </elements>
    </Section>
    <Section id="autoSchedulingSection" label="Auto Scheduling Informaction" position="user_form.xlsx,tabHeader,178" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="user_form.xlsx,tabHeader,188">
          <id>autoScheduleEnabled</id>
          <label>Auto Schedule Enabled</label>
          <type>Checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,189">
          <id>auditCategory</id>
          <label>Audit Category/Skill</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=AUDIT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,190">
          <id>inspectionGroup</id>
          <label>Inspection Group</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,191">
          <id>inspectionCompany</id>
          <label>Inspection Company</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,192">
          <id>skill</id>
          <label>Skill</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=INSPECTION_SKILL</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,193">
          <id>zone</id>
          <label>Zone</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=LOCATION_ZONE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Grid entityName="UserHc" id="hierarchyGrid" label="Hierarchies" mapping="userHc" position="user_form.xlsx,tabHeader,196" ratio="100%" selectionMode="Multiple" showHint="FALSE">
      <elements id="buttons">
        <element position="user_form.xlsx,tabHeader,203">
          <id>selectHc</id>
          <label>Select...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupHierarchicalCode&amp;replaceBtnAction=ok:UserSelectHclNodeOkAction</actionParams>
        </element>
        <element position="user_form.xlsx,tabHeader,204">
          <id>delHc</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="user_form.xlsx,tabHeader,208">
          <id>hclTypeName</id>
          <label>Hierarchical Code List</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,tabHeader,209">
          <id>hclLevelName</id>
          <label>Level Name</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <maxLength/>
        </element>
        <element position="user_form.xlsx,tabHeader,210">
          <id>hclFullLineage</id>
          <label>Node</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <maxLength/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="UserAttribute" id="userAttribute" label="Attributes" position="user_form.xlsx,tabHeader,213" ratio="100%" selectionMode="Single" showHint="FALSE">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="user_form.xlsx,tabHeader,223">
          <id>codelistId</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
        </element>
        <element position="user_form.xlsx,tabHeader,224">
          <id>key</id>
          <label>Attribute</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
        </element>
        <element position="user_form.xlsx,tabHeader,225">
          <id>value</id>
          <label>System Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
        </element>
        <element position="user_form.xlsx,tabHeader,226">
          <id>node</id>
          <label>Override Value</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
        </element>
        <element position="user_form.xlsx,tabHeader,227">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="InspectorUnavailableDatetime" id="inspectorUnavailableDatetime" label="Leaves / Activities" position="user_form.xlsx,tabHeader,230" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="user_form.xlsx,tabHeader,237">
          <id>addInspectorUnavailableDatetime</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,tabHeader,238">
          <id>delInspectorUnavailableDatetime</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="user_form.xlsx,tabHeader,242">
          <id>date</id>
          <label>Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,243">
          <id>period</id>
          <label>Period</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="user_form.xlsx,tabHeader,244">
          <id>activityType</id>
          <label>Activity Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
      </elements>
    </Grid>
    <SysCustGroup id="sysCustFields" label="Additional Information" position="user_form.xlsx,tabHeader,247" type="SysCustGroup"/>
  </sheet>
  <sheet id="tabImage" position="user_form.xlsx,tabImage">
    <Tab id="tabImage" label="Images &amp; Attachments" position="user_form.xlsx,tabImage,1" ratio="100%">
      <elements id="default">
        <element position="user_form.xlsx,tabImage,8">
          <id>userImage</id>
          <label>Images</label>
          <type>Grid</type>
        </element>
        <element position="user_form.xlsx,tabImage,9">
          <id>userAttachment</id>
          <label>Attachments</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid enableDropUpload="Y" entityName="UserImage" id="userImage" label="Images" position="user_form.xlsx,tabImage,12" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="user_form.xlsx,tabImage,19">
          <id>addImage</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=UserImage&amp;upload=true</actionParams>
        </element>
        <element position="user_form.xlsx,tabImage,20">
          <id>copyImage</id>
          <label>Copy</label>
          <action>UserCopyImagesAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,tabImage,21">
          <id>delImage</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="user_form.xlsx,tabImage,25">
          <id>imageType</id>
          <label>Type</label>
          <type>Selection</type>
          <winTitle>Image Type Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>imageTypeId</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popCodelistView</viewName>
          <labelRenderer/>
          <viewParams>name=IMAGE_TYPE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
          <extraParams/>
        </element>
        <element position="user_form.xlsx,tabImage,26">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="user_form.xlsx,tabImage,27">
          <id>fileId</id>
          <label>File</label>
          <type>Image</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams>Image_tooltiptext=fileId.original.fileName</extraParams>
        </element>
        <element position="user_form.xlsx,tabImage,28">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="user_form.xlsx,tabImage,29">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <extraParams/>
        </element>
      </elements>
    </Grid>
    <Grid enableDropUpload="Y" entityName="UserAttachment" id="userAttachment" label="Attachments" position="user_form.xlsx,tabImage,32" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="user_form.xlsx,tabImage,39">
          <id>addAttachment</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=UserAttachment&amp;upload=true</actionParams>
        </element>
        <element position="user_form.xlsx,tabImage,40">
          <id>copyAttachment</id>
          <label>Copy</label>
          <action>UserCopyAttachmentsAction</action>
          <actionParams/>
        </element>
        <element position="user_form.xlsx,tabImage,41">
          <id>delAttachment</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="user_form.xlsx,tabImage,45">
          <id>attachmentType</id>
          <label>Type</label>
          <type>Selection</type>
          <winTitle>Attachment Type Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>attachTypeId</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popCodelistView</viewName>
          <labelRenderer/>
          <viewParams>name=ATTACHMENT_TYPE</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="user_form.xlsx,tabImage,46">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="user_form.xlsx,tabImage,47">
          <id>fileId</id>
          <label>File</label>
          <type>Attach</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="user_form.xlsx,tabImage,48">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="user_form.xlsx,tabImage,49">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
      </elements>
    </Grid>
  </sheet>
</form>
