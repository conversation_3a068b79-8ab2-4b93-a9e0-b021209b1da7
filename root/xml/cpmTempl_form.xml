<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form module="cpmTempl" position="cpmTempl_form.xlsx">
  <sheet id="_system" position="cpmTempl_form.xlsx,_system">
    <ProjectInfo client="cnt" position="cpmTempl_form.xlsx,_system,1" project="base" releaseNo="1.0a"/>
    <ProductVersion position="cpmTempl_form.xlsx,_system,7">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,_system,10">
          <updatedOn>10-Jun-2011</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.01</releaseNo>
        </element>
        <element position="cpmTempl_form.xlsx,_system,11">
          <updatedOn>22-Jun-2011</updatedOn>
          <summary>bug #123, 456, 789</summary>
          <releaseNo>1.02</releaseNo>
        </element>
        <element position="cpmTempl_form.xlsx,_system,12">
          <updatedOn>29-Jun-2011</updatedOn>
          <summary>bug #222, 333, 444</summary>
          <releaseNo>1.03</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="form" position="cpmTempl_form.xlsx,form">
    <Form id="cpmTemplForm" label="Critical Path Template" module="cpmTempl" position="cpmTempl_form.xlsx,form,1" version="1"/>
    <TabGroup id="cpmTemplTabGroup" label="" position="cpmTempl_form.xlsx,form,7">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,14">
          <id>tabHeader</id>
          <label>Header</label>
          <type>Tab</type>
        </element>
        <element position="cpmTempl_form.xlsx,form,15">
          <id>tabTasks</id>
          <label>Tasks</label>
          <type>Tab</type>
        </element>
        <element position="cpmTempl_form.xlsx,form,16">
          <id>tabDisplayFields</id>
          <label>Display Fields</label>
          <type>Tab</type>
        </element>
      </elements>
    </TabGroup>
    <Toolbar id="cpmTemplToolbar" label="" position="cpmTempl_form.xlsx,form,19">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,26">
          <id>cpmTemplMenubar</id>
          <label/>
          <type>Menubar</type>
        </element>
      </elements>
    </Toolbar>
    <Menubar align="left" cssClass="cbx-cpmTemplMenubar" id="cpmTemplMenubar" position="cpmTempl_form.xlsx,form,29">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,36">
          <id>newDoc</id>
          <label>Create</label>
          <type>MenuItem</type>
          <action>NewDocAction</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,37">
          <id>amendDoc</id>
          <label>Amend</label>
          <type>MenuItem</type>
          <action>AmendDocAction</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,38">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>MenuItem</type>
          <action>SaveAndConfirmAction</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,39">
          <id>discardDoc</id>
          <label>Cancel</label>
          <type>MenuItem</type>
          <action>DiscardDocAction</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,40">
          <id>printGroup</id>
          <label>Print</label>
          <type>MenuGroup</type>
          <action>printGroup</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,41">
          <id>exportGroup</id>
          <label>Export</label>
          <type>MenuGroup</type>
          <action>exportGroup</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,42">
          <id>actionsGroup</id>
          <label>Actions</label>
          <type>MenuGroup</type>
          <action>actionsGroup</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,43">
          <id>initializeCpm</id>
          <label>Initialize CPM</label>
          <type>MenuItem</type>
          <action>InitializeCpmAction</action>
          <actionParams/>
        </element>
        <element position="cpmTempl_form.xlsx,form,44">
          <id>moreGroup</id>
          <label>More</label>
          <type>MenuGroup</type>
          <action>moreGroup</action>
          <actionParams/>
        </element>
      </elements>
    </Menubar>
    <Linkbar align="right" cssClass="" id="cpmTemplLinkbar" position="cpmTempl_form.xlsx,form,47">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,54">
          <id>duplicateWindow</id>
          <label>Duplicate Window</label>
          <action/>
          <actionParams/>
          <rendererClass/>
          <image>duplicateWindow.png</image>
        </element>
        <element position="cpmTempl_form.xlsx,form,55">
          <id>addToFavorites</id>
          <label>Add to Favorites</label>
          <action>AddDocToFavoriteAction</action>
          <actionParams/>
          <rendererClass/>
          <image>favorites.png</image>
        </element>
        <element position="cpmTempl_form.xlsx,form,56">
          <id>approval</id>
          <label>Approval</label>
          <action>OpenApprovalByDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>approval.png</image>
        </element>
        <element position="cpmTempl_form.xlsx,form,57">
          <id>relatedActivities</id>
          <label>Related Activities</label>
          <action>ShowRelatedDocAction</action>
          <actionParams>hiddenRelatedActivitiesTab={tabDocuments}</actionParams>
          <rendererClass/>
          <image>activities.png</image>
        </element>
      </elements>
    </Linkbar>
    <Header position="cpmTempl_form.xlsx,form,60">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,63">
          <id>docStatus</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>inactive:(inactive),active:,canceled:(canceled)</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="cpmTempl_form.xlsx,form,64">
          <id>description</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>{name}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="cpmTempl_form.xlsx,form,65">
          <id>version</id>
          <label>Version</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format>{version}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="cpmTempl_form.xlsx,form,66">
          <id>headerIntegration</id>
          <label/>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="cpmTempl_form.xlsx,form,67">
          <id>cpmTemplLinkbar</id>
          <label/>
          <type>Linkbar</type>
          <align/>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
      </elements>
    </Header>
    <DropdownStores position="cpmTempl_form.xlsx,form,70">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,73">
          <id>listMainModules</id>
          <action>GetDDStoreByQueryId</action>
          <actionParams>queryId=listMainModules</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="cpmTempl_form.xlsx,form,74">
          <id>listMainLevels</id>
          <action>GetDDStoreByQueryId</action>
          <actionParams>queryId=listMainLevels</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="cpmTempl_form.xlsx,form,75">
          <id>listMainModulesForCpm</id>
          <action>GetDDStoreByQueryId</action>
          <actionParams>queryId=listMainLevelsForCpm</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="cpmTempl_form.xlsx,form,76">
          <id>listAnchorDates</id>
          <action>GetDDStoreByQueryId</action>
          <actionParams>queryId=listAnchorDates</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="cpmTempl_form.xlsx,form,77">
          <id>uiAccessRightType</id>
          <action>GetDDStoreByActionParams</action>
          <actionParams>options=1:lbl.role.tabHeader.ruleUiAdmins.accessRight.1;2:lbl.role.tabHeader.ruleUiAdmins.accessRight.2;3:lbl.role.tabHeader.ruleUiAdmins.accessRight.3</actionParams>
          <lazy/>
          <reload/>
        </element>
      </elements>
    </DropdownStores>
    <MenuGroup id="actionsGroup" label="Actions" position="cpmTempl_form.xlsx,form,80">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,87">
          <id>copyDoc</id>
          <label>Copy</label>
          <type>MenuItem</type>
          <action>CopyDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,88">
          <id>[BLANK]</id>
          <label/>
          <type>MenuSeparator</type>
          <action/>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,89">
          <id>activateDoc</id>
          <label>Active</label>
          <type>MenuItem</type>
          <action>ActivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,90">
          <id>deactivateDoc</id>
          <label>Inactive</label>
          <type>MenuItem</type>
          <action>DeactivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="moreGroup" label="More" position="cpmTempl_form.xlsx,form,93">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,100">
          <id>customDocAction01</id>
          <label>Custom Action 1</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,101">
          <id>customDocAction02</id>
          <label>Custom Action 2</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,102">
          <id>customDocAction03</id>
          <label>Custom Action 3</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,103">
          <id>customDocAction04</id>
          <label>Custom Action 4</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,104">
          <id>customDocAction05</id>
          <label>Custom Action 5</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,105">
          <id>customDocAction06</id>
          <label>Custom Action 6</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,106">
          <id>customDocAction07</id>
          <label>Custom Action 7</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,107">
          <id>customDocAction08</id>
          <label>Custom Action 8</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,108">
          <id>customDocAction09</id>
          <label>Custom Action 9</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="cpmTempl_form.xlsx,form,109">
          <id>customDocAction10</id>
          <label>Custom Action 10</label>
          <type>MenuItem</type>
          <action>CpmTemplCustom10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="printGroup" label="Print" position="cpmTempl_form.xlsx,form,112">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,119">
          <id>customPrint01</id>
          <label>Custom Print 01</label>
          <type>MenuItem</type>
          <action>CustomPrint01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,120">
          <id>customPrint02</id>
          <label>Custom Print 02</label>
          <type>MenuItem</type>
          <action>CustomPrint02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,121">
          <id>customPrint03</id>
          <label>Custom Print 03</label>
          <type>MenuItem</type>
          <action>CustomPrint03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,122">
          <id>customPrint04</id>
          <label>Custom Print 04</label>
          <type>MenuItem</type>
          <action>CustomPrint04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,123">
          <id>customPrint05</id>
          <label>Custom Print 05</label>
          <type>MenuItem</type>
          <action>CustomPrint05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,124">
          <id>customPrint06</id>
          <label>Custom Print 06</label>
          <type>MenuItem</type>
          <action>CustomPrint06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,125">
          <id>customPrint07</id>
          <label>Custom Print 07</label>
          <type>MenuItem</type>
          <action>CustomPrint07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,126">
          <id>customPrint08</id>
          <label>Custom Print 08</label>
          <type>MenuItem</type>
          <action>CustomPrint08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,127">
          <id>customPrint09</id>
          <label>Custom Print 09</label>
          <type>MenuItem</type>
          <action>CustomPrint09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,128">
          <id>customPrint10</id>
          <label>Custom Print 10</label>
          <type>MenuItem</type>
          <action>CustomPrint10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="exportGroup" label="Export" position="cpmTempl_form.xlsx,form,131">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,form,138">
          <id>customExport01</id>
          <label>Custom Export 01</label>
          <type>MenuItem</type>
          <action>CustomExport01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,139">
          <id>customExport02</id>
          <label>Custom Export 02</label>
          <type>MenuItem</type>
          <action>CustomExport02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,140">
          <id>customExport03</id>
          <label>Custom Export 03</label>
          <type>MenuItem</type>
          <action>CustomExport03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,141">
          <id>customExport04</id>
          <label>Custom Export 04</label>
          <type>MenuItem</type>
          <action>CustomExport04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,142">
          <id>customExport05</id>
          <label>Custom Export 05</label>
          <type>MenuItem</type>
          <action>CustomExport05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,143">
          <id>customExport06</id>
          <label>Custom Export 06</label>
          <type>MenuItem</type>
          <action>CustomExport06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,144">
          <id>customExport07</id>
          <label>Custom Export 07</label>
          <type>MenuItem</type>
          <action>CustomExport07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,145">
          <id>customExport08</id>
          <label>Custom Export 08</label>
          <type>MenuItem</type>
          <action>CustomExport08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,146">
          <id>customExport09</id>
          <label>Custom Export 09</label>
          <type>MenuItem</type>
          <action>CustomExport09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="cpmTempl_form.xlsx,form,147">
          <id>customExport10</id>
          <label>Custom Export 10</label>
          <type>MenuItem</type>
          <action>CustomExport10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
  </sheet>
  <sheet id="tabHeader" position="cpmTempl_form.xlsx,tabHeader">
    <Tab id="tabHeader" label="Header" position="cpmTempl_form.xlsx,tabHeader,1" ratio="34%,33%,33%">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,tabHeader,8">
          <id>generalSection</id>
          <label/>
          <type>Section</type>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,9">
          <id>classificationSection</id>
          <label>Classification</label>
          <type>Section</type>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,10">
          <id>sysCustFields</id>
          <label>Additional Information</label>
          <type>SysCustGroup</type>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,11">
          <id>cpmTemplMatchRules</id>
          <label/>
          <type>Grid</type>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,12">
          <id>notifications</id>
          <label/>
          <type>Grid</type>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,13">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
      </elements>
    </Tab>
    <Section id="generalSection" label="General Information" position="cpmTempl_form.xlsx,tabHeader,16" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="cpmTempl_form.xlsx,tabHeader,26">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format/>
          <readonlyForm/>
          <sorting/>
          <mandatory>TRUE</mandatory>
          <labelRenderer/>
          <comboKey/>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,27">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format/>
          <readonlyForm/>
          <sorting/>
          <mandatory/>
          <labelRenderer/>
          <comboKey/>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,28">
          <id>applyModule</id>
          <label>Apply to</label>
          <type>Dropdown</type>
          <data>listMainModules</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade>applyLevel,anchorDateFieldId</cascade>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{label}</format>
          <readonlyForm/>
          <sorting/>
          <mandatory>TRUE</mandatory>
          <labelRenderer/>
          <comboKey>module</comboKey>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,29">
          <id>applyLevel</id>
          <label>Level</label>
          <type>Dropdown</type>
          <data>listMainLevels</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade>anchorDateFieldId</cascade>
          <cascadeExpr>'{applyModule}'==module</cascadeExpr>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{level}</format>
          <readonlyForm/>
          <sorting/>
          <mandatory/>
          <labelRenderer/>
          <comboKey>level</comboKey>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,30">
          <id>childLevelCondition</id>
          <label>Child Level Condition</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonlyForm/>
          <sorting/>
          <mandatory/>
          <labelRenderer/>
          <comboKey/>
          <viewName>popSelConditionView</viewName>
          <winTitle>Condition Lookup</winTitle>
          <popupFormat>{name}</popupFormat>
          <single>TRUE</single>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,31">
          <id>anchorDateFieldId</id>
          <label>Anchor Date</label>
          <type>Dropdown</type>
          <data>listAnchorDates</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr>'{applyModule}'==module&amp;&amp;(likes('{applyLevel}', level+'\\.%')||'{applyLevel}'==level||'entity'==level)</cascadeExpr>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{fieldPath}</format>
          <readonlyForm/>
          <sorting/>
          <mandatory>TRUE</mandatory>
          <labelRenderer/>
          <comboKey>fieldPath</comboKey>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,32">
          <id>isAnchorDatesMandatory</id>
          <label>Anchor Dates mandatory</label>
          <type>checkbox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <format/>
          <readonlyForm/>
          <sorting/>
          <mandatory/>
          <labelRenderer/>
          <comboKey/>
          <viewName/>
          <winTitle/>
          <popupFormat/>
          <single/>
        </element>
      </elements>
    </Section>
    <Section id="classificationSection" label="Classification" position="cpmTempl_form.xlsx,tabHeader,35" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="cpmTempl_form.xlsx,tabHeader,45">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <extraParams>Selection_preOpenPopupAction=ConstructViewParamsForClassificationAction</extraParams>
        </element>
      </elements>
    </Section>
    <Grid entityName="CpmTemplMatchRule" hintLabel="This record is available under the following condition(s)." id="cpmTemplMatchRules" label="Apply to" position="cpmTempl_form.xlsx,tabHeader,48" ratio="100%" selectionMode="Multiple" showHint="TRUE">
      <elements id="buttons">
        <element position="cpmTempl_form.xlsx,tabHeader,55">
          <id>addTemplCondition</id>
          <label>Select...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupSelectCondition</actionParams>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,56">
          <id>delItem</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="cpmTempl_form.xlsx,tabHeader,60">
          <id>conditionId</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <size/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom>Condition.id</dataFrom>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping>Condition.id</mapping>
          <format/>
          <single/>
          <viewName/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,61">
          <id>conditionNo</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <size/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom>Condition</dataFrom>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <format/>
          <single/>
          <viewName/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,62">
          <id>priority</id>
          <label>Priority</label>
          <type>Number</type>
          <data/>
          <size>XS</size>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory>TRUE</mandatory>
          <mapping/>
          <format/>
          <single/>
          <viewName/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,63">
          <id>conditionName</id>
          <label>Condition</label>
          <type>Hyperlink</type>
          <data/>
          <size>L</size>
          <sorting>asc</sorting>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=condition&amp;fieldId=conditionNo&amp;gridId=cpmTemplMatchRules</actionParams>
          <defaultValue/>
          <dataFrom>Condition.name</dataFrom>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping>conditionNo.name</mapping>
          <format/>
          <single/>
          <viewName/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="CpmAllNotification" hintLabel="" id="notifications" label="Notification" position="cpmTempl_form.xlsx,tabHeader,66" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="cpmTempl_form.xlsx,tabHeader,73">
          <id>addTemplNotification</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams>entityName=CpmAllNotification</actionParams>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,74">
          <id>delNotificationItem</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="cpmTempl_form.xlsx,tabHeader,78">
          <id>condition</id>
          <label>Condition</label>
          <type>Dropdown</type>
          <data/>
          <size>M</size>
          <sorting/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory>TRUE</mandatory>
          <mapping/>
          <format>{name}</format>
          <single/>
          <viewName/>
          <winTitle/>
          <allowDateFilter/>
          <popupFormat/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,79">
          <id>when</id>
          <label>When</label>
          <type>Dropdown</type>
          <data/>
          <size>M</size>
          <sorting/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory>TRUE</mandatory>
          <mapping/>
          <format>{name}</format>
          <single/>
          <viewName/>
          <winTitle/>
          <allowDateFilter/>
          <popupFormat/>
        </element>
        <element position="cpmTempl_form.xlsx,tabHeader,80">
          <id>notificationProfiles</id>
          <label>Notification Profile</label>
          <type>selection</type>
          <data/>
          <size>L</size>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory>TRUE</mandatory>
          <mapping/>
          <format>{profileName}</format>
          <single/>
          <viewName>popNotifiProfileView</viewName>
          <winTitle>Notification Profile Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{profileName}</popupFormat>
        </element>
      </elements>
    </Grid>
    <SysCustGroup id="sysCustFields" label="Additional Information" position="cpmTempl_form.xlsx,tabHeader,83" type="SysCustGroup"/>
  </sheet>
  <sheet id="tabTasks" position="cpmTempl_form.xlsx,tabTasks">
    <Tab id="tabTasks" label="Milestones" position="cpmTempl_form.xlsx,tabTasks,1" ratio="1">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,tabTasks,8">
          <id>cpmTemplTasks</id>
          <label>Milestones</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="CpmTemplTask" id="cpmTemplTasks" label="Milestones" position="cpmTempl_form.xlsx,tabTasks,11" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="cpmTempl_form.xlsx,tabTasks,18">
          <id>addTemplTask</id>
          <label>Select...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popCpmTemplSelCpmTaskWin</actionParams>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,19">
          <id>delCpoShip</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="cpmTempl_form.xlsx,tabTasks,23">
          <id>sequence</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,24">
          <id>taskName</id>
          <label>Name</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,25">
          <id>taskType</id>
          <label>Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,26">
          <id>milestoneAnchor</id>
          <label>Milestone Anchor Date</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <rendererClass>com.core.cbx.ui.zk.cul.grid.renderer.MilestoneAnchorDateCellRenderer</rendererClass>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,27">
          <id>offset</id>
          <label>Offset</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,28">
          <id>duration</id>
          <label>Duration</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,29">
          <id>assignees</id>
          <label>Assignee</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>assignees</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}{userName}{userOrGroupName}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName>popAssigneesView</viewName>
          <single/>
          <viewParams>applyModule={$ds.applyModule}&amp;assigneeType='ResponsibleParties'</viewParams>
          <winTitle>Assignee Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}{userName}{userOrGroupName}</popupFormat>
          <rendererClass>com.core.cbx.ui.renderer.UserGroupPartyCellRenderer</rendererClass>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,30">
          <id>defaultStatus</id>
          <label>Default Status</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,31">
          <id>milestonePredecessor</id>
          <label>Predecessors</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <rendererClass>com.core.cbx.ui.zk.cul.grid.renderer.MilestonePredecessorCellRenderer</rendererClass>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,32">
          <id>notifications</id>
          <label>Notifications</label>
          <type>Button</type>
          <data/>
          <action>OpenPopupTaskNotificationsWinAction</action>
          <actionParams>winId=popCpmTemplTaskNotificationWin</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,33">
          <id>events</id>
          <label>Events</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,34">
          <id>advancedSecuritys</id>
          <label>Advanced Security</label>
          <type>Button</type>
          <data/>
          <action>OpenPopupAdvancedSecurityWinAction</action>
          <actionParams>winId=popCpmTemplAdvancedSecurityWin</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable>TRUE</alwaysEditable>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,35">
          <id>fieldId</id>
          <label>Linked Field</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,36">
          <id>refEntity</id>
          <label>Linked Entity</label>
          <type>Dropdown</type>
          <data>listMainModulesForCpm</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey>applyToEntity</comboKey>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
        <element position="cpmTempl_form.xlsx,tabTasks,37">
          <id>refFieldId</id>
          <label>Ref. Field in Linked Entity</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <rendererClass/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabDiplayFields" position="cpmTempl_form.xlsx,tabDiplayFields">
    <Tab id="tabDisplayFields" label="Display Fields" position="cpmTempl_form.xlsx,tabDiplayFields,1" ratio="1">
      <elements id="default">
        <element position="cpmTempl_form.xlsx,tabDiplayFields,8">
          <id>cpmDisplayFields</id>
          <label>Display Fields</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="CpmDisplayField" id="cpmDisplayFields" label="Display Fields" position="cpmTempl_form.xlsx,tabDiplayFields,11" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="cpmTempl_form.xlsx,tabDiplayFields,18">
          <id>addDisplayField</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams>entityName=CpmDisplayField</actionParams>
        </element>
        <element position="cpmTempl_form.xlsx,tabDiplayFields,19">
          <id>selCpmTemplate</id>
          <label>Select from Other CPM Template…</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popCpmTemplSelDisplayFieldWin</actionParams>
        </element>
        <element position="cpmTempl_form.xlsx,tabDiplayFields,20">
          <id>delDisplayField</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="cpmTempl_form.xlsx,tabDiplayFields,24">
          <id>displayFieldRow</id>
          <label>Row</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <rendererClass/>
          <popupFormat/>
        </element>
        <element position="cpmTempl_form.xlsx,tabDiplayFields,25">
          <id>displayField</id>
          <label>Fields(Comma Separated)</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{fieldId}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName>popupDocumentFieldView</viewName>
          <single/>
          <viewParams>module={$ds.applyModule}&amp;isCpmApply=Cpm&amp;applyLevel={$ds.applyLevel}</viewParams>
          <winTitle>Document Field Lookup</winTitle>
          <allowDateFilter/>
          <readonly/>
          <rendererClass>com.core.cbx.ui.zk.cul.grid.renderer.CpmDisplayFieldCellRenderer</rendererClass>
          <popupFormat>{fieldId}</popupFormat>
        </element>
        <element position="cpmTempl_form.xlsx,tabDiplayFields,26">
          <id>hyperlinkKeyEntity</id>
          <label>Hyperlink Key</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{fieldId}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName>popupDocumentFieldView</viewName>
          <single>TRUE</single>
          <viewParams>module={$ds.applyModule}&amp;isCpmApply=Cpm&amp;isHyperlinkKey=Hyperlink&amp;applyLevel={$ds.applyLevel}</viewParams>
          <winTitle>Document Field Lookup</winTitle>
          <allowDateFilter/>
          <readonly/>
          <rendererClass>com.core.cbx.ui.zk.cul.grid.renderer.CpmHyperlinkKeyCellRenderer</rendererClass>
          <popupFormat>{fieldId}</popupFormat>
        </element>
        <element position="cpmTempl_form.xlsx,tabDiplayFields,27">
          <id>advanced</id>
          <label>Advanced</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <single/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <rendererClass/>
          <popupFormat/>
        </element>
      </elements>
    </Grid>
  </sheet>
</form>
