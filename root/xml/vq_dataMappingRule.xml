<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="vq" position="vq_dataMappingRule.xlsx">
  <sheet id="vqCopyCarton" position="vq_dataMappingRule.xlsx,vqCopyCarton">
    <DataMappingRule description="Mapping from vq carton copy" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqCopyCarton" position="vq_dataMappingRule.xlsx,vqCopyCarton,1" srcEntityName="VqCarton" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyCarton,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyCarton,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqCarton</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyImage" position="vq_dataMappingRule.xlsx,vqCopyImage">
    <DataMappingRule description="Mapping from vq image copy" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqCopyImage" position="vq_dataMappingRule.xlsx,vqCopyImage,1" srcEntityName="VqImage" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,11">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultFlag</srcFieldId>
          <dstFieldId>defaultFlag</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,12">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>vqImage.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,13">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId.original</srcFieldId>
          <dstFieldId>vqImage.fileId.original</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,14">
          <mappingType>Field</mappingType>
          <srcFieldId>filePath</srcFieldId>
          <dstFieldId>filePath</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,15">
          <mappingType>Field</mappingType>
          <srcFieldId>fileName</srcFieldId>
          <dstFieldId>fileName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,16">
          <mappingType>Field</mappingType>
          <srcFieldId>fileSize</srcFieldId>
          <dstFieldId>fileSize</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,17">
          <mappingType>Field</mappingType>
          <srcFieldId>protocol</srcFieldId>
          <dstFieldId>protocol</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,18">
          <mappingType>Field</mappingType>
          <srcFieldId>thumbnail</srcFieldId>
          <dstFieldId>thumbnail</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,19">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId.propFormat</srcFieldId>
          <dstFieldId>vqImage.fileId.propFormat</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,20">
          <mappingType>Field</mappingType>
          <srcFieldId>filePath</srcFieldId>
          <dstFieldId>filePath</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,21">
          <mappingType>Field</mappingType>
          <srcFieldId>fileName</srcFieldId>
          <dstFieldId>fileName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,22">
          <mappingType>Field</mappingType>
          <srcFieldId>fileSize</srcFieldId>
          <dstFieldId>fileSize</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,23">
          <mappingType>Field</mappingType>
          <srcFieldId>protocol</srcFieldId>
          <dstFieldId>protocol</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,24">
          <mappingType>Field</mappingType>
          <srcFieldId>thumbnail</srcFieldId>
          <dstFieldId>thumbnail</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,25">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId.withMarkUp</srcFieldId>
          <dstFieldId>vqImage.fileId.withMarkUp</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,26">
          <mappingType>Field</mappingType>
          <srcFieldId>filePath</srcFieldId>
          <dstFieldId>filePath</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,27">
          <mappingType>Field</mappingType>
          <srcFieldId>fileName</srcFieldId>
          <dstFieldId>fileName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,28">
          <mappingType>Field</mappingType>
          <srcFieldId>fileSize</srcFieldId>
          <dstFieldId>fileSize</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,29">
          <mappingType>Field</mappingType>
          <srcFieldId>protocol</srcFieldId>
          <dstFieldId>protocol</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,30">
          <mappingType>Field</mappingType>
          <srcFieldId>thumbnail</srcFieldId>
          <dstFieldId>thumbnail</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyImage,31">
          <mappingType>Section</mappingType>
          <srcFieldId>imageTypeId</srcFieldId>
          <dstFieldId>vqImage.imageTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyAttachment" position="vq_dataMappingRule.xlsx,vqCopyAttachment">
    <DataMappingRule description="Mapping from vq attachment copy" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqCopyAttachment" position="vq_dataMappingRule.xlsx,vqCopyAttachment,1" srcEntityName="VqAttachment" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyCostElement" position="vq_dataMappingRule.xlsx,vqCopyCostElement">
    <DataMappingRule description="Mapping from vq carton copy" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqCopyCostElement" position="vq_dataMappingRule.xlsx,vqCopyCostElement,1" srcEntityName="VqCostElement" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyCostElement,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyCostElement,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqCostElement</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqSelectIngredient" position="vq_dataMappingRule.xlsx,vqSelectIngredient">
    <DataMappingRule description="Mapping from Ingredient to vq" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2022-04-11" id="vqSelectIngredient" position="vq_dataMappingRule.xlsx,vqSelectIngredient,1" srcEntityName="Ingredient" srcEntityVersion="1" status="1" updatedDate="2022-04-11">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqSpecFormulation</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqSpecFormulation.ingredient</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,11">
          <mappingType>Field</mappingType>
          <srcFieldId>ingredientNo</srcFieldId>
          <dstFieldId>vqSpecFormulation.ingredientNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,12">
          <mappingType>Field</mappingType>
          <srcFieldId>ingredientName</srcFieldId>
          <dstFieldId>vqSpecFormulation.tree</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,13">
          <mappingType>Field</mappingType>
          <srcFieldId>ingredientCost</srcFieldId>
          <dstFieldId>vqSpecFormulation.ingredientCost</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,14">
          <mappingType>Field</mappingType>
          <srcFieldId>used</srcFieldId>
          <dstFieldId>vqSpecFormulation.used</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,15">
          <mappingType>Field</mappingType>
          <srcFieldId>isDeclared</srcFieldId>
          <dstFieldId>vqSpecFormulation.isDeclared</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isShowInSummary</srcFieldId>
          <dstFieldId>vqSpecFormulation.isShowInSummary</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,17">
          <mappingType>Field</mappingType>
          <srcFieldId>insNo</srcFieldId>
          <dstFieldId>vqSpecFormulation.insNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,18">
          <mappingType>Field</mappingType>
          <srcFieldId>concentration</srcFieldId>
          <dstFieldId>vqSpecFormulation.concentration</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,19">
          <mappingType>Field</mappingType>
          <srcFieldId>allowableRange</srcFieldId>
          <dstFieldId>vqSpecFormulation.allowableRange</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,20">
          <mappingType>Field</mappingType>
          <srcFieldId>maximumConcentrationScope</srcFieldId>
          <dstFieldId>vqSpecFormulation.maximumConcentrationScope</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,21">
          <mappingType>Field</mappingType>
          <srcFieldId>casNo</srcFieldId>
          <dstFieldId>vqSpecFormulation.casNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,22">
          <mappingType>Field</mappingType>
          <srcFieldId>function</srcFieldId>
          <dstFieldId>vqSpecFormulation.function</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,23">
          <mappingType>Field</mappingType>
          <srcFieldId>supplier</srcFieldId>
          <dstFieldId>vqSpecFormulation.supplier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,24">
          <mappingType>Field</mappingType>
          <srcFieldId>notesOrInstructions</srcFieldId>
          <dstFieldId>vqSpecFormulation.notesOrInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,25">
          <mappingType>Section</mappingType>
          <srcFieldId>category</srcFieldId>
          <dstFieldId>vqSpecFormulation.category</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,26">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>vqSpecFormulation.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,27">
          <mappingType>Section</mappingType>
          <srcFieldId>functionClass</srcFieldId>
          <dstFieldId>vqSpecFormulation.functionClass</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,28">
          <mappingType>Section</mappingType>
          <srcFieldId>sustainability</srcFieldId>
          <dstFieldId>vqSpecFormulation.sustainability</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,29">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>vqSpecFormulation.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectIngredient,30">
          <mappingType>type*</mappingType>
          <srcFieldId>templateName</srcFieldId>
          <dstFieldId>templateFile</dstFieldId>
          <dstFieldType>implementationClass*</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="specFormulationToQuotation" position="vq_dataMappingRule.xlsx,specFormulationToQuotation">
    <DataMappingRule description="Mapping from item's specFormulation to vq" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2022-04-11" id="specFormulationToQuotation" position="vq_dataMappingRule.xlsx,specFormulationToQuotation,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2022-04-11">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,9">
          <mappingType>Section</mappingType>
          <srcFieldId>ingredientList</srcFieldId>
          <dstFieldId>ingredientList</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,10">
          <mappingType>Section</mappingType>
          <srcFieldId>halal</srcFieldId>
          <dstFieldId>halal</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,11">
          <mappingType>Section</mappingType>
          <srcFieldId>maximumResidueLimits</srcFieldId>
          <dstFieldId>maximumResidueLimits</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,12">
          <mappingType>Section</mappingType>
          <srcFieldId>dateCodeFormat</srcFieldId>
          <dstFieldId>dateCodeFormat</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,13">
          <mappingType>Section</mappingType>
          <srcFieldId>dateLotMarkingLocation</srcFieldId>
          <dstFieldId>dateLotMarkingLocation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,14">
          <mappingType>Section</mappingType>
          <srcFieldId>dateLotMarkingTech</srcFieldId>
          <dstFieldId>dateLotMarkingTech</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,15">
          <mappingType>Section</mappingType>
          <srcFieldId>shelfLifeInformation</srcFieldId>
          <dstFieldId>shelfLifeInformation</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,specFormulationToQuotation,16">
          <mappingType>Section</mappingType>
          <srcFieldId>specFormulation</srcFieldId>
          <dstFieldId>vqSpecFormulation</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyDoc" position="vq_dataMappingRule.xlsx,vqCopyDoc">
    <DataMappingRule description="Mapping from vq copy" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqCopyDoc" position="vq_dataMappingRule.xlsx,vqCopyDoc,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>shortListed</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>referenceVq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqCopyDoc,18">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqCopyDocPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqGenOffersheet" position="vq_dataMappingRule.xlsx,vqGenOffersheet">
    <DataMappingRule description="Mapping from vq gen offersheet" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqGenOffersheet" position="vq_dataMappingRule.xlsx,vqGenOffersheet,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,13">
          <mappingType>Section</mappingType>
          <srcFieldId>vqType</srcFieldId>
          <dstFieldId>quoteType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,14">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>custCurrency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,15">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,16">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultCustomer.cust</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,17">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,19">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,21">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,22">
          <mappingType>Field</mappingType>
          <srcFieldId>ft20Qty</srcFieldId>
          <dstFieldId>ft20</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,23">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40Qty</srcFieldId>
          <dstFieldId>ft40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,24">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40HcQty</srcFieldId>
          <dstFieldId>ftHc40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,25">
          <mappingType>Field</mappingType>
          <srcFieldId>ft45Qty</srcFieldId>
          <dstFieldId>ft45</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,26">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>innerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,27">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>outerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,29">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,30">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,31">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,32">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>plannedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,33">
          <mappingType>Field</mappingType>
          <srcFieldId>baseELC</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,34">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,35">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,36">
          <mappingType>Section</mappingType>
          <srcFieldId>containerSize</srcFieldId>
          <dstFieldId>osItem.containerSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,37">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>osItem.incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,38">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>osItem.shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,39">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>osItem.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,40">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>osItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,41">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>osItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,42">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>osItem.paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,43">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,44">
          <mappingType>Field</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,45">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,46">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,47">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,48">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,49">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultCustomer.market</srcFieldId>
          <dstFieldId>osItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,50">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultCustomer.channel</srcFieldId>
          <dstFieldId>osItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,51">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>osItem.vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,52">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,53">
          <mappingType>Section</mappingType>
          <srcFieldId>classification</srcFieldId>
          <dstFieldId>osItem.classification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,54">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,55">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem.vqId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,56">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>osItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,57">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>osItem.paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,58">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,59">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,60">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,61">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,62">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,63">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,64">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,65">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,66">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,67">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,68">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,69">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,73">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqGenOfferSheetPreProcessor</implementationClass>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenOffersheet,74">
          <type>PostProcessor</type>
          <templateName>vqGenOffersheetTemplate</templateName>
          <templateFile>vq_gen_offersheet_post_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqGenOfferSheetPostProcess</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCostElement" position="vq_dataMappingRule.xlsx,vqCostElement">
    <DataMappingRule description="Mapping from CostSheetTemplateDetail to VqCostElement" domain="/" dstEntityName="VqCostElement" dstEntityVersion="1" effectiveDate="2012-06-03" id="vqCostElement" position="vq_dataMappingRule.xlsx,vqCostElement,1" srcEntityName="CostSheetTemplateDetail" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCostElement,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,9">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,10">
          <mappingType>Field</mappingType>
          <srcFieldId>costElementName</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,11">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultPercent</srcFieldId>
          <dstFieldId>percentageUnitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,12">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultAmt</srcFieldId>
          <dstFieldId>amount</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,13">
          <mappingType>Section</mappingType>
          <srcFieldId>costElementType</srcFieldId>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCostElement,14">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqSelectVendorChargesTemp" position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp">
    <DataMappingRule description="Mapping from VendorChargesTemplate to Vq" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-06-03" id="vqSelectVendorChargesTemp" position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,1" srcEntityName="VendorChargesTemplate" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,9">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorChargesTmplCharges</srcFieldId>
          <dstFieldId>vqOtherCharges</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,11">
          <mappingType>Field</mappingType>
          <srcFieldId>rate</srcFieldId>
          <dstFieldId>rate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,12">
          <mappingType>Field</mappingType>
          <srcFieldId>basis</srcFieldId>
          <dstFieldId>basis</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,13">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorChargesTmplCharges.type</srcFieldId>
          <dstFieldId>vqOtherCharges.type</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendorChargesTemp,14">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorChargesTmplCharges.currency</srcFieldId>
          <dstFieldId>vqOtherCharges.currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyOtherCharges" position="vq_dataMappingRule.xlsx,vqCopyOtherCharges">
    <DataMappingRule description="Mapping for Copy Vq OtherCharges" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-15" id="vqCopyOtherCharges" position="vq_dataMappingRule.xlsx,vqCopyOtherCharges,1" srcEntityName="VqOtherCharge" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyOtherCharges,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyOtherCharges,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqOtherCharges</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqCopyAdditionalCosts" position="vq_dataMappingRule.xlsx,vqCopyAdditionalCosts">
    <DataMappingRule description="Mapping for Copy Vq AdditionalCosts" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-15" id="vqCopyAdditionalCosts" position="vq_dataMappingRule.xlsx,vqCopyAdditionalCosts,1" srcEntityName="VqAdditionalCost" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqCopyAdditionalCosts,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyAdditionalCosts,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqAdditionalCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqCopyAdditionalCosts,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vqAdditionalCosts.isFromOpenCostingTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqGenVpo" position="vq_dataMappingRule.xlsx,vqGenVpo">
    <DataMappingRule description="Mapping from Vq to Vpo" domain="/" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-06-03" id="vqGenVpo" position="vq_dataMappingRule.xlsx,vqGenVpo,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,12">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,13">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,23">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>totalCost</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>vqNo</srcFieldId>
          <dstFieldId>quoteNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>vpoItem.qtyPerExportCarton</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>vpoItem.qtyPerInnerCarton</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,33">
          <mappingType>Section</mappingType>
          <srcFieldId>project</srcFieldId>
          <dstFieldId>vpoItem.project</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,34">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>vpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,35">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoItem.quoteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,36">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>vpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,37">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>vpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,38">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>vpoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,39">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>vpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,40">
          <mappingType>Section</mappingType>
          <srcFieldId>item.classification</srcFieldId>
          <dstFieldId>vpoItem.classification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,41">
          <mappingType>Section</mappingType>
          <srcFieldId>item.productCategory</srcFieldId>
          <dstFieldId>vpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,42">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>vpoItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,43">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>vpoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,44">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>vpoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,45">
          <mappingType>Section</mappingType>
          <srcFieldId>subContractor</srcFieldId>
          <dstFieldId>vpoItem.itemSubContractor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,46">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>vpoItem.vpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,47">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,48">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,49">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,50">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,51">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,52">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,53">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqGenVpo,57">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqCreateVpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqGenCpo" position="vq_dataMappingRule.xlsx,vqGenCpo">
    <DataMappingRule description="Mapping from Vq to Cpo" domain="/" dstEntityName="Cpo" dstEntityVersion="1" effectiveDate="2012-06-03" id="vqGenCpo" position="vq_dataMappingRule.xlsx,vqGenCpo,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>cpoType</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemCustPaymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,12">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,13">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCustIncotern</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCustPaymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>itemCustPaymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>itemHierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemProductCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>cpoItem.moq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,22">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCostSheetBaseElc</srcFieldId>
          <dstFieldId>cpoItem.sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>cpoItem.vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>cpoItem.qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>cpoItem.qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonLength</srcFieldId>
          <dstFieldId>cpoItem.l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonWidht</srcFieldId>
          <dstFieldId>cpoItem.w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonHeigh</srcFieldId>
          <dstFieldId>cpoItem.h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonGrossWeight</srcFieldId>
          <dstFieldId>cpoItem.gw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonNetweight</srcFieldId>
          <dstFieldId>cpoItem.nw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonCartonCbm</srcFieldId>
          <dstFieldId>cpoItem.cbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>vqCartonCartonCFT</srcFieldId>
          <dstFieldId>cpoItem.outerCartonCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>itemCustItemNo</srcFieldId>
          <dstFieldId>cpoItem.customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemName</srcFieldId>
          <dstFieldId>cpoItem.itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemDesc</srcFieldId>
          <dstFieldId>cpoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>cpoItem.planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,38">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCartonDimensionUom</srcFieldId>
          <dstFieldId>cpoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,39">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCartonWeightUOM</srcFieldId>
          <dstFieldId>cpoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,40">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>cpoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,41">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionUOM</srcFieldId>
          <dstFieldId>cpoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,42">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultUom</srcFieldId>
          <dstFieldId>cpoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,43">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemType</srcFieldId>
          <dstFieldId>cpoItem.itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,44">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>cpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,45">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>cpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,46">
          <mappingType>Section</mappingType>
          <srcFieldId>item.sourcingRecord.season</srcFieldId>
          <dstFieldId>cpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,47">
          <mappingType>Section</mappingType>
          <srcFieldId>item.sourcingRecord.year</srcFieldId>
          <dstFieldId>cpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,48">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>cpoItem.quoteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,49">
          <mappingType>Section</mappingType>
          <srcFieldId>project</srcFieldId>
          <dstFieldId>cpoItem.projectId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,50">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCostSheet</srcFieldId>
          <dstFieldId>cpoItem.costId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,51">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>cpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,52">
          <mappingType>Section</mappingType>
          <srcFieldId>itemHierarchy</srcFieldId>
          <dstFieldId>cpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,53">
          <mappingType>Section</mappingType>
          <srcFieldId>itemProductCategory</srcFieldId>
          <dstFieldId>cpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,57">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqCreateCpoPreProcessor</implementationClass>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqGenCpo,58">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqCreateCpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqNewProject" position="vq_dataMappingRule.xlsx,vqNewProject">
    <DataMappingRule description="Mapping from vq to project" domain="/" dstEntityName="Project" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqNewProject" position="vq_dataMappingRule.xlsx,vqNewProject,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqNewProject,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewProject,9">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewProject,10">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqNewProject,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SetItemToProjecInVqProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqNewMpo" position="vq_dataMappingRule.xlsx,vqNewMpo">
    <DataMappingRule description="Mapping from Vq to Vendor Master Order" domain="/" dstEntityName="Mpo" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqGenMpo" position="vq_dataMappingRule.xlsx,vqNewMpo,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>mpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,11">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,12">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,13">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>headerFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>payMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,22">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>totalCost</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>truckNumber</srcFieldId>
          <dstFieldId>countOfTruck</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCbm</srcFieldId>
          <dstFieldId>cbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,29">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,30">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,31">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>mpoItems.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,32">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>mpoItems.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,33">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemBrand</srcFieldId>
          <dstFieldId>mpoItems.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,34">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>mpoItems.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,35">
          <mappingType>Section</mappingType>
          <srcFieldId>containerType</srcFieldId>
          <dstFieldId>mpoItems.containerType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,36">
          <mappingType>Section</mappingType>
          <srcFieldId>truckType</srcFieldId>
          <dstFieldId>mpoItems.truckType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,37">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>mpoItems.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,38">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>mpoItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,39">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>mpoItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,40">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>mpoItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,41">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>mpoItems.quotation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,42">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>mpoItems.mpoItemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>item</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,43">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemSize</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,44">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,45">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,46">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,47">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,48">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>mpoItems.mpoItemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>item</mappedValue>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,49">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,50">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,51">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,52">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,53">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqNewMpo,57">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqGenMpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqSelectVendor" position="vq_dataMappingRule.xlsx,vqSelectVendor">
    <DataMappingRule description="Mapping from Customer" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-03-14" id="vqSelectVendor" position="vq_dataMappingRule.xlsx,vqSelectVendor,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>contacts</srcFieldId>
          <dstFieldId>vqContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,18">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>vqContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>vqContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,24">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqSelectVendorPreProcessor</implementationClass>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectVendor,25">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqSelectVendorPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vpContactSelect" position="vq_dataMappingRule.xlsx,vpContactSelect">
    <DataMappingRule description="VQ Select Contact Mapping" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="vpContactSelect" position="vq_dataMappingRule.xlsx,vpContactSelect,1" srcEntityName="VendorContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>vqContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vpContactSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>vqContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqContactCopy" position="vq_dataMappingRule.xlsx,vqContactCopy">
    <DataMappingRule description="VQ Contact Copy Mapping" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqContactCopy" position="vq_dataMappingRule.xlsx,vqContactCopy,1" srcEntityName="VqContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqContactCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqContactCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqAddressSelect" position="vq_dataMappingRule.xlsx,vqAddressSelect">
    <DataMappingRule description="VQ Select Address Mapping" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqAddressSelect" position="vq_dataMappingRule.xlsx,vqAddressSelect,1" srcEntityName="VendorAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>vqAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>vqAddress.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>vqAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressSelect,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>vqAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqAddressCopy" position="vq_dataMappingRule.xlsx,vqAddressCopy">
    <DataMappingRule description="VQ Address Copy Mapping" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqAddressCopy" position="vq_dataMappingRule.xlsx,vqAddressCopy,1" srcEntityName="VqAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqNewRfs" position="vq_dataMappingRule.xlsx,vqNewRfs">
    <DataMappingRule description="VQ Create Request For Specification" domain="/" dstEntityName="Rfs" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqNewRfs" position="vq_dataMappingRule.xlsx,vqNewRfs,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,9">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorId</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfsItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,11">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>rfsItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>rfsItems.itemVq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqNewRfs,16">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqNewRfsProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqSelectItem" position="vq_dataMappingRule.xlsx,vqSelectItem">
    <DataMappingRule description="Mapping from Item to VQ" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-02-20" id="vqSelectItem" position="vq_dataMappingRule.xlsx,vqSelectItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,11">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,12">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,13">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord</srcFieldId>
          <dstFieldId>sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqSelectItem,20">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqSelectItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="unitCostByQuantityCopy" position="vq_dataMappingRule.xlsx,unitCostByQuantityCopy">
    <DataMappingRule description="Mapping for Copy Vq UnitCostByQuantity" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2018-09-27" id="unitCostByQuantityCopy" position="vq_dataMappingRule.xlsx,unitCostByQuantityCopy,1" srcEntityName="UnitCostByQuantity" srcEntityVersion="1" status="1" updatedDate="2018-09-27">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,unitCostByQuantityCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostByQuantityCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>unitCostByQuantities</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="unitCostDetailCopyToColorSize" position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize">
    <DataMappingRule description="Mapping for Copy Vq UnitCostByQuantity" domain="/" dstEntityName="VqUnitCostDetail" dstEntityVersion="1" effectiveDate="2018-09-27" id="unitCostDetailCopyToColorSize" position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,1" srcEntityName="VqUnitCostDetail" srcEntityVersion="1" status="1" updatedDate="2018-09-27">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,9">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCostBreakdown</srcFieldId>
          <dstFieldId>unitCostBreakdown</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,10">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>unitsPerInner</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,11">
          <mappingType>Field</mappingType>
          <srcFieldId>innersPerOuter</srcFieldId>
          <dstFieldId>innersPerOuter</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,12">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>unitsPerOuter</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,13">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerCbm</srcFieldId>
          <dstFieldId>unitsPerCbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,14">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerCFT</srcFieldId>
          <dstFieldId>unitsPerCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,15">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCbm</srcFieldId>
          <dstFieldId>outerCartonCbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,16">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,17">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonL</srcFieldId>
          <dstFieldId>outerCartonL</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,18">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonW</srcFieldId>
          <dstFieldId>outerCartonW</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,19">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonH</srcFieldId>
          <dstFieldId>outerCartonH</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,20">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonGrossWeight</srcFieldId>
          <dstFieldId>outerCartonGrossWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,21">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonNetWeight</srcFieldId>
          <dstFieldId>outerCartonNetWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,22">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCartonL</srcFieldId>
          <dstFieldId>innerCartonL</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,23">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCartonW</srcFieldId>
          <dstFieldId>innerCartonW</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,24">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCartonH</srcFieldId>
          <dstFieldId>innerCartonH</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,25">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCartonGrossWeight</srcFieldId>
          <dstFieldId>innerCartonGrossWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,26">
          <mappingType>Field</mappingType>
          <srcFieldId>innerCartonNetWeight</srcFieldId>
          <dstFieldId>innerCartonNetWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,27">
          <mappingType>Field</mappingType>
          <srcFieldId>casePackL</srcFieldId>
          <dstFieldId>casePackL</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,28">
          <mappingType>Field</mappingType>
          <srcFieldId>casePackW</srcFieldId>
          <dstFieldId>casePackW</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,29">
          <mappingType>Field</mappingType>
          <srcFieldId>casePackH</srcFieldId>
          <dstFieldId>casePackH</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,30">
          <mappingType>Field</mappingType>
          <srcFieldId>casePackGrossWeight</srcFieldId>
          <dstFieldId>casePackGrossWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopyToColorSize,31">
          <mappingType>Field</mappingType>
          <srcFieldId>casePackNetWeight</srcFieldId>
          <dstFieldId>casePackNetWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqAddOpenCostingTemplate" position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate">
    <DataMappingRule description="Mapping for Copy OpenCostingTemplate" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2018-09-27" id="vqAddOpenCostingTemplate" position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,1" srcEntityName="OpenCostingTemplate" srcEntityVersion="1" status="1" updatedDate="2018-09-27">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>additionalCosts</srcFieldId>
          <dstFieldId>vqAdditionalCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled=false</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromOpenCostingTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqAddOpenCostingTemplate,16">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqAddOpenCostingTemplateProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="copyFromExistingVq" position="vq_dataMappingRule.xlsx,copyFromExistingVq">
    <DataMappingRule description="Mapping from Copy From Existing Vq" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2015-09-04" id="copyFromExistingVq" position="vq_dataMappingRule.xlsx,copyFromExistingVq,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2015-09-04">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,9">
          <mappingType>Field</mappingType>
          <srcFieldId>shortListed</srcFieldId>
          <dstFieldId>shortListed</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,10">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,11">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>plannedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,12">
          <mappingType>Field</mappingType>
          <srcFieldId>targetPrice</srcFieldId>
          <dstFieldId>targetPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,13">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorRebate</srcFieldId>
          <dstFieldId>vendorRebate</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,15">
          <mappingType>Field</mappingType>
          <srcFieldId>quoteDate</srcFieldId>
          <dstFieldId>quoteDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,16">
          <mappingType>Field</mappingType>
          <srcFieldId>effectiveFrom</srcFieldId>
          <dstFieldId>effectiveFrom</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,17">
          <mappingType>Field</mappingType>
          <srcFieldId>expiryDate</srcFieldId>
          <dstFieldId>expiryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE.plusDays(getDomainAttributeAsInteger('vq.defaultQuoteExpiryDays'))</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,18">
          <mappingType>Field</mappingType>
          <srcFieldId>deliveryFrequency</srcFieldId>
          <dstFieldId>deliveryFrequency</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,19">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,21">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemName</srcFieldId>
          <dstFieldId>vendorItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,22">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDesc</srcFieldId>
          <dstFieldId>vendorItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,23">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>contactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,24">
          <mappingType>Field</mappingType>
          <srcFieldId>contactEmail</srcFieldId>
          <dstFieldId>contactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,25">
          <mappingType>Field</mappingType>
          <srcFieldId>contactTelNo</srcFieldId>
          <dstFieldId>contactTelNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,26">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDescription</srcFieldId>
          <dstFieldId>shortDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,27">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,28">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>unitsPerInner</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,29">
          <mappingType>Field</mappingType>
          <srcFieldId>innersPerOuter</srcFieldId>
          <dstFieldId>innersPerOuter</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,30">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>unitsPerOuter</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,31">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerCbm</srcFieldId>
          <dstFieldId>unitsPerCbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,32">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerCFT</srcFieldId>
          <dstFieldId>unitsPerCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,33">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCbm</srcFieldId>
          <dstFieldId>outerCartonCbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,34">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,35">
          <mappingType>Field</mappingType>
          <srcFieldId>prodWeight</srcFieldId>
          <dstFieldId>prodWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,36">
          <mappingType>Field</mappingType>
          <srcFieldId>cartonRemark</srcFieldId>
          <dstFieldId>cartonRemark</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,37">
          <mappingType>Field</mappingType>
          <srcFieldId>ft20Qty</srcFieldId>
          <dstFieldId>ft20Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,38">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40Qty</srcFieldId>
          <dstFieldId>ft40Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,39">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40HcQty</srcFieldId>
          <dstFieldId>ft40HcQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,40">
          <mappingType>Field</mappingType>
          <srcFieldId>ft45Qty</srcFieldId>
          <dstFieldId>ft45Qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,41">
          <mappingType>Field</mappingType>
          <srcFieldId>containerRemarks</srcFieldId>
          <dstFieldId>containerRemarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,42">
          <mappingType>Field</mappingType>
          <srcFieldId>truckNumber</srcFieldId>
          <dstFieldId>truckNumber</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,43">
          <mappingType>Field</mappingType>
          <srcFieldId>palletised</srcFieldId>
          <dstFieldId>palletised</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,44">
          <mappingType>Field</mappingType>
          <srcFieldId>ti</srcFieldId>
          <dstFieldId>ti</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,45">
          <mappingType>Field</mappingType>
          <srcFieldId>hi</srcFieldId>
          <dstFieldId>hi</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,46">
          <mappingType>Field</mappingType>
          <srcFieldId>unitPerPallet</srcFieldId>
          <dstFieldId>unitPerPallet</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,47">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>referenceVq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,48">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,49">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,50">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,51">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,52">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,53">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,54">
          <mappingType>Section</mappingType>
          <srcFieldId>costBreakdownCurrency</srcFieldId>
          <dstFieldId>costBreakdownCurrency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity.openCosting=true</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,55">
          <mappingType>Section</mappingType>
          <srcFieldId>openCostingTemplate</srcFieldId>
          <dstFieldId>openCostingTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity.openCosting=true</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,56">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,57">
          <mappingType>Section</mappingType>
          <srcFieldId>cartonMaterial</srcFieldId>
          <dstFieldId>cartonMaterial</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,58">
          <mappingType>Section</mappingType>
          <srcFieldId>containerType</srcFieldId>
          <dstFieldId>containerType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,59">
          <mappingType>Section</mappingType>
          <srcFieldId>containerSize</srcFieldId>
          <dstFieldId>containerSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,60">
          <mappingType>Section</mappingType>
          <srcFieldId>loadingMethod</srcFieldId>
          <dstFieldId>loadingMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,61">
          <mappingType>Section</mappingType>
          <srcFieldId>truckType</srcFieldId>
          <dstFieldId>truckType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,62">
          <mappingType>Section</mappingType>
          <srcFieldId>palletType</srcFieldId>
          <dstFieldId>palletType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,63">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,64">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,65">
          <mappingType>Section</mappingType>
          <srcFieldId>subContractor</srcFieldId>
          <dstFieldId>subContractor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,66">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>shipmentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,67">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,68">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,69">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,70">
          <mappingType>Section</mappingType>
          <srcFieldId>unitCostByQuantities</srcFieldId>
          <dstFieldId>unitCostByQuantities</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,71">
          <mappingType>Section</mappingType>
          <srcFieldId>vqCarton</srcFieldId>
          <dstFieldId>vqCarton</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,72">
          <mappingType>Section</mappingType>
          <srcFieldId>vqContact</srcFieldId>
          <dstFieldId>vqContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,73">
          <mappingType>Section</mappingType>
          <srcFieldId>vqAddress</srcFieldId>
          <dstFieldId>vqAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,74">
          <mappingType>Section</mappingType>
          <srcFieldId>vqImage</srcFieldId>
          <dstFieldId>vqImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,75">
          <mappingType>Section</mappingType>
          <srcFieldId>vqAttachment</srcFieldId>
          <dstFieldId>vqAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,76">
          <mappingType>Section</mappingType>
          <srcFieldId>vqOtherCharges</srcFieldId>
          <dstFieldId>vqOtherCharges</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,77">
          <mappingType>Section</mappingType>
          <srcFieldId>vqComponentCosts</srcFieldId>
          <dstFieldId>vqComponentCosts</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,copyFromExistingVq,81">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CopyFromExistingVqPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="unitCostDetailCopy" position="vq_dataMappingRule.xlsx,unitCostDetailCopy">
    <DataMappingRule description="Mapping for Copy Vq UnitCostDetail" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2018-09-27" id="unitCostDetailCopy" position="vq_dataMappingRule.xlsx,unitCostDetailCopy,1" srcEntityName="VqUnitCostDetail" srcEntityVersion="1" status="1" updatedDate="2020-06-19">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,unitCostDetailCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vqUnitCostDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="costBreakSelectComponent" position="vq_dataMappingRule.xlsx,costBreakSelectComponent">
    <DataMappingRule description="Mapping from Componet to CostBreakMaterial" domain="/" dstEntityName="VqComponentCost" dstEntityVersion="1" effectiveDate="2012-02-20" id="costBreakSelectComponent" position="vq_dataMappingRule.xlsx,costBreakSelectComponent,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2014-01-03">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,10">
          <mappingType>Field</mappingType>
          <srcFieldId>composition</srcFieldId>
          <dstFieldId>composition</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,11">
          <mappingType>Field</mappingType>
          <srcFieldId>consumption</srcFieldId>
          <dstFieldId>consumption</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,12">
          <mappingType>Field</mappingType>
          <srcFieldId>consumptionUOM</srcFieldId>
          <dstFieldId>consumptionUOM</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,13">
          <mappingType>Field</mappingType>
          <srcFieldId>wastagePercentage</srcFieldId>
          <dstFieldId>wastage</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,14">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,15">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>imageAttachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,16">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,17">
          <mappingType>Section</mappingType>
          <srcFieldId>materialSubType</srcFieldId>
          <dstFieldId>materialSubType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,18">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,19">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectComponent,20">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>component</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="costBreakSelectMaterialPalette" position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette">
    <DataMappingRule description="Mapping from MaterialPalette to CostBreakMaterial" domain="/" dstEntityName="VqComponentCost" dstEntityVersion="1" effectiveDate="2014-11-05" id="costBreakSelectMaterialPalette" position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,1" srcEntityName="MaterialPaletteMaterial" srcEntityVersion="1" status="1" updatedDate="2014-11-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.componentId.itemName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>composition</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.componentId.composition</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>consumption</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.consumption</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>consumptionUOM</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.componentId.consumptionUOM</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>wastage</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.wastagePercentage</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.componentId.unitCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.uom</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity.componentId.currency</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,17">
          <mappingType>Section</mappingType>
          <srcFieldId>componentId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,18">
          <mappingType>Section</mappingType>
          <srcFieldId>componentId.fileId</srcFieldId>
          <dstFieldId>imageAttachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,19">
          <mappingType>Section</mappingType>
          <srcFieldId>componentId.materialType</srcFieldId>
          <dstFieldId>materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,costBreakSelectMaterialPalette,20">
          <mappingType>Section</mappingType>
          <srcFieldId>componentId.materialSubType</srcFieldId>
          <dstFieldId>materialSubType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="vqOtherRequirementSelectTemplat" position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat">
    <DataMappingRule description="" domain="/" dstEntityName="Vq" dstEntityVersion="1" effectiveDate="2012-01-11" id="vqOtherRequirementSelectTemplate" position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,1" srcEntityName="FactoryAuditTemplate" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,9">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateChecklists</srcFieldId>
          <dstFieldId>vqOtherRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!='1'</condition>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,12">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,13">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,14">
          <mappingType>Field</mappingType>
          <srcFieldId>fieldType</srcFieldId>
          <dstFieldId>fieldType</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,15">
          <mappingType>Field</mappingType>
          <srcFieldId>isMandatory</srcFieldId>
          <dstFieldId>isMandatory</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,16">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDropdown</srcFieldId>
          <dstFieldId>contentDropdown</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,17">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDate</srcFieldId>
          <dstFieldId>contentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,18">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDecimal</srcFieldId>
          <dstFieldId>contentDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,19">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueImage</srcFieldId>
          <dstFieldId>contentImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,20">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueNumber</srcFieldId>
          <dstFieldId>contentNumber</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,21">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueTextarea</srcFieldId>
          <dstFieldId>contentTextarea</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,22">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueText</srcFieldId>
          <dstFieldId>contentText</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,23">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateOptions</srcFieldId>
          <dstFieldId>vqOtherRequirements.vqOtherRequirementContents</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,24">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,25">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,26">
          <mappingType>Field</mappingType>
          <srcFieldId>enableTextbox</srcFieldId>
          <dstFieldId>enableTextbox</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,27">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,28">
          <mappingType>Field</mappingType>
          <srcFieldId>isChecked</srcFieldId>
          <dstFieldId>isChecked</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,29">
          <mappingType>Field</mappingType>
          <srcFieldId>textboxText</srcFieldId>
          <dstFieldId>textboxText</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqOtherRequirementSelectTemplat,33">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.VqSelectOtherReqTemplatePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vqNewItem" position="vq_dataMappingRule.xlsx,vqNewItem">
    <DataMappingRule description="Mapping from Adopt as New Item" domain="/" dstEntityName="Item" dstEntityVersion="1" effectiveDate="2014-11-05" id="vqNewItem" position="vq_dataMappingRule.xlsx,vqNewItem,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2014-11-05">
      <elements id="mappingRule">
        <element position="vq_dataMappingRule.xlsx,vqNewItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>$FieldNotUpdate=$ALL</mappedValue>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>item.productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemType</srcFieldId>
          <dstFieldId>itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>item.isOrderIndividual</srcFieldId>
          <dstFieldId>isOrderIndividual</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>refItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemVendorFact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>$FieldNotUpdate=$ALL</mappedValue>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>itemVendorFact.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>itemVendorFact.vendorItemNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>itemVendorFact.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>vqImage</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vqImage.fileId</srcFieldId>
          <dstFieldId>itemImage.fileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>vqImage.imageTypeId</srcFieldId>
          <dstFieldId>itemImage.imageTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemOtherRequirements</srcFieldId>
          <dstFieldId>itemOtherRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>item.otherRequirementTempId</srcFieldId>
          <dstFieldId>otherRequirementTempId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="vq_dataMappingRule.xlsx,vqNewItem,27">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.cbxsoftware.rest.service.inheritance.processor.VqNewItemPreProcessor</implementationClass>
        </element>
        <element position="vq_dataMappingRule.xlsx,vqNewItem,28">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.cbxsoftware.rest.service.inheritance.processor.VqNewItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
