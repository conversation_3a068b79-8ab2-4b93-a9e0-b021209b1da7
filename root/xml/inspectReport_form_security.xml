<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="inspectReport" position="inspectReport_form_security.xlsx">
  <sheet id="_system" position="inspectReport_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="inspectReport_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="inspectReport_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,_system,10">
          <updatedOn>2015/Jun/10</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="inspectReport_form_security.xlsx,generalInfo">
    <GeneralInfo position="inspectReport_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="inspectReport_form_security.xlsx,condition">
    <ConditionList position="inspectReport_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,condition,4">
          <conditionId>statusDraft</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,5">
          <conditionId>statusOfficial</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,6">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,7">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,8">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,9">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,10">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,11">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,12">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,13">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,14">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,15">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,16">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,17">
          <conditionId>isInspectionFormatByLotWithPOM</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,18">
          <conditionId>isContainMeasurement</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,19">
          <conditionId>isContainMeasurement4ItemSet</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,20">
          <conditionId>isExternalDomain</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,21">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,22">
          <conditionId>isInspectionFormatByItem</conditionId>
        </element>
        <element position="inspectReport_form_security.xlsx,condition,23">
          <conditionId>isInspectionFormatByLot</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="inspectReport_form_security.xlsx,default">
    <ActionConditionMatrix position="inspectReport_form_security.xlsx,default,1">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,5">
          <actionId>editDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,6">
          <actionId>amendDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,7">
          <actionId>saveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,8">
          <actionId>baseSaveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,9">
          <actionId>saveAndConfirm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,10">
          <actionId>sendToVendor</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,11">
          <actionId>sendToBuyer</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,12">
          <actionId>discardDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,13">
          <actionId>copyDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,14">
          <actionId>activateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,15">
          <actionId>deactivateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,16">
          <actionId>cancelDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,17">
          <actionId>loadDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,18">
          <actionId>inspectReportViewCapa</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,19">
          <actionId>initializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,20">
          <actionId>draftStatus</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,21">
          <actionId>officialStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>disallowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,22">
          <actionId>loadAQLDDStore</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,23">
          <actionId>loadInspectionLevelDDStore</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,24">
          <actionId>inspectReportCustom01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,25">
          <actionId>inspectReportCustom02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,26">
          <actionId>inspectReportCustom03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,27">
          <actionId>inspectReportCustom04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,28">
          <actionId>inspectReportCustom05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,29">
          <actionId>inspectReportCustom06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,30">
          <actionId>inspectReportCustom07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,31">
          <actionId>inspectReportCustom08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,32">
          <actionId>inspectReportCustom09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,33">
          <actionId>inspectReportCustom10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,34">
          <actionId>customPrint01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,35">
          <actionId>customPrint02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,36">
          <actionId>customPrint03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,37">
          <actionId>customPrint04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,38">
          <actionId>customPrint05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,39">
          <actionId>customPrint06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,40">
          <actionId>customPrint07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,41">
          <actionId>customPrint08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,42">
          <actionId>customPrint09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,43">
          <actionId>customPrint10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,44">
          <actionId>customExport01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,45">
          <actionId>customExport02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,46">
          <actionId>customExport03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,47">
          <actionId>customExport04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,48">
          <actionId>customExport05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,49">
          <actionId>customExport06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,50">
          <actionId>customExport07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,51">
          <actionId>customExport08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,52">
          <actionId>customExport09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,53">
          <actionId>customExport10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,54">
          <actionId>markAsCustomStatus01Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,55">
          <actionId>markAsCustomStatus02Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,56">
          <actionId>markAsCustomStatus03Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,57">
          <actionId>markAsCustomStatus04Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,58">
          <actionId>markAsCustomStatus05Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,59">
          <actionId>markAsCustomStatus06Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,60">
          <actionId>markAsCustomStatus07Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,61">
          <actionId>markAsCustomStatus08Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,62">
          <actionId>markAsCustomStatus09Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,63">
          <actionId>markAsCustomStatus10Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,64">
          <actionId>reinitializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,65">
          <actionId>refreshCpmTemplate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectReport_form_security.xlsx,default,66">
          <actionId>refreshCpmPlanDate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isInspectionFormatByLotWithPOM>allowed</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>allowed</isContainMeasurement>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="inspectReport_form_security.xlsx,default,69">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,default,72">
          <componentId>ui</componentId>
          <statusDraft>editable</statusDraft>
          <statusOfficial>editable</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isNotLatest>editable</isNotLatest>
          <isInspectionFormatByLotWithPOM>editable</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>editable</isContainMeasurement>
          <isContainMeasurement4ItemSet>editable</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>editable</isInspectionFormatByItem>
          <isInspectionFormatByLot>editable</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,73">
          <componentId>ui.tabHeader.generalSection</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>inherit</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,74">
          <componentId>ui.tabHeader.measurementResultSection</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>hidden</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,75">
          <componentId>ui.tabInspectReportItems.inspectReportItems.hierarchy</componentId>
          <statusDraft>readonly</statusDraft>
          <statusOfficial>readonly</statusOfficial>
          <docStatusActive>readonly</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
          <isNotLatest>readonly</isNotLatest>
          <isInspectionFormatByLotWithPOM>readonly</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>readonly</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,76">
          <componentId>ui.tabDetails.inspectReportDetails.noOfAttachments</componentId>
          <statusDraft>readonly</statusDraft>
          <statusOfficial>readonly</statusOfficial>
          <docStatusActive>readonly</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
          <isNotLatest>readonly</isNotLatest>
          <isInspectionFormatByLotWithPOM>readonly</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>readonly</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,77">
          <componentId>ui.tabMeasurement</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>hidden</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,78">
          <componentId>ui.tabMeasurement.inspectReportMeasDetails.refreshLastItem</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>hidden</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,79">
          <componentId>ui.tabMeasurement.inspectReportMeasDetails.itemNo</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>inherit</isContainMeasurement>
          <isContainMeasurement4ItemSet>hidden</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,80">
          <componentId>ui.inspectReportLinkbar.addToFavorites</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>inherit</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,81">
          <componentId>ui.inspectReportLinkbar.approval</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>inherit</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,82">
          <componentId>ui.inspectReportLinkbar.relatedActivities</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isInspectionFormatByLotWithPOM>inherit</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>inherit</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,83">
          <componentId>ui.inspectReportMenubar.editDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>editable</isNotLatest>
          <isInspectionFormatByLotWithPOM>editable</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>editable</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,84">
          <componentId>ui.inspectReportMenubar.amendDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>editable</isNotLatest>
          <isInspectionFormatByLotWithPOM>editable</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>editable</isContainMeasurement>
          <isContainMeasurement4ItemSet>inherit</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>inherit</isInspectionFormatByItem>
          <isInspectionFormatByLot>inherit</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,85">
          <componentId>ui.tabInspectReportItems.inspectReportItems.selectFromLotIB</componentId>
          <statusDraft>editable</statusDraft>
          <statusOfficial>editable</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>editable</docStatusInactive>
          <docStatusCanceled>editable</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>editable</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isNotLatest>editable</isNotLatest>
          <isInspectionFormatByLotWithPOM>editable</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>editable</isContainMeasurement>
          <isContainMeasurement4ItemSet>editable</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>hidden</isInspectionFormatByItem>
          <isInspectionFormatByLot>editable</isInspectionFormatByLot>
        </element>
        <element position="inspectReport_form_security.xlsx,default,86">
          <componentId>ui.tabInspectReportItems.inspectReportItems.selectFromItemIB</componentId>
          <statusDraft>editable</statusDraft>
          <statusOfficial>editable</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>editable</docStatusInactive>
          <docStatusCanceled>editable</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>editable</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isNotLatest>editable</isNotLatest>
          <isInspectionFormatByLotWithPOM>editable</isInspectionFormatByLotWithPOM>
          <isContainMeasurement>editable</isContainMeasurement>
          <isContainMeasurement4ItemSet>editable</isContainMeasurement4ItemSet>
          <isInspectionFormatByItem>editable</isInspectionFormatByItem>
          <isInspectionFormatByLot>hidden</isInspectionFormatByLot>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="inspectReport_form_security.xlsx,acl">
    <ActionRule position="inspectReport_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,5">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,6">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,7">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,8">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,9">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,10">
          <actionId>sendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,11">
          <actionId>sendToBuyer</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>not-has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,12">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,13">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,14">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,15">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,16">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,17">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,18">
          <actionId>inspectReportViewCapa</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,19">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,20">
          <actionId>officialStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,21">
          <actionId>draftStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,22">
          <actionId>loadAQLDDStore</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,23">
          <actionId>loadInspectionLevelDDStore</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>has</inspectReport.Editor>
          <inspectReport.ReadOnly>has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,24">
          <actionId>inspectReportCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,25">
          <actionId>inspectReportCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,26">
          <actionId>inspectReportCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,27">
          <actionId>inspectReportCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,28">
          <actionId>inspectReportCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,29">
          <actionId>inspectReportCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,30">
          <actionId>inspectReportCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,31">
          <actionId>inspectReportCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,32">
          <actionId>inspectReportCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,33">
          <actionId>inspectReportCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,34">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,35">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,36">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,37">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,38">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,39">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,40">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,41">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,42">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,43">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,44">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,45">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,46">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,47">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,48">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,49">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,50">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,51">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,52">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,53">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,54">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,55">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,56">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,57">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,58">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,59">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,60">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,61">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,62">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,63">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,64">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>not-has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,65">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>not-has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,66">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectReport.Author>not-has</inspectReport.Author>
          <inspectReport.Editor>not-has</inspectReport.Editor>
          <inspectReport.ReadOnly>not-has</inspectReport.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="inspectReport_form_security.xlsx,acl,69">
      <elements id="default">
        <element position="inspectReport_form_security.xlsx,acl,72">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,73">
          <componentId>ui.tabInspectReportItems</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,74">
          <componentId>ui.tabDetails</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,75">
          <componentId>ui.tabMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,76">
          <componentId>ui.tabImages</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,77">
          <componentId>ui.inspectReportLinkbar.approval</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,78">
          <componentId>ui.tabChecklist</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,79">
          <componentId>ui.tabMobileActivities</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,80">
          <componentId>ui.tabHeader.vendorInfoSection.vendor</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,81">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,82">
          <componentId>ui.tabOther</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="inspectReport_form_security.xlsx,acl,83">
          <componentId>ui.tabParties</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="inspectReport_form_security.xlsx,acl,86">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
