<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<popup module="trigger" position="trigger_popup.xlsx">
  <sheet id="popSelectEventType" position="trigger_popup.xlsx,popSelectEventType">
    <PopupCustomWin id="popSelectEventType" label="Define Event Details" position="trigger_popup.xlsx,popSelectEventType,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectEventType,8">
          <id>eventTypeList</id>
          <type>Grid</type>
        </element>
        <element position="trigger_popup.xlsx,popSelectEventType,9">
          <id>popSelectEventTypeToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Grid entityName="TriggerListener" id="eventTypeList" label="Event Type" position="trigger_popup.xlsx,popSelectEventType,12" ratio="100%" rowRenderer="com.core.cbx.trigger.form.PopupSelectEventTypeRowRenderer" selectionMode="" showHint="FALSE">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="trigger_popup.xlsx,popSelectEventType,22">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>400</size>
          <prefix/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes, 5:Date Checking</format>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
        </element>
      </elements>
    </Grid>
    <Toolbar align="" id="popSelectEventTypeToolbar" position="trigger_popup.xlsx,popSelectEventType,25">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectEventType,32">
          <id>popSelectEventTypeButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popSelectEventTypeButtons" label="" position="trigger_popup.xlsx,popSelectEventType,35">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectEventType,42">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCancleAndCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popDefActionPerform" position="trigger_popup.xlsx,popDefActionPerform">
    <PopupCustomWin id="popDefActionPerform" label="Define Event Details" position="trigger_popup.xlsx,popDefActionPerform,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefActionPerform,8">
          <id>popDefActionPerformSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,9">
          <id>popDefActionPerformToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerListener" hideTitle="TRUE" id="popDefActionPerformSection" label="" position="trigger_popup.xlsx,popDefActionPerform,12" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popDefActionPerform,22">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <data/>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,23">
          <id>eventModule</id>
          <label>Source of Event</label>
          <type>Dropdown</type>
          <data>listMainModules</data>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade>actionId</cascade>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{label}</format>
          <viewName/>
          <comboKey>module</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,24">
          <id>docCondition</id>
          <label>Condition</label>
          <type>Selection</type>
          <data/>
          <winTitle>Condition Lookup</winTitle>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping>docCondition</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popupConditionView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,25">
          <id>actionId</id>
          <label>Action</label>
          <type>Dropdown</type>
          <data>listAccessObjActionId</data>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr>'{eventModule}'==objectId</cascadeExpr>
          <action/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{actionId}</format>
          <viewName/>
          <comboKey>actionId</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,26">
          <id>customizedCondition</id>
          <label>Customized Condition</label>
          <type>Text</type>
          <data/>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popDefActionPerformToolbar" position="trigger_popup.xlsx,popDefActionPerform,29">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefActionPerform,36">
          <id>popDefActionPerformButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popDefActionPerformButtons" label="" position="trigger_popup.xlsx,popDefActionPerform,39">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefActionPerform,46">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopDefActionPerformOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefActionPerform,47">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popDeFieldChange" position="trigger_popup.xlsx,popDeFieldChange">
    <PopupCustomWin id="popDeFieldChange" label="Define Event Details" position="trigger_popup.xlsx,popDeFieldChange,1" size="">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDeFieldChange,8">
          <id>popDeFieldChangeSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,9">
          <id>criterias</id>
          <type>Grid</type>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,10">
          <id>popDeFieldChangeToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerListener" hideTitle="TRUE" id="popDeFieldChangeSection" label="" position="trigger_popup.xlsx,popDeFieldChange,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popDeFieldChange,23">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <winTitle/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,24">
          <id>eventModule</id>
          <label>Source of Event</label>
          <type>Dropdown</type>
          <data>listMainModules</data>
          <action/>
          <actionParams/>
          <winTitle/>
          <dataFrom/>
          <cascade>eventLevel,anchorDateFieldId</cascade>
          <cascadeExpr/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{label}</format>
          <viewName/>
          <comboKey>module</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,25">
          <id>eventLevel</id>
          <label>Level</label>
          <type>Dropdown</type>
          <data>listMainLevels</data>
          <action/>
          <actionParams/>
          <winTitle/>
          <dataFrom/>
          <cascade>anchorDateFieldId</cascade>
          <cascadeExpr>'{eventModule}'==module</cascadeExpr>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{level}</format>
          <viewName/>
          <comboKey>level</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,26">
          <id>docCondition</id>
          <label>Condition</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <winTitle>Condition Lookup</winTitle>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single>TRUE</single>
          <mapping>docCondition</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popupConditionView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,27">
          <id>requireType</id>
          <label>Require</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <winTitle/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,28">
          <id>customizedCondition</id>
          <label>Customized Condition</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <winTitle/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
      </elements>
    </Section>
    <Grid entityName="TriggerListenerCriteria" id="criterias" label="Field Changes" position="trigger_popup.xlsx,popDeFieldChange,31" ratio="100%" rowRenderer="com.core.cbx.trigger.form.ListenerCriteriaRowRenderer" selectionMode="Multiple" showHint="FALSE">
      <elements id="buttons">
        <element position="trigger_popup.xlsx,popDeFieldChange,38">
          <id>addTriggerListener</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,39">
          <id>delTriggerListener</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="trigger_popup.xlsx,popDeFieldChange,43">
          <id>sourceIdEntity</id>
          <label>Field ID</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName>popupDocumentFieldView</viewName>
          <format>{fieldName}</format>
          <single>TRUE</single>
          <winTitle>Document Field Lookup</winTitle>
          <viewParams>module=$ds.{eventModule}</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <rendererClass>com.core.cbx.trigger.form.SourceIdEntitySelectionCellRenderer</rendererClass>
          <readonly/>
          <popupFormat>{fieldName}</popupFormat>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,44">
          <id>fromValueType</id>
          <label>From Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,45">
          <id>fromValue</id>
          <label>From Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,46">
          <id>toValueType</id>
          <label>To Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,47">
          <id>toValue</id>
          <label>To Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
          <popupFormat/>
        </element>
      </elements>
    </Grid>
    <Toolbar align="" id="popDeFieldChangeToolbar" position="trigger_popup.xlsx,popDeFieldChange,50">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDeFieldChange,57">
          <id>popDeFieldChangeButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popDeFieldChangeButtons" label="" position="trigger_popup.xlsx,popDeFieldChange,60">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDeFieldChange,67">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopDeFieldChangeOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDeFieldChange,68">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popDefAprvFieldChange" position="trigger_popup.xlsx,popDefAprvFieldChange">
    <PopupCustomWin id="popDefAprvFieldChange" label="Define Event Details" position="trigger_popup.xlsx,popDefAprvFieldChange,1" size="">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,8">
          <id>popDefAprvFieldChangeSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,9">
          <id>criterias</id>
          <type>Grid</type>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,10">
          <id>popDefAprvFieldChangeToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerListener" hideTitle="TRUE" id="popDefAprvFieldChangeSection" label="" position="trigger_popup.xlsx,popDefAprvFieldChange,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,23">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,24">
          <id>aprvTmplId</id>
          <label>Approval Template</label>
          <type>Selection</type>
          <winTitle>Approval Template Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single>TRUE</single>
          <mapping>aprvTmplId</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName>popAprvTmplView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,25">
          <id>docCondition</id>
          <label>Condition</label>
          <type>Selection</type>
          <winTitle>Condition Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single>TRUE</single>
          <mapping>docCondition</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popupConditionView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,26">
          <id>requireType</id>
          <label>Require</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,27">
          <id>customizedCondition</id>
          <label>Customized Condition</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
      </elements>
    </Section>
    <Grid entityName="TriggerListenerCriteria" id="criterias" label="Approval Field Changes" position="trigger_popup.xlsx,popDefAprvFieldChange,30" ratio="100%" rowRenderer="com.core.cbx.trigger.form.ListenerCriteriaRowRenderer" selectionMode="Multiple" showHint="FALSE">
      <elements id="buttons">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,37">
          <id>addTriggerListener</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,38">
          <id>delTriggerListener</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,42">
          <id>sourceGroupId</id>
          <label>Stage</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,43">
          <id>sourceIdEntity</id>
          <label>Field ID</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName>popupDocumentFieldView</viewName>
          <format>{fieldName}</format>
          <single>TRUE</single>
          <winTitle>Document Field Lookup</winTitle>
          <viewParams>module=aprvProfile&amp;entityType=main</viewParams>
          <allowDateFilter>TRUE</allowDateFilter>
          <rendererClass>com.core.cbx.trigger.form.SourceIdEntitySelectionCellRenderer</rendererClass>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,44">
          <id>fromValueType</id>
          <label>From Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,45">
          <id>fromValue</id>
          <label>From Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,46">
          <id>toValueType</id>
          <label>To Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,47">
          <id>toValue</id>
          <label>To Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
      </elements>
    </Grid>
    <Toolbar align="" id="popDefAprvFieldChangeToolbar" position="trigger_popup.xlsx,popDefAprvFieldChange,50">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,57">
          <id>popDefAprvFieldChangeButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popDefAprvFieldChangeButtons" label="" position="trigger_popup.xlsx,popDefAprvFieldChange,60">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,67">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopDefAprvFieldChangeOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefAprvFieldChange,68">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popDefCpmChange" position="trigger_popup.xlsx,popDefCpmChange">
    <PopupCustomWin id="popDefCpmChange" label="Define Event Details" position="trigger_popup.xlsx,popDefCpmChange,1" size="">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefCpmChange,8">
          <id>popDefCpmChangeSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,9">
          <id>criterias</id>
          <type>Grid</type>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,10">
          <id>popDefCpmChangeToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerListener" hideTitle="TRUE" id="popDefCpmChangeSection" label="" position="trigger_popup.xlsx,popDefCpmChange,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popDefCpmChange,23">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,24">
          <id>cpmTmplId</id>
          <label>CPM Template</label>
          <type>Selection</type>
          <winTitle>Critical Path Template Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single>TRUE</single>
          <mapping>cpmTmplId</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName>popCpmTmplView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,25">
          <id>docCondition</id>
          <label>Condition</label>
          <type>Selection</type>
          <winTitle>Condition Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single>TRUE</single>
          <mapping>docCondition</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popupConditionView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,26">
          <id>requireType</id>
          <label>Require</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,27">
          <id>customizedCondition</id>
          <label>Customized Condition</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <single/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
      </elements>
    </Section>
    <Grid entityName="TriggerListenerCriteria" id="criterias" label="CPM Field Changes" position="trigger_popup.xlsx,popDefCpmChange,30" ratio="100%" rowRenderer="com.core.cbx.trigger.form.ListenerCriteriaRowRenderer" selectionMode="Multiple" showHint="FALSE">
      <elements id="buttons">
        <element position="trigger_popup.xlsx,popDefCpmChange,37">
          <id>addTriggerListener</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,38">
          <id>delTriggerListener</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="trigger_popup.xlsx,popDefCpmChange,42">
          <id>sourceGroupId</id>
          <label>Milestone</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,43">
          <id>sourceIdEntity</id>
          <label>Field ID</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName>popupDocumentFieldView</viewName>
          <format>{fieldName}</format>
          <single>TRUE</single>
          <winTitle>Document Field Lookup</winTitle>
          <viewParams>module=cpmTask&amp;entityType=main</viewParams>
          <allowDateFilter>TRUE</allowDateFilter>
          <rendererClass>com.core.cbx.trigger.form.SourceIdEntitySelectionCellRenderer</rendererClass>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,44">
          <id>fromValueType</id>
          <label>From Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,45">
          <id>fromValue</id>
          <label>From Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,46">
          <id>toValueType</id>
          <label>To Value Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format>{name}</format>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,47">
          <id>toValue</id>
          <label>To Value</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <viewName/>
          <format/>
          <single/>
          <winTitle/>
          <viewParams/>
          <allowDateFilter/>
          <rendererClass/>
          <readonly/>
        </element>
      </elements>
    </Grid>
    <Toolbar align="" id="popDefCpmChangeToolbar" position="trigger_popup.xlsx,popDefCpmChange,50">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefCpmChange,57">
          <id>popDefCpmChangeButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popDefCpmChangeButtons" label="" position="trigger_popup.xlsx,popDefCpmChange,60">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefCpmChange,67">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopDefCpmChangeOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefCpmChange,68">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popDefDateChecking" position="trigger_popup.xlsx,popDefDateChecking">
    <PopupCustomWin id="popDefDateChecking" label="Define Event Details" position="trigger_popup.xlsx,popDefDateChecking,1">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefDateChecking,8">
          <id>popDefDateCheckingSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,9">
          <id>popDefDateCheckingToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerListener" id="popDefDateCheckingSection" label="Please note that the Event will be processed by the backend scheduler on a daily basis" position="trigger_popup.xlsx,popDefDateChecking,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popDefDateChecking,23">
          <id>eventType</id>
          <label>Event Type</label>
          <type>Label</type>
          <data/>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <hidden/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:Action Performed, 2:Field Changes, 3:Approval Field Changes, 4:CPM Field Changes, 5:Date Checking</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,24">
          <id>eventModule</id>
          <label>Source of Event</label>
          <type>Dropdown</type>
          <data>listMainModules</data>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade>dateField</cascade>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <hidden/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{label}</format>
          <viewName/>
          <comboKey>module</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,25">
          <id>eventEntity</id>
          <label>Apply to</label>
          <type>Dropdown</type>
          <data>listTabsAndTables</data>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade>dateField</cascade>
          <cascadeExpr>'{eventModule}'==module</cascadeExpr>
          <action/>
          <filterBy/>
          <single/>
          <hidden>TRUE</hidden>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey>applyToEntity</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,26">
          <id>dateField</id>
          <label>Date Field</label>
          <type>Dropdown</type>
          <data>listDateFieldIds</data>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr>'{eventModule}'==module</cascadeExpr>
          <action/>
          <filterBy/>
          <single/>
          <hidden/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{label}</format>
          <viewName/>
          <comboKey>fieldId</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,27">
          <id>noOfDays</id>
          <label>No. of Day(s)</label>
          <type>Number</type>
          <data/>
          <winTitle/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single/>
          <hidden/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,28">
          <id>docCondition</id>
          <label>Condition</label>
          <type>Selection</type>
          <data/>
          <winTitle>Condition Lookup</winTitle>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <action/>
          <filterBy/>
          <single>TRUE</single>
          <hidden/>
          <mapping>docCondition</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <viewName>popupConditionView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popDefDateCheckingToolbar" position="trigger_popup.xlsx,popDefDateChecking,31">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefDateChecking,38">
          <id>popDefDateCheckingButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popDefDateCheckingButtons" label="" position="trigger_popup.xlsx,popDefDateChecking,41">
      <elements id="default">
        <element position="trigger_popup.xlsx,popDefDateChecking,48">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopDefDateCheckingOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popDefDateChecking,49">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popSelectActionType" position="trigger_popup.xlsx,popSelectActionType">
    <PopupCustomWin id="popSelectActionType" label="Define Action Details" position="trigger_popup.xlsx,popSelectActionType,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectActionType,8">
          <id>actionTypeList</id>
          <type>Grid</type>
        </element>
        <element position="trigger_popup.xlsx,popSelectActionType,9">
          <id>popSelectActionTypeToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Grid entityName="TriggerAction" id="actionTypeList" label="Action Type" position="trigger_popup.xlsx,popSelectActionType,12" ratio="100%" rowRenderer="com.core.cbx.trigger.form.PopupSelectActionTypeRowRenderer" selectionMode="" showHint="FALSE">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="trigger_popup.xlsx,popSelectActionType,22">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>400</size>
          <prefix/>
          <format>1:CPM Milestone Update, 2:Send Notification, 3:Invoke API, 4:Initialize CPM,5:Re-initialize Responsible Party,6:Recalculate CPM Baseline</format>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
        </element>
      </elements>
    </Grid>
    <Toolbar align="" id="popSelectActionTypeToolbar" position="trigger_popup.xlsx,popSelectActionType,25">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectActionType,32">
          <id>popSelectActionTypeButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popSelectActionTypeButtons" label="" position="trigger_popup.xlsx,popSelectActionType,35">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSelectActionType,42">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCancleAndCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popCpmTaskUpdate" position="trigger_popup.xlsx,popCpmTaskUpdate">
    <PopupCustomWin id="popCpmTaskUpdate" label="Define Action Details" position="trigger_popup.xlsx,popCpmTaskUpdate,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,8">
          <id>popCpmTaskUpdateSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,9">
          <id>popCpmTaskUpdateSection2</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,10">
          <id>popCpmTaskUpdateSection3</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,11">
          <id>popCpmTaskUpdateToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popCpmTaskUpdateSection" label="" position="trigger_popup.xlsx,popCpmTaskUpdate,14" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,24">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:CPM Milestone Update, 2:Send Notification, 3:Invoke API</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,25">
          <id>cpmTemplates</id>
          <label>CPM Template</label>
          <type>Selection</type>
          <winTitle>Critical Path Template Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping>cpmTemplates</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName>popCpmTmplView</viewName>
          <comboKey>id</comboKey>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,26">
          <id>cpmTaskTemplates</id>
          <label>Milestone</label>
          <type>Selection</type>
          <winTitle>CPM Task Template Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping>cpmTaskTemplates</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{taskName}</format>
          <viewName>popTaskTmplView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,27">
          <id>cpmTriggerMessage1</id>
          <label>The fields below will be applied to the Milestone if they contain a value.</label>
          <type>Message</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass>cbx-sect-message</cssClass>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,28">
          <id>cpmTriggerMessage2</id>
          <label>If the field is empty, the original value of the Milestone will be kept.</label>
          <type>Message</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass>cbx-sect-message</cssClass>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,29">
          <id>cpmTriggerMessage3</id>
          <label>The number input in date fields will be added to the date the event has been triggered, then applied to the Milestone.</label>
          <type>Message</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass>cbx-sect-message</cssClass>
        </element>
      </elements>
    </Section>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popCpmTaskUpdateSection2" label="" position="trigger_popup.xlsx,popCpmTaskUpdate,32" ratio="8%,92%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,42">
          <id>updateStatusCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,43">
          <id>updateStatus</id>
          <label>Update Status to</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,44">
          <id>rejectReasonIdCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,45">
          <id>rejectReasonId</id>
          <label>Reason</label>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,46">
          <id>rejectDescriptionCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,47">
          <id>rejectDescription</id>
          <label>Reason Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,48">
          <id>percentageCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,49">
          <id>percentage</id>
          <label>%</label>
          <type>Decimal</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,50">
          <id>quantityCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,51">
          <id>quantity</id>
          <label>Quantity</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,52">
          <id>assigneeGroupCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,53">
          <id>assigneeGroup</id>
          <label>Assignee</label>
          <type>Selection</type>
          <winTitle>Assignee Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}{userName}{userOrGroupName}</format>
          <viewName>popAssigneesView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <cssClass>applyModule={$ds.applyModule}&amp;assigneeType='ResponsibleParties'</cssClass>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,54">
          <id>descriptionCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,55">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
      </elements>
    </Section>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popCpmTaskUpdateSection3" label="" position="trigger_popup.xlsx,popCpmTaskUpdate,58" ratio="8%,35%,57%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,68">
          <id>plannedStartCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,69">
          <id>plannedStart</id>
          <label>Baseline Start</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,70">
          <id>plannedStartDateField</id>
          <label/>
          <type>Selection</type>
          <winTitle>Document Field Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{fieldId}</format>
          <viewName>popupDocumentDateFieldView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <viewParams>entityType=main</viewParams>
          <extraParams>Selection_selectionFreeText=TRUE</extraParams>
          <popupFormat>{fieldId}</popupFormat>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,71">
          <id>plannedEndCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,72">
          <id>plannedEnd</id>
          <label>Baseline End</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,73">
          <id>plannedEndDateField</id>
          <label/>
          <type>Selection</type>
          <winTitle>Document Field Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{fieldId}</format>
          <viewName>popupDocumentDateFieldView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <viewParams>entityType=main</viewParams>
          <extraParams>Selection_selectionFreeText=TRUE</extraParams>
          <popupFormat>{fieldId}</popupFormat>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,74">
          <id>actualStartCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,75">
          <id>actualStart</id>
          <label>Latest Start</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,76">
          <id>actualStartDateField</id>
          <label/>
          <type>Selection</type>
          <winTitle>Document Field Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{fieldId}</format>
          <viewName>popupDocumentDateFieldView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <viewParams>entityType=main</viewParams>
          <extraParams>Selection_selectionFreeText=TRUE</extraParams>
          <popupFormat>{fieldId}</popupFormat>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,77">
          <id>actualEndCheckbox</id>
          <label/>
          <type>checkbox</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,78">
          <id>actualEnd</id>
          <label>Latest End</label>
          <type>Number</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <viewParams/>
          <extraParams/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,79">
          <id>actualEndDateField</id>
          <label/>
          <type>Selection</type>
          <winTitle>Document Field Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{fieldId}</format>
          <viewName>popupDocumentDateFieldView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
          <viewParams>entityType=main</viewParams>
          <extraParams>Selection_selectionFreeText=TRUE</extraParams>
          <popupFormat>{fieldId}</popupFormat>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popCpmTaskUpdateToolbar" position="trigger_popup.xlsx,popCpmTaskUpdate,82">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,89">
          <id>popCpmTaskUpdateButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popCpmTaskUpdateButtons" label="" position="trigger_popup.xlsx,popCpmTaskUpdate,92">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,99">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopCpmTaskUpdateOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popCpmTaskUpdate,100">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popSendNotification" position="trigger_popup.xlsx,popSendNotification">
    <PopupCustomWin id="popSendNotification" label="Define Action Details" position="trigger_popup.xlsx,popSendNotification,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSendNotification,8">
          <id>popSendNotificationSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popSendNotification,9">
          <id>popSendNotificationToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popSendNotificationSection" label="" position="trigger_popup.xlsx,popSendNotification,12" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popSendNotification,22">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:CPM Task Update, 2:Send Notification, 3:Invoke API</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
        </element>
        <element position="trigger_popup.xlsx,popSendNotification,23">
          <id>notificationId</id>
          <label>Notification Profile</label>
          <type>Selection</type>
          <winTitle>Notification Profile Lookup</winTitle>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single>TRUE</single>
          <rowspan/>
          <mapping>notificationId</mapping>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{profileName}</format>
          <viewName>popNotifiProfileView</viewName>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter>FALSE</allowDateFilter>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popSendNotificationToolbar" position="trigger_popup.xlsx,popSendNotification,26">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSendNotification,33">
          <id>popSendNotificationButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popSendNotificationButtons" label="" position="trigger_popup.xlsx,popSendNotification,36">
      <elements id="default">
        <element position="trigger_popup.xlsx,popSendNotification,43">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopSendNotificationOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popSendNotification,44">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popInvokeApi" position="trigger_popup.xlsx,popInvokeApi">
    <PopupCustomWin id="popInvokeApi" label="Define Action Details" position="trigger_popup.xlsx,popInvokeApi,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popInvokeApi,8">
          <id>popInvokeApiSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popInvokeApi,9">
          <id>popInvokeApiToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popInvokeApiSection" label="" position="trigger_popup.xlsx,popInvokeApi,12" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popInvokeApi,22">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:CPM Task Update, 2:Send Notification, 3:Invoke API</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
        </element>
        <element position="trigger_popup.xlsx,popInvokeApi,23">
          <id>customClass</id>
          <label>Custom Class</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
        </element>
        <element position="trigger_popup.xlsx,popInvokeApi,24">
          <id>customPayload</id>
          <label>Parameter</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popInvokeApiToolbar" position="trigger_popup.xlsx,popInvokeApi,27">
      <elements id="default">
        <element position="trigger_popup.xlsx,popInvokeApi,34">
          <id>popInvokeApiButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popInvokeApiButtons" label="" position="trigger_popup.xlsx,popInvokeApi,37">
      <elements id="default">
        <element position="trigger_popup.xlsx,popInvokeApi,44">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopInvokeApiOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popInvokeApi,45">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popReInitResponsibleParty" position="trigger_popup.xlsx,popReInitResponsibleParty">
    <PopupCustomWin id="popReInitResponsibleParty" label="Define Action Details" position="trigger_popup.xlsx,popReInitResponsibleParty,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,8">
          <id>popReInitResponsiblePartySection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,9">
          <id>popReInitResponsiblePartyToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popReInitResponsiblePartySection" label="" position="trigger_popup.xlsx,popReInitResponsibleParty,12" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,22">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:CPM Task Update, 2:Send Notification, 3:Invoke API,5:Re-initialize Responsible Party</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
        </element>
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,23">
          <id>partyNames</id>
          <label>Party Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName>popCodelistView</viewName>
          <comboKey/>
          <labelRenderer/>
          <viewParams>name=RESPONSIBLE_PARTIES_NAME</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popReInitResponsiblePartyToolbar" position="trigger_popup.xlsx,popReInitResponsibleParty,26">
      <elements id="default">
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,33">
          <id>popReInitResponsiblePartyButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popReInitResponsiblePartyButtons" label="" position="trigger_popup.xlsx,popReInitResponsibleParty,36">
      <elements id="default">
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,43">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>popReInitResponsiblePartyOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popReInitResponsibleParty,44">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
  <sheet id="popCpmRecalculate" position="trigger_popup.xlsx,popCpmRecalculate">
    <PopupCustomWin id="popCpmRecalculate" label="Define Action Details" position="trigger_popup.xlsx,popCpmRecalculate,1" size="s">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmRecalculate,8">
          <id>popCpmRecalculateSection</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popCpmRecalculate,9">
          <id>popCpmRecalculateSection2</id>
          <type>Section</type>
        </element>
        <element position="trigger_popup.xlsx,popCpmRecalculate,10">
          <id>popCpmRecalculateToolbar</id>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popCpmRecalculateSection" label="" position="trigger_popup.xlsx,popCpmRecalculate,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popCpmRecalculate,23">
          <id>actionType</id>
          <label>Action Type</label>
          <type>Label</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format>1:CPM Milestone Update, 2:Send Notification, 3:Invoke API, 6:Recalculate CPM Baseline</format>
          <viewName/>
          <comboKey/>
          <labelRenderer>com.core.cbx.trigger.form.PopSelActionOrEventTypeLabelRenderer</labelRenderer>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmRecalculate,24">
          <id>cpmMessage1</id>
          <label>Baseline will recalculated based on latest anchor date values,please choose appropriate options to apply recalculation on:</label>
          <type>Message</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass>cbx-sect-message</cssClass>
        </element>
      </elements>
    </Section>
    <Section entityName="TriggerAction" hideTitle="TRUE" id="popCpmRecalculateSection2" label="" position="trigger_popup.xlsx,popCpmRecalculate,27" ratio="8%,92%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="trigger_popup.xlsx,popCpmRecalculate,37">
          <id>milestoneOption</id>
          <label/>
          <type>Dropdown</type>
          <winTitle/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
        <element position="trigger_popup.xlsx,popCpmRecalculate,38">
          <id>customClass</id>
          <label>Custom Class</label>
          <type>Text</type>
          <winTitle/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <single/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <viewName/>
          <comboKey/>
          <labelRenderer/>
          <allowDateFilter/>
          <cssClass/>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popCpmRecalculateToolbar" position="trigger_popup.xlsx,popCpmRecalculate,41">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmRecalculate,48">
          <id>popCpmRecalculateButtons</id>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popCpmRecalculateButtons" label="" position="trigger_popup.xlsx,popCpmRecalculate,51">
      <elements id="default">
        <element position="trigger_popup.xlsx,popCpmRecalculate,58">
          <id>ok</id>
          <label>OK</label>
          <type>Button</type>
          <action>PopCpmRecalculateOkAction</action>
          <actionParams/>
        </element>
        <element position="trigger_popup.xlsx,popCpmRecalculate,59">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
</popup>
