<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<popup module="batchUpdate" position="batchUpdate_popup.xlsx">
  <sheet id="popBatchUpdateWin" position="batchUpdate_popup.xlsx,popBatchUpdateWin">
    <PopupCustomWin id="popBatchUpdateWin" label="Batch Update" position="batchUpdate_popup.xlsx,popBatchUpdateWin,1" size="S">
      <elements id="default">
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,8">
          <id>popBatchUpdateActionSection</id>
          <label/>
          <type>Section</type>
        </element>
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,9">
          <id>popBatchUpdateActionToolbar</id>
          <label/>
          <type>Toolbar</type>
        </element>
      </elements>
    </PopupCustomWin>
    <Section hideTitle="TRUE" id="popBatchUpdateActionSection" label="" position="batchUpdate_popup.xlsx,popBatchUpdateWin,12" ratio="100%">
      <elements id="fields">
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,19">
          <id>field</id>
          <label>Field</label>
          <type>Dropdown</type>
          <data/>
          <action>GetDDStoreByViewModule</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{fieldLabel}</format>
          <readonlyFormat/>
          <comboSorting>fieldLabel-ASC</comboSorting>
          <comboKey/>
          <hideTitle/>
          <readonly/>
          <size>L</size>
        </element>
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,20">
          <id>changeValue</id>
          <label>Change to</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <hideTitle/>
          <readonly/>
          <size>L</size>
        </element>
      </elements>
    </Section>
    <Toolbar align="" id="popBatchUpdateActionToolbar" label="" position="batchUpdate_popup.xlsx,popBatchUpdateWin,23">
      <elements id="default">
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,30">
          <id>popBatchUpdateActionButtons</id>
          <label/>
          <type>Buttonbar</type>
        </element>
      </elements>
    </Toolbar>
    <Buttonbar align="right" id="popBatchUpdateActionButtons" label="" position="batchUpdate_popup.xlsx,popBatchUpdateWin,33">
      <elements id="default">
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,40">
          <id>sendToVendor</id>
          <label>Send To Vendor</label>
          <type>Button</type>
          <action>PopBatchUpdateOkAction</action>
          <actionParams>sendToVendor=true</actionParams>
        </element>
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,41">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>Button</type>
          <action>PopBatchUpdateOkAction</action>
          <actionParams>confirmDoc=true</actionParams>
        </element>
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,42">
          <id>save</id>
          <label>Save</label>
          <type>Button</type>
          <action>PopBatchUpdateOkAction</action>
          <actionParams>confirmDoc=false</actionParams>
        </element>
        <element position="batchUpdate_popup.xlsx,popBatchUpdateWin,43">
          <id>close</id>
          <label>Cancel</label>
          <type>Button</type>
          <action>PopupCustomWinCloseAction</action>
          <actionParams/>
        </element>
      </elements>
    </Buttonbar>
  </sheet>
</popup>
