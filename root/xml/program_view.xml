<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="program" position="program_view.xlsx">
  <sheet id="programView" position="program_view.xlsx,programView">
    <ViewDefinition advancedSearchId="" description="Program View" id="programView" label="Programs - All" moduleId="program" position="program_view.xlsx,programView,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="program_view.xlsx,programView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programView,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="program_view.xlsx,programView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=name</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programView,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programView,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programView,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programView,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,30">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,31">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,32">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,33">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programView,34">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,35">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,36">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,37">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programView,38">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,39">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,40">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,41">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,42">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,43">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,44">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,45">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,46">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,47">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programView,48">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programView,52">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,53">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,54">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,55">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,56">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,57">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,58">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,59">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,60">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,61">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,62">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,63">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,64">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>1</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,65">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,66">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,67">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,68">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,69">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programView,70">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,71">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,72">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programView,73">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programActiveView" position="program_view.xlsx,programActiveView">
    <ViewDefinition advancedSearchId="" description="Program View" id="programActiveView" label="Programs - Active" moduleId="program" position="program_view.xlsx,programActiveView,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programActiveView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programActiveView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="program_view.xlsx,programActiveView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programActiveView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programActiveView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programActiveView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=name</value>
        </element>
        <element position="program_view.xlsx,programActiveView,14">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programActiveView,18">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programActiveView,19">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,20">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programActiveView,21">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,22">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,23">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programActiveView,24">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,25">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,26">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,27">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,28">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,29">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,30">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,31">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,32">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,33">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,34">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programActiveView,35">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,36">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,37">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programActiveView,38">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,39">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,40">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,41">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,42">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,43">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,44">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,45">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,46">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,47">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programActiveView,48">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programActiveView,52">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,53">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,54">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,55">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,56">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,57">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,58">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,59">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,60">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,61">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,62">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,63">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,64">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,65">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,66">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,67">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,68">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,69">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programActiveView,70">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,71">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,72">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programActiveView,73">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programImagesView" position="program_view.xlsx,programImagesView">
    <ViewDefinition advancedSearchId="" description="Program Images View" id="programImagesView" label="Programs - Images" moduleId="program" position="program_view.xlsx,programImagesView,1" queryId="listProgramImage" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programImagesView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programImagesView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="program_view.xlsx,programImagesView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programImagesView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programImagesView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programImagesView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=name</value>
        </element>
        <element position="program_view.xlsx,programImagesView,14">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity.programImages</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programImagesView,18">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programImagesView,19">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programImagesView,20">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programImagesView,21">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programImagesView,22">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programImagesView,23">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programImagesView,24">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programImagesView,25">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programImagesView,26">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programImagesView,30">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,32">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,33">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,34">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,35">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,36">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,37">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,38">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,39">
          <id>imageDescription</id>
          <label>Image Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cpi.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programImagesView,40">
          <id>remarks</id>
          <label>Remarks</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cpi.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,41">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,42">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,43">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,44">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,45">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,46">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,47">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,48">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,49">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programImagesView,50">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programImagesView,51">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programAttachmentsView" position="program_view.xlsx,programAttachmentsView">
    <ViewDefinition advancedSearchId="" description="Program Attachments View" id="programAttachmentsView" label="Programs - Attachments" moduleId="program" position="program_view.xlsx,programAttachmentsView,1" queryId="listProgramAttach" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programAttachmentsView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity.programAttachments</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programAttachmentsView,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,22">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,25">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programAttachmentsView,29">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,30">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,31">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,32">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,33">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,34">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,35">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,36">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,37">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,38">
          <id>attachmentDescription</id>
          <label>Attachment Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cpa.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,39">
          <id>remarks</id>
          <label>Remarks</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cpa.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,40">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,41">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,42">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,43">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,44">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,45">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,46">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,47">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,48">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,49">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,50">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,51">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programAttachmentsView,52">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popProgramView" position="program_view.xlsx,popProgramView">
    <ViewDefinition advancedSearchId="" description="Program Lookup" id="popProgramView" label="Program Lookup" moduleId="program" position="program_view.xlsx,popProgramView,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,popProgramView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="program_view.xlsx,popProgramView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="program_view.xlsx,popProgramView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,popProgramView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>single</value>
        </element>
        <element position="program_view.xlsx,popProgramView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (editingStatus =|STRING ''confirmed'')</value>
        </element>
        <element position="program_view.xlsx,popProgramView,13">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>3</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="program_view.xlsx,popProgramView,21">
          <id>pImage</id>
          <label>Image</label>
          <type>Thumbnail</type>
          <format>Image_tooltiptext=name</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>ca.FILE_PATH</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,22">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,23">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,popProgramView,24">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,25">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,26">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,27">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,28">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,29">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,30">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,31">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,32">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,33">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,34">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,35">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,36">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,37">
          <id>updatedOn</id>
          <label>Last Modified On</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,38">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,popProgramView,39">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus01View" position="program_view.xlsx,programStatus01View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus01View" label="Programs - Custom Status 01" moduleId="program" position="program_view.xlsx,programStatus01View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus01View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus01View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus01View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus01View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus01View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus01'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus01View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus01View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus01View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus01View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus01View,23">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,24">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus01View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus01View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus01View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus01View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus01View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus01View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus02View" position="program_view.xlsx,programStatus02View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus02View" label="Programs - Custom Status 02" moduleId="program" position="program_view.xlsx,programStatus02View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus02View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus02View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus02View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus02View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus02View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus02'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus02View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus02View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus02View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus02View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus02View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,24">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus02View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus02View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus02View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus02View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus02View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus02View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus03View" position="program_view.xlsx,programStatus03View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus03View" label="Programs - Custom Status 03" moduleId="program" position="program_view.xlsx,programStatus03View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus03View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus03View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus03View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus03View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus03View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus03'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus03View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus03View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus03View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus03View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus03View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus03View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus03View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus03View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus03View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus03View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus03View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus04View" position="program_view.xlsx,programStatus04View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus04View" label="Programs - Custom Status 04" moduleId="program" position="program_view.xlsx,programStatus04View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus04View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus04View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus04View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus04View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus04View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus04'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus04View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus04View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus04View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus04View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus04View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus04View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus04View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus04View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus04View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus04View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus04View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus05View" position="program_view.xlsx,programStatus05View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus05View" label="Programs - Custom Status 05" moduleId="program" position="program_view.xlsx,programStatus05View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus05View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus05View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus05View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus05View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus05View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus05View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus05View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus05View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus05View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus05View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus05View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus05View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus05View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus05View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus05View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus05View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus06View" position="program_view.xlsx,programStatus06View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus06View" label="Programs - Custom Status 06" moduleId="program" position="program_view.xlsx,programStatus06View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus06View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus06View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus06View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus06View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus06View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus06'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus06View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus06View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus06View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus06View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus06View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus06View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus06View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus06View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus06View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus06View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus06View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus07View" position="program_view.xlsx,programStatus07View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus07View" label="Programs - Custom Status 07" moduleId="program" position="program_view.xlsx,programStatus07View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus07View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus07View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus07View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus07View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus07View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus07'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus07View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus07View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus07View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus07View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus07View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus07View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus07View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus07View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus07View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus07View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus07View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus08View" position="program_view.xlsx,programStatus08View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus08View" label="Programs - Custom Status 08" moduleId="program" position="program_view.xlsx,programStatus08View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus08View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus08View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus08View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus08View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus08View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus08'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus08View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus08View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus08View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus08View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus08View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus08View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus08View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus08View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus08View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus08View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus08View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus09View" position="program_view.xlsx,programStatus09View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus09View" label="Programs - Custom Status 09" moduleId="program" position="program_view.xlsx,programStatus09View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus09View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus09View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus09View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus09View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus09View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus09'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus09View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus09View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus09View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus09View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus09View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,30">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus09View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus09View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus09View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus09View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus09View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus09View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="programStatus10View" position="program_view.xlsx,programStatus10View">
    <ViewDefinition advancedSearchId="" description="Program View" id="programStatus10View" label="Programs - Custom Status 10" moduleId="program" position="program_view.xlsx,programStatus10View,1" queryId="listProgram" searchCriterion="">
      <elements id="options">
        <element position="program_view.xlsx,programStatus10View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="program_view.xlsx,programStatus10View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus10View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="program_view.xlsx,programStatus10View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="program_view.xlsx,programStatus10View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus10'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="program_view.xlsx,programStatus10View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="program_view.xlsx,programStatus10View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus10View,18">
          <id>searchNewDoc</id>
          <label>New Program</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus10View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus10View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,30">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,31">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus10View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="program_view.xlsx,programStatus10View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=program&amp;entityName=Program</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="program_view.xlsx,programStatus10View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="program_view.xlsx,programStatus10View,50">
          <id>programNo</id>
          <label>Program No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,51">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>cp.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,54">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cp.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,55">
          <id>period</id>
          <label>Period</label>
          <type>CodeList</type>
          <format>bookName=PROGRAM_YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>cp.PERIOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,56">
          <id>startDate</id>
          <label>Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,57">
          <id>endDate</id>
          <label>End Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>cp.END_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,58">
          <id>Program</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility/>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,59">
          <id>Program</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>cp</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,60">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,61">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,62">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.program.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,63">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>32px</width>
          <visibility>0</visibility>
          <mappedField>cp.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,64">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,65">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,66">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,67">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>96px</width>
          <visibility>0</visibility>
          <mappedField>cp.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,68">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cp.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="program_view.xlsx,programStatus10View,69">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>cp.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,70">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cp.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="program_view.xlsx,programStatus10View,71">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>cp.PROGRAM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
