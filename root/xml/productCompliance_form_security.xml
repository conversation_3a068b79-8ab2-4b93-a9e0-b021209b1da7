<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="productCompliance" position="productCompliance_form_security.xlsx">
  <sheet id="_system" position="productCompliance_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="productCompliance_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="productCompliance_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/20</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="productCompliance_form_security.xlsx,generalInfo">
    <GeneralInfo position="productCompliance_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="productCompliance_form_security.xlsx,condition">
    <ConditionList position="productCompliance_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,6">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,7">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,8">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,9">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,10">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,11">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,12">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,13">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,14">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,15">
          <conditionId>isComplianceOneOfActivityStatusNotEmpty</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,16">
          <conditionId>isComplianceRequestSectionReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,17">
          <conditionId>isComplianceResponseSectionHidden</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,18">
          <conditionId>isComplianceResponseSectionReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,19">
          <conditionId>isComplianceResponseResultAndRemarksBuyerReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,20">
          <conditionId>isComplianceResponseHiddenBuyerSubmit</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,21">
          <conditionId>isComplianceReviewSectionReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,22">
          <conditionId>isComplianceReviewSectionHidden</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,23">
          <conditionId>isComplianceActivityReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,24">
          <conditionId>isComplianceAmendInVendorHidden</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,25">
          <conditionId>isComplianceAmendInBuyerHidden</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,26">
          <conditionId>isComplianceLatestActivityStatusReviewed</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,27">
          <conditionId>isComplianceExpiryDateReadonly</conditionId>
        </element>
        <element position="productCompliance_form_security.xlsx,condition,28">
          <conditionId>isComplianceActivityHiddenUnSubmit</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="productCompliance_form_security.xlsx,default">
    <ActionConditionMatrix position="productCompliance_form_security.xlsx,default,1">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,5">
          <actionId>amendDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,6">
          <actionId>loadDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,7">
          <actionId>productComplianceSaveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,8">
          <actionId>saveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,9">
          <actionId>reviewSubmit</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,10">
          <actionId>reviewUnSubmit</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,11">
          <actionId>requestSubmit</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,12">
          <actionId>responseSubmit</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,13">
          <actionId>discardDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,14">
          <actionId>copyDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,15">
          <actionId>activateDoc</actionId>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,16">
          <actionId>deactivateDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,17">
          <actionId>cancelDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,18">
          <actionId>productComplianceCustom01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,19">
          <actionId>productComplianceCustom02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,20">
          <actionId>productComplianceCustom03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,21">
          <actionId>productComplianceCustom04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,22">
          <actionId>productComplianceCustom05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,23">
          <actionId>productComplianceCustom06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,24">
          <actionId>productComplianceCustom07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,25">
          <actionId>productComplianceCustom08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,26">
          <actionId>productComplianceCustom09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="productCompliance_form_security.xlsx,default,27">
          <actionId>productComplianceCustom10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="productCompliance_form_security.xlsx,default,30">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,default,33">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isNotLatest>readonly</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>editable</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>editable</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>editable</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>editable</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>editable</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>editable</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>editable</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>editable</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>editable</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>editable</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>editable</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>editable</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>editable</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>editable</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,34">
          <componentId>ui.productComplianceMenubar.amendDoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>hidden</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>hidden</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>hidden</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,35">
          <componentId>ui.productComplianceMenubar.actionsGroup.deactivatedoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>hidden</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>editable</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>editable</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>hidden</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,36">
          <componentId>ui.productComplianceLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,37">
          <componentId>ui.productComplianceLinkbar.openForum</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,38">
          <componentId>ui.productComplianceLinkbar.addToFavorites</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,39">
          <componentId>ui.productComplianceLinkbar.followDoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,40">
          <componentId>ui.productComplianceLinkbar.unfollowDoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,41">
          <componentId>ui.productComplianceLinkbar.approval</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,42">
          <componentId>ui.tabHeader.complianceInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,43">
          <componentId>ui.tabHeader.vendorInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,44">
          <componentId>ui.tabHeader.factoryInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,45">
          <componentId>ui.tabHeader.otherInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,46">
          <componentId>ui.tabHeader.auditorInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,47">
          <componentId>ui.tabHeader.classificationSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>readonly</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,48">
          <componentId>ui.productComplianceActivity.tabHeader.requestInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>readonly</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>readonly</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,49">
          <componentId>ui.productComplianceActivity.tabHeader.requestInfoSection.requestSubmit</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>hidden</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>hidden</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,50">
          <componentId>ui.productComplianceActivity.tabHeader.responseInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>hidden</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>readonly</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>readonly</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,51">
          <componentId>ui.productComplianceActivity.tabHeader.responseInfoSection.responseResult</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>readonly</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,52">
          <componentId>ui.productComplianceActivity.tabHeader.responseInfoSection.responseRemarks</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>readonly</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,53">
          <componentId>ui.productComplianceActivity.tabHeader.responseInfoSection.expiryDate</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>inherit</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>readonly</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,54">
          <componentId>ui.productComplianceActivity.tabHeader.responseInfoSection.responseSubmit</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>hidden</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>hidden</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>hidden</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,55">
          <componentId>ui.productComplianceActivity.tabHeader.reviewInfoSection</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>readonly</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>hidden</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>readonly</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,56">
          <componentId>ui.productComplianceActivity.tabHeader.reviewInfoSection.reviewSubmit</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>inherit</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>hidden</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>hidden</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>inherit</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
        <element position="productCompliance_form_security.xlsx,default,57">
          <componentId>ui.productComplianceActivity.tabHeader.reviewInfoSection.reviewUnSubmit</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotLatest>inherit</isNotLatest>
          <isComplianceOneOfActivityStatusNotEmpty>inherit</isComplianceOneOfActivityStatusNotEmpty>
          <isComplianceRequestSectionReadonly>inherit</isComplianceRequestSectionReadonly>
          <isComplianceResponseSectionHidden>inherit</isComplianceResponseSectionHidden>
          <isComplianceResponseSectionReadonly>inherit</isComplianceResponseSectionReadonly>
          <isComplianceResponseResultAndRemarksBuyerReadonly>hidden</isComplianceResponseResultAndRemarksBuyerReadonly>
          <isComplianceReviewSectionReadonly>inherit</isComplianceReviewSectionReadonly>
          <isComplianceReviewSectionHidden>inherit</isComplianceReviewSectionHidden>
          <isComplianceActivityReadonly>editable</isComplianceActivityReadonly>
          <isComplianceResponseHiddenBuyerSubmit>inherit</isComplianceResponseHiddenBuyerSubmit>
          <isComplianceExpiryDateReadonly>inherit</isComplianceExpiryDateReadonly>
          <isComplianceActivityHiddenUnSubmit>hidden</isComplianceActivityHiddenUnSubmit>
          <isComplianceAmendInVendorHidden>inherit</isComplianceAmendInVendorHidden>
          <isComplianceAmendInBuyerHidden>inherit</isComplianceAmendInBuyerHidden>
          <isComplianceLatestActivityStatusReviewed>inherit</isComplianceLatestActivityStatusReviewed>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="productCompliance_form_security.xlsx,acl">
    <ActionRule position="productCompliance_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,5">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,6">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,7">
          <actionId>productComplianceSaveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,8">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,9">
          <actionId>reviewSubmit</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,10">
          <actionId>reviewUnSubmit</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,11">
          <actionId>requestSubmit</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,12">
          <actionId>responseSubmit</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,13">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,14">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,15">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,16">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,17">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>has</productCompliance.Author>
          <productCompliance.Editor>has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,18">
          <actionId>productComplianceCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,19">
          <actionId>productComplianceCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,20">
          <actionId>productComplianceCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,21">
          <actionId>productComplianceCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,22">
          <actionId>productComplianceCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,23">
          <actionId>productComplianceCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,24">
          <actionId>productComplianceCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,25">
          <actionId>productComplianceCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,26">
          <actionId>productComplianceCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,27">
          <actionId>productComplianceCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <productCompliance.Author>not-has</productCompliance.Author>
          <productCompliance.Editor>not-has</productCompliance.Editor>
          <productCompliance.ReadOnly>not-has</productCompliance.ReadOnly>
          <vpo.Author>not-has</vpo.Author>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="productCompliance_form_security.xlsx,acl,30">
      <elements id="default">
        <element position="productCompliance_form_security.xlsx,acl,33">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,34">
          <componentId>ui.tabVPOItemSKUShipment</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,35">
          <componentId>ui.tabHeader.complianceInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,36">
          <componentId>ui.tabHeader.otherInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,37">
          <componentId>ui.tabHeader.vendorInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,38">
          <componentId>ui.tabHeader.factoryInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,39">
          <componentId>ui.tabHeader.auditorInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,40">
          <componentId>ui.tabHeader.classificationSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,41">
          <componentId>ui.tabHeader.complianceItems</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,42">
          <componentId>ui.tabHeader.complianceActivities</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="productCompliance_form_security.xlsx,acl,43">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="productCompliance_form_security.xlsx,acl,46">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
