<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="group" position="group_entity.xlsx">
  <sheet id="_system" position="group_entity.xlsx,_system">
    <ProjectInfo client="Base" position="group_entity.xlsx,_system,1" project="SCMS" release_no="1.00"/>
    <ProductVersion position="group_entity.xlsx,_system,7">
      <elements id="default">
        <element position="group_entity.xlsx,_system,10">
          <updated_on>19-Oct-2011</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="group_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="N" main_entity="Group" module="group" position="group_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
  </sheet>
  <sheet id="entityDef" position="group_entity.xlsx,entityDef">
    <Entity name="Group" position="group_entity.xlsx,entityDef,1" ref_pattern="com.core.cbx.data.generator.DomainPatternSeqGenerator(&quot;CBX_SEQ_GROUP_REF_NO&quot;,&quot;system.pattern.GroupRefNo&quot;, &quot;GP#{Date:YY}-#{Seq:6}&quot;)" report_table_name="N/A" table_name="CNT_GROUP" unique_field_id="name">
      <elements id="header">
        <element position="group_entity.xlsx,entityDef,8">
          <entity_field_id>name</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name>NAME</report_column_name>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>2</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,9">
          <entity_field_id>domainId</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name>DOMAIN_ID</report_column_name>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,10">
          <entity_field_id>descn</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name>DESCN</report_column_name>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,11">
          <entity_field_id>hclHeaderNode</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name>HCL_HEADER_NODE</report_column_name>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,12">
          <entity_field_id>vendorPortal</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name>VENDOR_PORTAL</report_column_name>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="group_entity.xlsx,entityDef,16">
          <entity_field_id>groupHc</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>GroupHc.groupId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,17">
          <entity_field_id>memberOfId</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>2</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,18">
          <entity_field_id>groupMembers</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>GroupMember.memberId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,19">
          <entity_field_id>memberRoles</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>MemberRole.memberId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,20">
          <entity_field_id>memberHclNodes</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>MemberHclNode.memberId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,21">
          <entity_field_id>availableContacts</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1>RESPONSIBLE_PARTIES_NAME</data1>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,22">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="GroupHc" position="group_entity.xlsx,entityDef,25" ref_pattern="${hclFullLineage}" report_table_name="N/A" table_name="CNT_GROUP_HC" unique_field_id="hclNodeId">
      <elements id="header">
        <element position="group_entity.xlsx,entityDef,32">
          <entity_field_id>groupId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,33">
          <entity_field_id>hclTypeName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,34">
          <entity_field_id>hclLevelName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,35">
          <entity_field_id>hclNodeCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,36">
          <entity_field_id>hclFullLineage</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,37">
          <entity_field_id>hclId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,38">
          <entity_field_id>hclNodeId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,39">
          <entity_field_id>hclTypeId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,40">
          <entity_field_id>hclTypeLevelId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <report_column_name/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="GroupMember" position="group_entity.xlsx,entityDef,43" ref_pattern="" table_name="CNT_GROUP_MEMBER">
      <elements id="header">
        <element position="group_entity.xlsx,entityDef,50">
          <entity_field_id>groupId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,51">
          <entity_field_id>memberId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="group_entity.xlsx,entityDef,52">
          <entity_field_id>memberType</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
  </sheet>
</entity>
