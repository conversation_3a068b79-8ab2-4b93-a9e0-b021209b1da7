<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="packagingArtwork" position="packagingArtwork_dataMappingRule.xlsx">
  <sheet id="packagingArtworkCopyDoc" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc">
    <DataMappingRule description="Mapping from Packaging Artwork to Packaging Artwork" domain="/" dstEntityName="PackagingArtwork" dstEntityVersion="1" effectiveDate="2012-03-15" id="packagingArtworkCopyDoc" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,1" srcEntityName="PackagingArtwork" srcEntityVersion="1" status="1" updatedDate="2013-11-26">
      <elements id="mappingRule">
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>new</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>code</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="packagingArtworkDetailCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkDetailCopy">
    <DataMappingRule description="Mapping from Packaging Artwork Detail to Packaging Artwork Detail" domain="/" dstEntityName="PackagingArtwork" dstEntityVersion="1" effectiveDate="2012-03-15" id="packagingArtworkDetailCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkDetailCopy,1" srcEntityName="PackagingArtworkDetail" srcEntityVersion="1" status="1" updatedDate="2013-11-26">
      <elements id="mappingRule">
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkDetailCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkDetailCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packagingArtworkDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="packagingArtworkSelectAttrTempl" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl">
    <DataMappingRule description="Mapping from Attribute Template to PackagingArtwork" domain="/" dstEntityName="PackagingArtwork" dstEntityVersion="1" effectiveDate="2014-10-22" id="packagingArtworkSelectAttrTempl" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,1" srcEntityName="AttributeTemplate" srcEntityVersion="1" status="1" updatedDate="2014-10-22">
      <elements id="mappingRule">
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packagingArtworkAttributes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,10">
          <mappingType>Field</mappingType>
          <srcFieldId>attribute</srcFieldId>
          <dstFieldId>packagingArtworkAttributes.attribute</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,11">
          <mappingType>Field</mappingType>
          <srcFieldId>value</srcFieldId>
          <dstFieldId>packagingArtworkAttributes.value</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkSelectAttrTempl,12">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>packagingArtworkAttributes.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="packagingArtworkImagesCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkImagesCopy">
    <DataMappingRule description="Mapping for Copy Packaging Artwork Images" domain="/" dstEntityName="PackagingArtwork" dstEntityVersion="1" effectiveDate="2012-12-12" id="packagingArtworkImagesCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkImagesCopy,1" srcEntityName="PackagingArtworkImage" srcEntityVersion="1" status="1" updatedDate="2012-12-12">
      <elements id="mappingRule">
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkImagesCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkImagesCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packagingArtworkImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkImagesCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="packagingArtworkAttachmentsCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkAttachmentsCopy">
    <DataMappingRule description="Mapping for Copy Packaging Artwork Attachments" domain="/" dstEntityName="PackagingArtwork" dstEntityVersion="1" effectiveDate="2012-12-12" id="packagingArtworkAttachmentsCopy" position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkAttachmentsCopy,1" srcEntityName="PackagingArtworkAttachment" srcEntityVersion="1" status="1" updatedDate="2012-12-12">
      <elements id="mappingRule">
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkAttachmentsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="packagingArtwork_dataMappingRule.xlsx,packagingArtworkAttachmentsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>packagingArtworkAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
