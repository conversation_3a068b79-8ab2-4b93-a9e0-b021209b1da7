<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="sampleEvaluation" position="sampleEvaluation_validation.xlsx">
  <sheet id="ValidationProfile" position="sampleEvaluation_validation.xlsx,ValidationProfile">
    <ValidationProfile position="sampleEvaluation_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ValidationProfile,4">
          <id>sampleEvaluationValidatorId</id>
          <profileName>Default Data Validation Profile SampleEvaluation[ver:1]</profileName>
          <entityName>SampleEvaluation</entityName>
          <entityVer>1</entityVer>
          <action>SaveAndConfirm,SaveDoc,Mark<PERSON>Sub<PERSON>,MarkAsInProgress,Mark<PERSON><PERSON><PERSON><PERSON>,
MarkAsRejected,MarkAsWaive,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,
MarkAsCustomStatus03Doc,<PERSON><PERSON><PERSON>ustom<PERSON>tatus04Doc,Mark<PERSON><PERSON>ustomStatus05Doc,
<PERSON><PERSON><PERSON>ust<PERSON><PERSON>tatus06Doc,MarkAs<PERSON>ustomStatus07Doc,Mark<PERSON><PERSON>ustom<PERSON>tatus08Doc,
MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>14-Sep-2015</updatedOn>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationProfile,5">
          <id>sampleEvaluationValidatorId2</id>
          <profileName>Default Data Validation Profile SampleEvaluation[ver:1]</profileName>
          <entityName>SampleEvaluation</entityName>
          <entityVer>1</entityVer>
          <action>SampleEvaluationSendToVendor</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>14-Sep-2015</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="sampleEvaluation_validation.xlsx,ValidationRule">
    <ValidationRule position="sampleEvaluation_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,5">
          <type>ExternalActiveValidator</type>
          <className>com.core.cbx.validation.validator.ExternalActiveValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,6">
          <type>UniqueInSectionValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInSectionValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="sampleEvaluation_validation.xlsx,ValidationRule,7">
          <type>AttachLinkMandatoryValidator</type>
          <className>com.core.cbx.validation.validator.AttachLinkMandatoryValidator</className>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="sampleEvaluation_validation.xlsx,MandatoryValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,MandatoryValidator,1" profileId="sampleEvaluationValidatorId" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,8">
          <entityName>EvaluationDtl</entityName>
          <fieldId>evaluation</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>evaluation</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,9">
          <entityName>EvaluationDtl</entityName>
          <fieldId>section</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>section</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,10">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleEvaluation_validation.xlsx,MandatoryValidator,13" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,20">
          <entityName>EvaluationDtl</entityName>
          <fieldId>evaluation</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>evaluation</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,21">
          <entityName>EvaluationDtl</entityName>
          <fieldId>section</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>evaluationDtls</GRID_ID>
          <LABEL_FIELD_ID>section</LABEL_FIELD_ID>
        </element>
        <element position="sampleEvaluation_validation.xlsx,MandatoryValidator,22">
          <entityName>SampleEvaluationImage</entityName>
          <fieldId>image</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationImages</GRID_ID>
          <LABEL_FIELD_ID>image</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="ExternalActiveValidator" position="sampleEvaluation_validation.xlsx,ExternalActiveValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,ExternalActiveValidator,1" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,ExternalActiveValidator,8">
          <entityName>SampleEvaluation</entityName>
          <fieldId>vendor</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <LABEL_FIELD_ID>vendor</LABEL_FIELD_ID>
          <DOCUMENT_NO>vendorCode</DOCUMENT_NO>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInSectionValidator" position="sampleEvaluation_validation.xlsx,UniqueInSectionValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,UniqueInSectionValidator,1" profileId="sampleEvaluationValidatorId" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,UniqueInSectionValidator,8">
          <entityName>FitAdditionalInfo</entityName>
          <fieldId>seq</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <ERROR_ID>REF024</ERROR_ID>
          <FIELD_GROUP>seq,subItemEntity</FIELD_GROUP>
          <GRID_ID>fitAdditionalInfo</GRID_ID>
          <LABEL_FIELD_ID>$GRID_LABEL</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="AttachLinkMandatoryValidator" position="sampleEvaluation_validation.xlsx,AttachLinkMandatoryValidator">
    <ValidationField position="sampleEvaluation_validation.xlsx,AttachLinkMandatoryValidator,1" profileId="sampleEvaluationValidatorId" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,AttachLinkMandatoryValidator,8">
          <entityName>SampleEvaluationAttach</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationAttachs</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <validateBaseField>fileAddress</validateBaseField>
        </element>
      </elements>
    </ValidationField>
    <ValidationField position="sampleEvaluation_validation.xlsx,AttachLinkMandatoryValidator,11" profileId="sampleEvaluationValidatorId2" profileName="">
      <elements id="default">
        <element position="sampleEvaluation_validation.xlsx,AttachLinkMandatoryValidator,18">
          <entityName>SampleEvaluationAttach</entityName>
          <fieldId>attachment</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>sampleEvaluationAttachs</GRID_ID>
          <LABEL_FIELD_ID>attachment</LABEL_FIELD_ID>
          <validateBaseField>fileAddress</validateBaseField>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
