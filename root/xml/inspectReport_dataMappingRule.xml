<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="inspectReport" position="inspectReport_dataMappingRule.xlsx">
  <sheet id="irVpoItemToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem">
    <DataMappingRule description="Mapping for VpoItem to InspectReportItem" domain="/" dstEntityName="InspectReportItem" dstEntityVersion="1" effectiveDate="2012-03-15" id="irVpoItemToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,1" srcEntityName="VpoItem" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>vpoItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>vpoItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>vpoItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>vpoItemType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>vpoItemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColors</srcFieldId>
          <dstFieldId>itemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSizes</srcFieldId>
          <dstFieldId>itemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoItemToReportItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irVpoShipDtlToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem">
    <DataMappingRule description="Mapping for VpoShipDtl to  InspectReportItem" domain="/" dstEntityName="InspectReportItem" dstEntityVersion="1" effectiveDate="2012-03-15" id="irVpoShipDtlToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,1" srcEntityName="VpoShipDtl" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>actualQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>vpoShipDetailOrderQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,11">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipId</srcFieldId>
          <dstFieldId>vpoShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>vpoShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>vpoShipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoShipDtlToReportItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpoShipDetail</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irVpoToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoToReportItem">
    <DataMappingRule description="Mapping for Vpo to  InspectReportItem" domain="/" dstEntityName="InspectReportItem" dstEntityVersion="1" effectiveDate="2012-03-15" id="irVpoToReportItem" position="inspectReport_dataMappingRule.xlsx,irVpoToReportItem,1" srcEntityName="Vpo" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irVpoToReportItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoToReportItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoNo</srcFieldId>
          <dstFieldId>vpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVpoToReportItem,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irVendorToReport" position="inspectReport_dataMappingRule.xlsx,irVendorToReport">
    <DataMappingRule description="Mapping for Vendor to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irVendorToReport" position="inspectReport_dataMappingRule.xlsx,irVendorToReport,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>vendorContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>vendorContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>vendorContactTelNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irVendorToReport,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irFactoryToReport" position="inspectReport_dataMappingRule.xlsx,irFactoryToReport">
    <DataMappingRule description="Mapping for Fact to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irFactoryToReport" position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,1" srcEntityName="Fact" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>factoryName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>factCode</srcFieldId>
          <dstFieldId>factoryCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>factoryContactTelNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>factoryContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>factoryContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,14">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irFactoryToReport,15">
          <mappingType>Section</mappingType>
          <srcFieldId>factAddress</srcFieldId>
          <dstFieldId>factoryAddress</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDefault=true</condition>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irCustomerToReport" position="inspectReport_dataMappingRule.xlsx,irCustomerToReport">
    <DataMappingRule description="Mapping for Cust to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irCustomerToReport" position="inspectReport_dataMappingRule.xlsx,irCustomerToReport,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irCustomerToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irCustomerToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>customerName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irCustomerToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>custCode</srcFieldId>
          <dstFieldId>customerCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irCustomerToReport,11">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irReportTemplateToReport" position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport">
    <DataMappingRule description="Mapping for InspectReportTemplate to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irReportTemplateToReport" position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,1" srcEntityName="InspectReportTemplate" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectionLevel</srcFieldId>
          <dstFieldId>inspectionLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>criticalLevel</srcFieldId>
          <dstFieldId>criticalLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>majorLevel</srcFieldId>
          <dstFieldId>majorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>minorLevel</srcFieldId>
          <dstFieldId>minorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowRealtimeUpdateSR</srcFieldId>
          <dstFieldId>isAllowRealtimeUpdateSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowRealtimeAddDel</srcFieldId>
          <dstFieldId>isAllowRealtimeAddDel</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowManuallyOverwriteDR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteDR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisableDefectCode</srcFieldId>
          <dstFieldId>isDisableDefectCode</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>majorAsOneCriticalDefect</srcFieldId>
          <dstFieldId>majorAsOneCriticalDefect</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>minorAsOneMajorDefect</srcFieldId>
          <dstFieldId>minorAsOneMajorDefect</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isAggregateDefectCount</srcFieldId>
          <dstFieldId>isAggregateDefectCount</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,20">
          <mappingType>Field</mappingType>
          <srcFieldId>isDiscardCalMinorInDR</srcFieldId>
          <dstFieldId>isDiscardCalMinorInDR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,21">
          <mappingType>Field</mappingType>
          <srcFieldId>majorAsOneCritical</srcFieldId>
          <dstFieldId>majorAsOneCritical</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,22">
          <mappingType>Field</mappingType>
          <srcFieldId>minorAsOneMajor</srcFieldId>
          <dstFieldId>minorAsOneMajor</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,23">
          <mappingType>Field</mappingType>
          <srcFieldId>isDiscardCalMinorInMR</srcFieldId>
          <dstFieldId>isDiscardCalMinorInMR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,24">
          <mappingType>Field</mappingType>
          <srcFieldId>measInspection</srcFieldId>
          <dstFieldId>measInspection</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,25">
          <mappingType>Field</mappingType>
          <srcFieldId>measCritical</srcFieldId>
          <dstFieldId>measCritical</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,26">
          <mappingType>Field</mappingType>
          <srcFieldId>measMajor</srcFieldId>
          <dstFieldId>measMajor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,27">
          <mappingType>Field</mappingType>
          <srcFieldId>measMinor</srcFieldId>
          <dstFieldId>measMinor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,28">
          <mappingType>Field</mappingType>
          <srcFieldId>allowUpdateMeasSR</srcFieldId>
          <dstFieldId>allowUpdateMeasSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,29">
          <mappingType>Field</mappingType>
          <srcFieldId>isCountMeasIntoDefect</srcFieldId>
          <dstFieldId>isCountMeasIntoDefect</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,30">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowManuallyOverwriteMR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteMR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,31">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowManuallyOverwriteOR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteOR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,32">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplate</srcFieldId>
          <dstFieldId>factoryAuditTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,33">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectReportTemplateItems</srcFieldId>
          <dstFieldId>inspectReportDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isNotAvailable='0'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,34">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,35">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>testItem</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,36">
          <mappingType>Field</mappingType>
          <srcFieldId>isMeasDefectItems</srcFieldId>
          <dstFieldId>isMeasDefectItems</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,37">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.section.seq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,38">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.section.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,39">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isCriticalShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isCriticalShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,40">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isMajorShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isMajorShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,41">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isMinorShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isMinorShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,42">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,43">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>critical</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,44">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>major</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,45">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>minor</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,46">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>noOfAttachments</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,47">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>reportTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity!=null</condition>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,irReportTemplateToReport,51">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IrReportTemplateToReportProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irBookingToReport" position="inspectReport_dataMappingRule.xlsx,irBookingToReport">
    <DataMappingRule description="Mapping for InspectBooking to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irBookingToReport" position="inspectReport_dataMappingRule.xlsx,irBookingToReport,1" srcEntityName="InspectBooking" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDescription</srcFieldId>
          <dstFieldId>shortDescription</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingNo</srcFieldId>
          <dstFieldId>inspectBookingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedInspectDate</srcFieldId>
          <dstFieldId>plannedInspectDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>estimatedEffort</srcFieldId>
          <dstFieldId>estimatedEffort</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>criticalAQLCount</srcFieldId>
          <dstFieldId>criticalAQLCount</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>majorAQLCount</srcFieldId>
          <dstFieldId>majorAQLCount</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>customerName</srcFieldId>
          <dstFieldId>customerName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>customerCode</srcFieldId>
          <dstFieldId>customerCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>minorAQLCount</srcFieldId>
          <dstFieldId>minorAQLCount</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryName</srcFieldId>
          <dstFieldId>factoryName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,19">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryCode</srcFieldId>
          <dstFieldId>factoryCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,20">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryContactName</srcFieldId>
          <dstFieldId>factoryContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,21">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryContactEmail</srcFieldId>
          <dstFieldId>factoryContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,22">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,23">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,24">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorContactName</srcFieldId>
          <dstFieldId>vendorContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,25">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorContactEmail</srcFieldId>
          <dstFieldId>vendorContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,26">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorContactTelNo</srcFieldId>
          <dstFieldId>vendorContactTelNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,27">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryContactTelNo</srcFieldId>
          <dstFieldId>factoryContactTelNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,28">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectBooking</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,29">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,30">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectionParty</srcFieldId>
          <dstFieldId>inspectionParty</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,31">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectionOffice</srcFieldId>
          <dstFieldId>inspectionOffice</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,32">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectType</srcFieldId>
          <dstFieldId>inspectType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,33">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectFormat</srcFieldId>
          <dstFieldId>inspectFormat</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,34">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate</srcFieldId>
          <dstFieldId>reportTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,35">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,36">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,37">
          <mappingType>Section</mappingType>
          <srcFieldId>customer</srcFieldId>
          <dstFieldId>customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,38">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectors</srcFieldId>
          <dstFieldId>inspectors</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,39">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems</srcFieldId>
          <dstFieldId>inspectReportItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,40">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,41">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipmentNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,42">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipmentDate</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipmentDate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,43">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,44">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemName</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,45">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemDesc</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,46">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipDetailOrderQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,47">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>inspectReportItems.actualQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,48">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpo</srcFieldId>
          <dstFieldId>inspectReportItems.vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,49">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemImage</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,50">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipment</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,51">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoItem</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,52">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemSpec</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemSpec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,53">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipDetail</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipDetail</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,54">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingItems.vpoShipDetail.qty</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipDetailOrderQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,55">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.hierarchy</srcFieldId>
          <dstFieldId>inspectReportItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,56">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.productCategory</srcFieldId>
          <dstFieldId>inspectReportItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,57">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.itemId</srcFieldId>
          <dstFieldId>inspectReportItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,58">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.vpoItemType</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,59">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.itemColors</srcFieldId>
          <dstFieldId>inspectReportItems.itemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,60">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingItems.itemSizes</srcFieldId>
          <dstFieldId>inspectReportItems.itemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,61">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate</srcFieldId>
          <dstFieldId>reportTemplate</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>reportTemplate!=null</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,62">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.inspectionLevel</srcFieldId>
          <dstFieldId>inspectionLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,63">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.criticalLevel</srcFieldId>
          <dstFieldId>criticalLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,64">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.majorLevel</srcFieldId>
          <dstFieldId>majorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,65">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.minorLevel</srcFieldId>
          <dstFieldId>minorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,66">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAllowRealtimeUpdateSR</srcFieldId>
          <dstFieldId>isAllowRealtimeUpdateSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,67">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAllowRealtimeAddDel</srcFieldId>
          <dstFieldId>isAllowRealtimeAddDel</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,68">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAllowManuallyOverwriteDR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteDR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,69">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isDisableDefectCode</srcFieldId>
          <dstFieldId>isDisableDefectCode</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,70">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.majorAsOneCriticalDefect</srcFieldId>
          <dstFieldId>majorAsOneCriticalDefect</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,71">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.minorAsOneMajorDefect</srcFieldId>
          <dstFieldId>minorAsOneMajorDefect</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,72">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAggregateDefectCount</srcFieldId>
          <dstFieldId>isAggregateDefectCount</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,73">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isDiscardCalMinorInDR</srcFieldId>
          <dstFieldId>isDiscardCalMinorInDR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,74">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.majorAsOneCritical</srcFieldId>
          <dstFieldId>majorAsOneCritical</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,75">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.minorAsOneMajor</srcFieldId>
          <dstFieldId>minorAsOneMajor</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,76">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isDiscardCalMinorInMR</srcFieldId>
          <dstFieldId>isDiscardCalMinorInMR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,77">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.measInspection</srcFieldId>
          <dstFieldId>measInspection</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,78">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.measCritical</srcFieldId>
          <dstFieldId>measCritical</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,79">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.measMajor</srcFieldId>
          <dstFieldId>measMajor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,80">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.measMinor</srcFieldId>
          <dstFieldId>measMinor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,81">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.allowUpdateMeasSR</srcFieldId>
          <dstFieldId>allowUpdateMeasSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,82">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isCountMeasIntoDefect</srcFieldId>
          <dstFieldId>isCountMeasIntoDefect</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,83">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAllowManuallyOverwriteMR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteMR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,84">
          <mappingType>Field</mappingType>
          <srcFieldId>reportTemplate.isAllowManuallyOverwriteOR</srcFieldId>
          <dstFieldId>isAllowManuallyOverwriteOR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,85">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate.factoryAuditTemplate</srcFieldId>
          <dstFieldId>factoryAuditTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,86">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate.inspectReportTemplateItems</srcFieldId>
          <dstFieldId>inspectReportDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isNotAvailable='0'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,87">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,88">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>testItem</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,89">
          <mappingType>Field</mappingType>
          <srcFieldId>isMeasDefectItems</srcFieldId>
          <dstFieldId>isMeasDefectItems</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,90">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.section.seq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,91">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.section.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,92">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isCriticalShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isCriticalShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,93">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isMajorShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isMajorShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,94">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isMinorShown</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.section.isMinorShown</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,95">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,96">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>critical</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,97">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>major</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,98">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>minor</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,99">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>noOfAttachments</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>0</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,100">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate.factoryAuditTemplate</srcFieldId>
          <dstFieldId>factoryAuditTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>reportTemplate!=null</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,101">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate.factoryAuditTemplate.factoryAuditTemplateRequirements</srcFieldId>
          <dstFieldId>inspectReportRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!='1'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,102">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,103">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,104">
          <mappingType>Field</mappingType>
          <srcFieldId>isMandatory</srcFieldId>
          <dstFieldId>isMandatory</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,105">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired1</srcFieldId>
          <dstFieldId>isResult1Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,106">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired2</srcFieldId>
          <dstFieldId>isResult2Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,107">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired3</srcFieldId>
          <dstFieldId>isResult3Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,108">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired4</srcFieldId>
          <dstFieldId>isResult4Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,109">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired5</srcFieldId>
          <dstFieldId>isResult5Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,110">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired6</srcFieldId>
          <dstFieldId>isResult6Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,111">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired7</srcFieldId>
          <dstFieldId>isResult7Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,112">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired8</srcFieldId>
          <dstFieldId>isResult8Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,113">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired9</srcFieldId>
          <dstFieldId>isResult9Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,114">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired10</srcFieldId>
          <dstFieldId>isResult10Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,115">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeq</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,116">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,117">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>defaultValueSeqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.defaultValue.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,118">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result1</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,119">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result2</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,120">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result3</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,121">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result4</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,122">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result5</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,123">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result6</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,124">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result7</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,125">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result8</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,126">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result9</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,127">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result10</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,128">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,129">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateColumns</srcFieldId>
          <dstFieldId>inspectReportChecklistSummary</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!='1'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,130">
          <mappingType>Field</mappingType>
          <srcFieldId>label</srcFieldId>
          <dstFieldId>result</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,131">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,132">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>labelSeqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,133">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectBookingSampleRules</srcFieldId>
          <dstFieldId>inspectReportSampleRules</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,134">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.ruleName</srcFieldId>
          <dstFieldId>inspectReportSampleRules.ruleName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,135">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.inspectionLevel</srcFieldId>
          <dstFieldId>inspectReportSampleRules.inspectionLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,136">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.criticalLevel</srcFieldId>
          <dstFieldId>inspectReportSampleRules.criticalLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,137">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.majorLevel</srcFieldId>
          <dstFieldId>inspectReportSampleRules.majorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,138">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.minorLevel</srcFieldId>
          <dstFieldId>inspectReportSampleRules.minorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,139">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.isAllowRealtimeUpdateSR</srcFieldId>
          <dstFieldId>inspectReportSampleRules.isAllowRealtimeUpdateSR</dstFieldId>
          <dstFieldType>SSS</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,140">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingSampleRules.internalSeqNo</srcFieldId>
          <dstFieldId>inspectReportSampleRules.internalSeqNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingToReport,144">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IrBookingHeaderToReportProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irBookingHeaderToReport" position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport">
    <DataMappingRule description="Mapping for InspectBooking Header to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irBookingHeaderToReport" position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,1" srcEntityName="InspectBooking" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>customerName</srcFieldId>
          <dstFieldId>customerName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerCode</srcFieldId>
          <dstFieldId>customerCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryName</srcFieldId>
          <dstFieldId>factoryName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryCode</srcFieldId>
          <dstFieldId>factoryCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryContactName</srcFieldId>
          <dstFieldId>factoryContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>factoryContactEmail</srcFieldId>
          <dstFieldId>factoryContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorContactName</srcFieldId>
          <dstFieldId>vendorContactName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorContactEmail</srcFieldId>
          <dstFieldId>vendorContactEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,19">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,20">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,21">
          <mappingType>Section</mappingType>
          <srcFieldId>customer</srcFieldId>
          <dstFieldId>customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,22">
          <mappingType>Section</mappingType>
          <srcFieldId>inspectors</srcFieldId>
          <dstFieldId>inspectors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingHeaderToReport,26">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IrBookingHeaderToReportProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irBookingItemToReport" position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport">
    <DataMappingRule description="Mapping for InspectBookingItem to InspectReportItem" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2016-07-05" id="irBookingItemToReport" position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,1" srcEntityName="InspectBookingItem" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,9">
          <mappingType>Section</mappingType>
          <srcFieldId>reportTemplate</srcFieldId>
          <dstFieldId>reportTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity!=null</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>inspectReportItems.actualQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipDetailOrderQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipmentNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipmentDate</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItemNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItemDesc</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,18">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDetail</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipDetail</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,19">
          <mappingType>Section</mappingType>
          <srcFieldId>vpo</srcFieldId>
          <dstFieldId>inspectReportItems.vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,20">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipment</srcFieldId>
          <dstFieldId>inspectReportItems.vpoShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,21">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,22">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>inspectReportItems.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,23">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>inspectReportItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,24">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>inspectReportItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReport,28">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IrBookingItemToReportProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irBookingItemToReportItem" position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem">
    <DataMappingRule description="Mapping for InspectBookingItem to InspectReportItem" domain="/" dstEntityName="InspectReportItem" dstEntityVersion="1" effectiveDate="2012-03-15" id="irBookingItemToReportItem" position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,1" srcEntityName="InspectBookingItem" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,9">
          <mappingType>Field</mappingType>
          <srcFieldId>inspectBookingId</srcFieldId>
          <dstFieldId>iriInspectBooking</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>actualQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipDetailOrderQty</srcFieldId>
          <dstFieldId>vpoShipDetailOrderQty</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoNo</srcFieldId>
          <dstFieldId>vpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipmentNo</srcFieldId>
          <dstFieldId>vpoShipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoShipmentDate</srcFieldId>
          <dstFieldId>vpoShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItemNo</srcFieldId>
          <dstFieldId>vpoItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItemName</srcFieldId>
          <dstFieldId>vpoItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItemDesc</srcFieldId>
          <dstFieldId>vpoItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDetail</srcFieldId>
          <dstFieldId>vpoShipDetail</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>vpo</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>custPoNo</srcFieldId>
          <dstFieldId>custPoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.businessName</srcFieldId>
          <dstFieldId>custName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipment</srcFieldId>
          <dstFieldId>vpoShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,24">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItem.innerGtin</srcFieldId>
          <dstFieldId>innerGtin</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoItem.outerGtin</srcFieldId>
          <dstFieldId>outerGtin</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItemType</srcFieldId>
          <dstFieldId>vpoItemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColors</srcFieldId>
          <dstFieldId>itemColors</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSizes</srcFieldId>
          <dstFieldId>itemSizes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingItemToReportItem,36">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IrBookingItemToReportItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irSpecSetToReport" position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport">
    <DataMappingRule description="Mapping for SpecSet to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irSpecSetToReport" position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,1" srcEntityName="SpecSet" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,9">
          <mappingType>Section</mappingType>
          <srcFieldId>specSize</srcFieldId>
          <dstFieldId>inspectReportMeasSizeResults</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>inspectReportMeasSizeResults.specSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,11">
          <mappingType>Section</mappingType>
          <srcFieldId>specMeasurements</srcFieldId>
          <dstFieldId>inspectReportMeasDetails</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irSpecSetToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>inspectReportMeasDetails.specMeasurement</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>entity</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="inspectReportItemCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportItemCopy">
    <DataMappingRule description="Mapping for InspectReport Item Copy" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="inspectReportItemCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportItemCopy,1" srcEntityName="InspectReportItem" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportItemCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportItemCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irInspectReportImageCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportImageCopy">
    <DataMappingRule description="Mapping for InspectReport Image Copy" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irInspectReportImageCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportImageCopy,1" srcEntityName="InspectReportImage" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportImageCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportImageCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irInspectReportAttachCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportAttachCopy">
    <DataMappingRule description="Mapping for InspectReport Attachment Copy" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="irInspectReportAttachCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportAttachCopy,1" srcEntityName="InspectReportAttachment" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportAttachCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportAttachCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="inspectReportCopyDoc" position="inspectReport_dataMappingRule.xlsx,inspectReportCopyDoc">
    <DataMappingRule description="Mapping for  InspecReport Copy Doc" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-03-15" id="inspectReportCopyDoc" position="inspectReport_dataMappingRule.xlsx,inspectReportCopyDoc,1" srcEntityName="InspectReport" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>inspectReportNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irMeasResultsCopy" position="inspectReport_dataMappingRule.xlsx,irMeasResultsCopy">
    <DataMappingRule description="Mapping for Copy Meas Results" domain="/" dstEntityName="InspectReportMeasResult" dstEntityVersion="1" effectiveDate="2012-02-20" id="irMeasResultsCopy" position="inspectReport_dataMappingRule.xlsx,irMeasResultsCopy,1" srcEntityName="InspectReportMeasResult" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irMeasResultsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irMeasResultsCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>com.core.cbx.defaulting.generator.InspectReportMeasResultSeqNoGenerator</mappedValue>
          <mappedValueType>Default</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irMeasDetailResultsCopy" position="inspectReport_dataMappingRule.xlsx,irMeasDetailResultsCopy">
    <DataMappingRule description="Mapping for Copy Meas Detail Results" domain="/" dstEntityName="InspectReportMeasDetailResult" dstEntityVersion="1" effectiveDate="2012-02-20" id="irMeasDetailResultsCopy" position="inspectReport_dataMappingRule.xlsx,irMeasDetailResultsCopy,1" srcEntityName="InspectReportMeasDetailResult" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irMeasDetailResultsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irInspectReportDetailImageCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailImageCopy">
    <DataMappingRule description="Mapping for Copy Inspect Report Detail Image" domain="/" dstEntityName="InspectReportDetail" dstEntityVersion="1" effectiveDate="2016-06-20" id="irInspectReportDetailImageCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailImageCopy,1" srcEntityName="InspectReportDetailImage" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailImageCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailImageCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportDetailImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irInspectReportDetailAttachment" position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailAttachment">
    <DataMappingRule description="Mapping for Copy Inspect Report Detail Attechment" domain="/" dstEntityName="InspectReportDetail" dstEntityVersion="1" effectiveDate="2016-06-20" id="irInspectReportDetailAttachmentCopy" position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailAttachment,1" srcEntityName="InspectReportDetailAttachment" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irInspectReportDetailAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportDetailAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="checklistTemplateToReport" position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport">
    <DataMappingRule description="" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2016-06-20" id="checklistTemplateToReport" position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,1" srcEntityName="FactoryAuditTemplate" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,9">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateRequirements</srcFieldId>
          <dstFieldId>inspectReportRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!='1'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeq</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>defaultValueSeqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.defaultValue.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>isMandatory</srcFieldId>
          <dstFieldId>isMandatory</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired1</srcFieldId>
          <dstFieldId>isResult1Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired2</srcFieldId>
          <dstFieldId>isResult2Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired3</srcFieldId>
          <dstFieldId>isResult3Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired4</srcFieldId>
          <dstFieldId>isResult4Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,20">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired5</srcFieldId>
          <dstFieldId>isResult5Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,21">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired6</srcFieldId>
          <dstFieldId>isResult6Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,22">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired7</srcFieldId>
          <dstFieldId>isResult7Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,23">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired8</srcFieldId>
          <dstFieldId>isResult8Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,24">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired9</srcFieldId>
          <dstFieldId>isResult9Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,25">
          <mappingType>Field</mappingType>
          <srcFieldId>isRequired10</srcFieldId>
          <dstFieldId>isResult10Available</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,26">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result1</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,27">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result2</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result3</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,29">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result4</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,30">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result5</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,31">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result6</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,32">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result7</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,33">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result8</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,34">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result9</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,35">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>result10</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,36">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,37">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionNotes</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.notesInstructions</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,38">
          <mappingType>Field</mappingType>
          <srcFieldId>notesInstructions</srcFieldId>
          <dstFieldId>notesInstructions</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,39">
          <mappingType>Field</mappingType>
          <srcFieldId>fieldType</srcFieldId>
          <dstFieldId>fieldType</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,40">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueTextarea</srcFieldId>
          <dstFieldId>observationsComments</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,41">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDropdown</srcFieldId>
          <dstFieldId>oCsDropdown</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,42">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueBookName</srcFieldId>
          <dstFieldId>oCsBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,43">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueSlnBookName</srcFieldId>
          <dstFieldId>oCsSlnBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,44">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDate</srcFieldId>
          <dstFieldId>oCsDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,45">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDecimal</srcFieldId>
          <dstFieldId>oCsDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,46">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueImage</srcFieldId>
          <dstFieldId>oCsImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,47">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueNumber</srcFieldId>
          <dstFieldId>oCsNumber</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,48">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueText</srcFieldId>
          <dstFieldId>oCsText</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,49">
          <mappingType>Field</mappingType>
          <srcFieldId>maximumScore</srcFieldId>
          <dstFieldId>maximumScore</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,50">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SectionItem</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,51">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateRequirements.importance</srcFieldId>
          <dstFieldId>inspectReportRequirements.importance</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,52">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateRequirements.reviewBy</srcFieldId>
          <dstFieldId>inspectReportRequirements.reviewBy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,53">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateRequirements.defaultValueSelection</srcFieldId>
          <dstFieldId>inspectReportRequirements.oCsSelection</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,54">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateColumns</srcFieldId>
          <dstFieldId>inspectReportChecklistSummary</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!=true</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,55">
          <mappingType>Field</mappingType>
          <srcFieldId>label</srcFieldId>
          <dstFieldId>result</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,56">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,57">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>labelSeqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,58">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateOptions</srcFieldId>
          <dstFieldId>inspectReportRequirements.inspectReportObservationsComments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,59">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,60">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,61">
          <mappingType>Field</mappingType>
          <srcFieldId>enableTextbox</srcFieldId>
          <dstFieldId>enableTextbox</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,62">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,63">
          <mappingType>Field</mappingType>
          <srcFieldId>isChecked</srcFieldId>
          <dstFieldId>isChecked</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,64">
          <mappingType>Field</mappingType>
          <srcFieldId>textboxText</srcFieldId>
          <dstFieldId>textboxText</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToReport,68">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ChecklistTemplateToInspectReportSetupFieldVaulePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irBookingAQLToReport" position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport">
    <DataMappingRule description="Mapping for InspectBooking AQL to InspectReport" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2017-05-10" id="irBookingAQLToReport" position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,1" srcEntityName="InspectBooking" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,9">
          <mappingType>Field</mappingType>
          <srcFieldId>templateInspectionLevel</srcFieldId>
          <dstFieldId>inspectionLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId>templateCriticalLevel</srcFieldId>
          <dstFieldId>criticalLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId>templateMajorLevel</srcFieldId>
          <dstFieldId>majorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId>templateMinorLevel</srcFieldId>
          <dstFieldId>minorLevel</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId>isAllowRealtimeUpdateSR</srcFieldId>
          <dstFieldId>isAllowRealtimeUpdateSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId>measInspection</srcFieldId>
          <dstFieldId>measInspection</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>measCritical</srcFieldId>
          <dstFieldId>measCritical</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>measMajor</srcFieldId>
          <dstFieldId>measMajor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>measMinor</srcFieldId>
          <dstFieldId>measMinor</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>allowUpdateMeasSR</srcFieldId>
          <dstFieldId>allowUpdateMeasSR</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irBookingAQLToReport,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isCountMeasIntoDefect</srcFieldId>
          <dstFieldId>isCountMeasIntoDefect</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irRequirementImageCopy" position="inspectReport_dataMappingRule.xlsx,irRequirementImageCopy">
    <DataMappingRule description="Mapping for Copy Inspect Report Requirement Image" domain="/" dstEntityName="InspectReportRequirement" dstEntityVersion="1" effectiveDate="2016-06-20" id="irRequirementImageCopy" position="inspectReport_dataMappingRule.xlsx,irRequirementImageCopy,1" srcEntityName="InspectReportRequirementImage" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irRequirementImageCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irRequirementImageCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportRequirementImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irRequirementAttachmentCopy" position="inspectReport_dataMappingRule.xlsx,irRequirementAttachmentCopy">
    <DataMappingRule description="Mapping for Copy Inspect Report Detail Attechment" domain="/" dstEntityName="InspectReportRequirement" dstEntityVersion="1" effectiveDate="2016-06-20" id="irRequirementAttachmentCopy" position="inspectReport_dataMappingRule.xlsx,irRequirementAttachmentCopy,1" srcEntityName="InspectReportRequirementAttachment" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irRequirementAttachmentCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irRequirementAttachmentCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportRequirementAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="checklistTemplateToIRChecklis" position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis">
    <DataMappingRule description="" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-01-11" id="checklistTemplateToIRChecklis" position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,1" srcEntityName="FactoryAuditTemplate" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,9">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateChecklists</srcFieldId>
          <dstFieldId>inspectReportChecklists</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!='1'</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.factoryAuditTemplateSection.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,12">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,13">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,14">
          <mappingType>Field</mappingType>
          <srcFieldId>fieldType</srcFieldId>
          <dstFieldId>fieldType</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,15">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueTextarea</srcFieldId>
          <dstFieldId>observationsComments</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,16">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDropdown</srcFieldId>
          <dstFieldId>oCsDropdown</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,17">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDate</srcFieldId>
          <dstFieldId>oCsDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,18">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDecimal</srcFieldId>
          <dstFieldId>oCsDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,19">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueImage</srcFieldId>
          <dstFieldId>oCsImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,20">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueNumber</srcFieldId>
          <dstFieldId>oCsNumber</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,21">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueText</srcFieldId>
          <dstFieldId>oCsText</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,22">
          <mappingType>Field</mappingType>
          <srcFieldId>isMandatory</srcFieldId>
          <dstFieldId>isMandatory</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,23">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateOptions</srcFieldId>
          <dstFieldId>inspectReportChecklists.inspectReportObservationsComments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,24">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,25">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,26">
          <mappingType>Field</mappingType>
          <srcFieldId>enableTextbox</srcFieldId>
          <dstFieldId>enableTextbox</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,27">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,28">
          <mappingType>Field</mappingType>
          <srcFieldId>isChecked</srcFieldId>
          <dstFieldId>isChecked</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,29">
          <mappingType>Field</mappingType>
          <srcFieldId>textboxText</srcFieldId>
          <dstFieldId>textboxText</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,checklistTemplateToIRChecklis,33">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.IRCheckListSelectTemplatePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="irMoreDetailImgCLCopy" position="inspectReport_dataMappingRule.xlsx,irMoreDetailImgCLCopy">
    <DataMappingRule description="Mapping for Copy Inspect Report More Detail Image" domain="/" dstEntityName="InspectReportChecklist" dstEntityVersion="1" effectiveDate="2018-02-09" id="irMoreDetailImgCLCopy" position="inspectReport_dataMappingRule.xlsx,irMoreDetailImgCLCopy,1" srcEntityName="InspectReportMoreDetailImageCL" srcEntityVersion="1" status="1" updatedDate="2018-02-09">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irMoreDetailImgCLCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irMoreDetailImgCLCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportMoreDetailImageCL</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="irMoreDetailAttCLCopy" position="inspectReport_dataMappingRule.xlsx,irMoreDetailAttCLCopy">
    <DataMappingRule description="Mapping for Copy Inspect Report More Detail Attachment" domain="/" dstEntityName="InspectReportChecklist" dstEntityVersion="1" effectiveDate="2018-02-09" id="irMoreDetailAttCLCopy" position="inspectReport_dataMappingRule.xlsx,irMoreDetailAttCLCopy,1" srcEntityName="InspectReportMoreDetailAttachmentCL" srcEntityVersion="1" status="1" updatedDate="2018-02-09">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,irMoreDetailAttCLCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,irMoreDetailAttCLCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportMoreDetailAttachmentCL</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="inspectReportRequirementsCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy">
    <DataMappingRule description="" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2021-08-05" id="inspectReportRequirementsCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy,1" srcEntityName="InspectReportRequirement" srcEntityVersion="1" status="1" updatedDate="2021-08-05">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportRequirementsCopy,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.InspectReportRequirementCopyPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="inspectReportChecklistCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy">
    <DataMappingRule description="" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2021-08-05" id="inspectReportChecklistCopy" position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy,1" srcEntityName="InspectReportChecklist" srcEntityVersion="1" status="1" updatedDate="2021-08-05">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportChecklists</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportChecklistCopy,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.InspectReportChecklistCopyPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="inspecReportItemSelectItem" position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem">
    <DataMappingRule description="Mapping from Item to InspectReportItem" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2012-02-20" id="inspecReportItemSelectItem" position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="31-十二月-2099">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportItems</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>inspectReportItems.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>inspectReportItems.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspecReportItemSelectItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>inspectReportItems.vpoItemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Reference</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="checklistSectionToReport" position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport">
    <DataMappingRule description="" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2016-06-20" id="checklistSectionToReport" position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,1" srcEntityName="FactoryAuditTemplate" srcEntityVersion="1" status="1" updatedDate="2016-06-20">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,9">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateSections</srcFieldId>
          <dstFieldId>inspectReportRequirements</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isDisabled!=true</condition>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionSeq</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionName</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>defaultValueSeqNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.defaultValue.seqNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sectionNotes</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.notesInstructions</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>notesInstructions</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.notesInstructions</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,15">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,16">
          <mappingType>Field</mappingType>
          <srcFieldId>fieldType</srcFieldId>
          <dstFieldId>fieldType</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,17">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueTextarea</srcFieldId>
          <dstFieldId>observationsComments</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,18">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDropdown</srcFieldId>
          <dstFieldId>oCsDropdown</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,19">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueBookName</srcFieldId>
          <dstFieldId>oCsBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,20">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueSlnBookName</srcFieldId>
          <dstFieldId>oCsSlnBookName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,21">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDate</srcFieldId>
          <dstFieldId>oCsDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,22">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueDecimal</srcFieldId>
          <dstFieldId>oCsDecimal</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,23">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueImage</srcFieldId>
          <dstFieldId>oCsImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,24">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueNumber</srcFieldId>
          <dstFieldId>oCsNumber</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,25">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultValueText</srcFieldId>
          <dstFieldId>oCsText</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,26">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult1Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,27">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult2Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult3Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,29">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult4Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,30">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult5Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,31">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult6Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,32">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult7Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,33">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult8Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,34">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult9Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,35">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isResult10Available</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy>Constant</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,36">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isFromTemplate</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,37">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>type</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Section</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,38">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultValueSelection</srcFieldId>
          <dstFieldId>inspectReportRequirements.oCsSelection</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,39">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryAuditTemplateOptions</srcFieldId>
          <dstFieldId>inspectReportRequirements.inspectReportObservationsComments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,40">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,41">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,42">
          <mappingType>Field</mappingType>
          <srcFieldId>enableTextbox</srcFieldId>
          <dstFieldId>enableTextbox</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,43">
          <mappingType>Field</mappingType>
          <srcFieldId>isDisabled</srcFieldId>
          <dstFieldId>isDisabled</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,44">
          <mappingType>Field</mappingType>
          <srcFieldId>isChecked</srcFieldId>
          <dstFieldId>isChecked</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,checklistSectionToReport,45">
          <mappingType>Field</mappingType>
          <srcFieldId>textboxText</srcFieldId>
          <dstFieldId>textboxText</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="inspectReportSelectFactAddress" position="inspectReport_dataMappingRule.xlsx,inspectReportSelectFactAddress">
    <DataMappingRule description="InspectReport form select address" domain="/" dstEntityName="InspectReport" dstEntityVersion="1" effectiveDate="2022-05-05" id="inspectReportSelectFactAddress" position="inspectReport_dataMappingRule.xlsx,inspectReportSelectFactAddress,1" srcEntityName="FactAddress" srcEntityVersion="1" status="1" updatedDate="2022-05-05">
      <elements id="mappingRule">
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportSelectFactAddress,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="inspectReport_dataMappingRule.xlsx,inspectReportSelectFactAddress,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factoryAddress</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
