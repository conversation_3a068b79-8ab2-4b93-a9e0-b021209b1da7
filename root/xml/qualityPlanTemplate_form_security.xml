<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="qualityPlanTemplate" position="qualityPlanTemplate_form_security.xlsx">
  <sheet id="_system" position="qualityPlanTemplate_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="qualityPlanTemplate_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="qualityPlanTemplate_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,_system,10">
          <updatedOn>2015/Jul/17</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="qualityPlanTemplate_form_security.xlsx,generalInfo">
    <GeneralInfo position="qualityPlanTemplate_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="qualityPlanTemplate_form_security.xlsx,condition">
    <ConditionList position="qualityPlanTemplate_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,6">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,7">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,8">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,9">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,10">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,11">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,12">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,13">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,14">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,condition,15">
          <conditionId>isExternalDomain</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="qualityPlanTemplate_form_security.xlsx,default">
    <ActionConditionMatrix position="qualityPlanTemplate_form_security.xlsx,default,1">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,default,4">
          <actionId>loadDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,5">
          <actionId>newDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,6">
          <actionId>editDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,7">
          <actionId>amendDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,8">
          <actionId>saveDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,9">
          <actionId>baseSaveDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,10">
          <actionId>saveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,11">
          <actionId>discardDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,12">
          <actionId>copyDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,13">
          <actionId>activateDoc</actionId>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,14">
          <actionId>deactivateDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,15">
          <actionId>cancelDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,16">
          <actionId>initializeCpm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>disallowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,17">
          <actionId>customDocAction01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,18">
          <actionId>customDocAction02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,19">
          <actionId>customDocAction03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,20">
          <actionId>customDocAction04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,21">
          <actionId>customDocAction05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,22">
          <actionId>customDocAction06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,23">
          <actionId>customDocAction07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,24">
          <actionId>customDocAction08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,25">
          <actionId>customDocAction09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,26">
          <actionId>customDocAction10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,27">
          <actionId>markAsCustomStatus01Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,28">
          <actionId>markAsCustomStatus02Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,29">
          <actionId>markAsCustomStatus03Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,30">
          <actionId>markAsCustomStatus04Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,31">
          <actionId>markAsCustomStatus05Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,32">
          <actionId>markAsCustomStatus06Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,33">
          <actionId>markAsCustomStatus07Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,34">
          <actionId>markAsCustomStatus08Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,35">
          <actionId>markAsCustomStatus09Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,36">
          <actionId>markAsCustomStatus10Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,37">
          <actionId>customExport01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,38">
          <actionId>customExport02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,39">
          <actionId>customExport03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,40">
          <actionId>customExport04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,41">
          <actionId>customExport05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,42">
          <actionId>customExport06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,43">
          <actionId>customExport07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,44">
          <actionId>customExport08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,45">
          <actionId>customExport09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,46">
          <actionId>customExport10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,47">
          <actionId>customPrint01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,48">
          <actionId>customPrint02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,49">
          <actionId>customPrint03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,50">
          <actionId>customPrint04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,51">
          <actionId>customPrint05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,52">
          <actionId>customPrint06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,53">
          <actionId>customPrint07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,54">
          <actionId>customPrint08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,55">
          <actionId>customPrint09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,56">
          <actionId>customPrint10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,57">
          <actionId>reinitializeCpm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,58">
          <actionId>refreshCpmTemplate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,59">
          <actionId>refreshCpmPlanDate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="qualityPlanTemplate_form_security.xlsx,default,62">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,default,65">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isExternalDomain>editable</isExternalDomain>
          <isHeaderHclReadOnly>editable</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,66">
          <componentId>ui.qualityPlanTemplateLinkbar.followDoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,67">
          <componentId>ui.qualityPlanTemplateLinkbar.unfollowDoc</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,68">
          <componentId>ui.qualityPlanTemplateLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,69">
          <componentId>ui.qualityPlanTemplateLinkbar.addToFavorites</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,70">
          <componentId>ui.qualityPlanTemplateLinkbar.approval</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,71">
          <componentId>ui.qualityPlanTemplateLinkbar.relatedActivities</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,72">
          <componentId>ui.qualityPlanTemplateLinkbar.editDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,default,73">
          <componentId>ui.qualityPlanTemplateLinkbar.amendDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isExternalDomain>inherit</isExternalDomain>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="qualityPlanTemplate_form_security.xlsx,acl">
    <ActionRule position="qualityPlanTemplate_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,acl,4">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,5">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,6">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,7">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,8">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,9">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,10">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,11">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,12">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,13">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,14">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,15">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,16">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,17">
          <actionId>customDocAction01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,18">
          <actionId>customDocAction02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,19">
          <actionId>customDocAction03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,20">
          <actionId>customDocAction04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,21">
          <actionId>customDocAction05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,22">
          <actionId>customDocAction06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,23">
          <actionId>customDocAction07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,24">
          <actionId>customDocAction08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,25">
          <actionId>customDocAction09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,26">
          <actionId>customDocAction10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,27">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,28">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,29">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,30">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,31">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,32">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,33">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,34">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,35">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,36">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,37">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,38">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,39">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,40">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,41">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,42">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,43">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,44">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,45">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,46">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,47">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,48">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,49">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,50">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,51">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,52">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,53">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,54">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,55">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,56">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,57">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,58">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,59">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <qualityPlanTemplate.Author>not-has</qualityPlanTemplate.Author>
          <qualityPlanTemplate.Editor>not-has</qualityPlanTemplate.Editor>
          <qualityPlanTemplate.ReadOnly>not-has</qualityPlanTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="qualityPlanTemplate_form_security.xlsx,acl,62">
      <elements id="default">
        <element position="qualityPlanTemplate_form_security.xlsx,acl,65">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,66">
          <componentId>ui.tabHeader.generalInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,67">
          <componentId>ui.tabLinkageToDoc</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,68">
          <componentId>ui.tabLinkageToDoc.linkageToDocSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,69">
          <componentId>ui.tabLinkageToDoc.qualityPlanTemplateLinkedDocuments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="qualityPlanTemplate_form_security.xlsx,acl,70">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="qualityPlanTemplate_form_security.xlsx,acl,73">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
