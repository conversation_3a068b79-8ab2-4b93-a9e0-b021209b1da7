<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="materialPalette" position="materialPalette_dataMappingRule.xlsx">
  <sheet id="materialPaletteSelectMaterial" position="materialPalette_dataMappingRule.xlsx,materialPaletteSelectMaterial">
    <DataMappingRule description="Mapping from Component to Material Palette" domain="/" dstEntityName="MaterialPalette" dstEntityVersion="1" effectiveDate="2012-02-20" id="materialPaletteSelectMaterial" position="materialPalette_dataMappingRule.xlsx,materialPaletteSelectMaterial,1" srcEntityName="Component" srcEntityVersion="1" status="1" updatedDate="2014-01-16">
      <elements id="mappingRule">
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteSelectMaterial,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteSelectMaterial,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialPaletteMaterial</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteSelectMaterial,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialPaletteMaterial.componentId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="materialPaletteCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy">
    <DataMappingRule description="Mapping from MaterialPalette table to MaterialPalette" domain="/" dstEntityName="MaterialPalette" dstEntityVersion="1" effectiveDate="2014-02-13" id="materialPaletteCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,1" srcEntityName="MaterialPalette" srcEntityVersion="1" status="1" updatedDate="2014-02-13">
      <elements id="mappingRule">
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>paletteCode</srcFieldId>
          <dstFieldId>paletteCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="materialPaletteImagesCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteImagesCopy">
    <DataMappingRule description="Mapping for Copy Material Palette Images" domain="/" dstEntityName="MaterialPalette" dstEntityVersion="1" effectiveDate="2012-12-12" id="materialPaletteImagesCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteImagesCopy,1" srcEntityName="MaterialPaletteImage" srcEntityVersion="1" status="1" updatedDate="2012-12-12">
      <elements id="mappingRule">
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteImagesCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteImagesCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialPaletteImage</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="materialPaletteAttachmentsCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteAttachmentsCopy">
    <DataMappingRule description="Mapping for Copy Material Palette Attachments" domain="/" dstEntityName="MaterialPalette" dstEntityVersion="1" effectiveDate="2012-12-12" id="materialPaletteAttachmentsCopy" position="materialPalette_dataMappingRule.xlsx,materialPaletteAttachmentsCopy,1" srcEntityName="MaterialPaletteAttachment" srcEntityVersion="1" status="1" updatedDate="2012-12-12">
      <elements id="mappingRule">
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteAttachmentsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="materialPalette_dataMappingRule.xlsx,materialPaletteAttachmentsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialPaletteAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
