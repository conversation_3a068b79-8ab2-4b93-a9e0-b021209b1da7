<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="termsAndConditions" position="termsAndConditions_view_security.xlsx">
  <sheet id="generalInfo" position="termsAndConditions_view_security.xlsx,generalInfo">
    <GeneralInfo position="termsAndConditions_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="termsAndConditions_view_security.xlsx,condition">
    <ConditionList position="termsAndConditions_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="termsAndConditions_view_security.xlsx,default">
    <ActionConditionMatrix position="termsAndConditions_view_security.xlsx,default,1">
      <elements id="default">
        <element position="termsAndConditions_view_security.xlsx,default,4">
          <actionId>searchActivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,5">
          <actionId>searchDeactivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,6">
          <actionId>searchNewDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,7">
          <actionId>searchViewAllExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,8">
          <actionId>searchViewCurExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,9">
          <actionId>batchUpdateField</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,10">
          <actionId>openBatchUpdateWin</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,11">
          <actionId>openCpmMode</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,12">
          <actionId>adminModuleDownload</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="termsAndConditions_view_security.xlsx,default,13">
          <actionId>adminModuleUpload</actionId>
          <ANY>allowed</ANY>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="termsAndConditions_view_security.xlsx,default,16">
      <elements id="default"/>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="termsAndConditions_view_security.xlsx,acl">
    <ActionRule position="termsAndConditions_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="termsAndConditions_view_security.xlsx,acl,4">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,5">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,6">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>not-has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,7">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,8">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,9">
          <actionId>batchUpdateField</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,10">
          <actionId>openBatchUpdateWin</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,11">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <termsAndConditions.Author>not-has</termsAndConditions.Author>
          <termsAndConditions.Editor>not-has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,12">
          <actionId>adminModuleDownload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>not-has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,13">
          <actionId>adminModuleUpload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <termsAndConditions.Author>has</termsAndConditions.Author>
          <termsAndConditions.Editor>has</termsAndConditions.Editor>
          <termsAndConditions.ReadOnly>has</termsAndConditions.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="termsAndConditions_view_security.xlsx,acl,16">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="termsAndConditions_view_security.xlsx,acl,21">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
