<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="sampleTracker" position="sampleTracker_dataMappingRule.xlsx">
  <sheet id="sampleTrackerCopyDoc" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc">
    <DataMappingRule description="Mapping for sampleTracker copy doc" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleTrackerCopyDoc" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>trackerNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,14">
          <mappingType>Field</mappingType>
          <srcFieldId>result</srcFieldId>
          <dstFieldId>result</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,15">
          <mappingType>Field</mappingType>
          <srcFieldId>reviewUser</srcFieldId>
          <dstFieldId>reviewUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,16">
          <mappingType>Field</mappingType>
          <srcFieldId>reviewedOn</srcFieldId>
          <dstFieldId>reviewedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,17">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>comments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.isSampleSent</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,22">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SampleTrackerCopyDocPreProcessor</implementationClass>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerCopyDoc,23">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SampleTrackerCopyDocPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleDetailsPopupCopy" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsPopupCopy">
    <DataMappingRule description="Mapping Copy SampleDetail" domain="/" dstEntityName="SampleDetail" dstEntityVersion="1" effectiveDate="2016-12-19" id="sampleDetailsPopupCopy" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsPopupCopy,1" srcEntityName="SampleDetail" srcEntityVersion="1" status="1" updatedDate="2016-12-29">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsPopupCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsPopupCopy,12">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SampleDetailsPopupCopyProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy">
    <DataMappingRule description="Mapping Copy SampleDetail" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,1" srcEntityName="SampleDetail" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.evaluateUser</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleVersion</dstFieldId>
          <dstFieldType/>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.$readonly</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.isSampleSent</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.isCopiedInVendor</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.requestedUser</dstFieldId>
          <dstFieldType/>
          <mappedValue>user</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopy,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.requestedDate</dstFieldId>
          <dstFieldType/>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleDetailsCopyInVendor" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor">
    <DataMappingRule description="Mapping Copy SampleDetail in Vendor domain." domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleDetailsCopyInVendor" position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,1" srcEntityName="SampleDetail" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.evaluateUser</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleId</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleVersion</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.vendorComments</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.receivedDate</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.receivedQuantity</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.status</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.result</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.vendorAttachment1</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,22">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.vendorAttachment2</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,23">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.vendorAttachment3</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,24">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sentDate</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,25">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sentQuantity</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,26">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.$readonly</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,27">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.isSampleSent</dstFieldId>
          <dstFieldType/>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailsCopyInVendor,28">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.isCopiedInVendor</dstFieldId>
          <dstFieldType/>
          <mappedValue>TRUE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="materialDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy">
    <DataMappingRule description="Mapping Copy MaterialDetail" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="materialDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,1" srcEntityName="MaterialDetail" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.evaluateUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.sampleVersion</dstFieldId>
          <dstFieldType/>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.$readonly</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.requestedUser</dstFieldId>
          <dstFieldType/>
          <mappedValue>user</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailsCopy,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.requestedDate</dstFieldId>
          <dstFieldType/>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="documentDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy">
    <DataMappingRule description="Mapping for DocumentDetailsCopy" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="documentDetailsCopy" position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,1" srcEntityName="DocumentDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>documentDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.sampleVersion</dstFieldId>
          <dstFieldType/>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.$readonly</dstFieldId>
          <dstFieldType/>
          <mappedValue/>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.requestedUser</dstFieldId>
          <dstFieldType/>
          <mappedValue>user</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailsCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.requestedDate</dstFieldId>
          <dstFieldType/>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate">
    <DataMappingRule description="Mapping from SampleRequestTemplate to SampleTracker" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="sampleDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,1" srcEntityName="SampleRequestTmplDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>sampleDetail.seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>sampleDetail.description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultQuantity</srcFieldId>
          <dstFieldId>sampleDetail.requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,13">
          <mappingType>Field</mappingType>
          <srcFieldId>altSizeCode</srcFieldId>
          <dstFieldId>sampleDetail.altSizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,14">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>sampleDetail.weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,15">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>sampleDetail.instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,16">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>sampleDetail.approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>1900-01-01</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sampleDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.sizeCode.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,19">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,20">
          <mappingType>Field</mappingType>
          <srcFieldId>isReqVendorEva</srcFieldId>
          <dstFieldId>sampleRequestDetail.isReqVendorEva</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,21">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleType</srcFieldId>
          <dstFieldId>sampleDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,22">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>sampleDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,23">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUom</srcFieldId>
          <dstFieldId>sampleDetail.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,24">
          <mappingType>Section</mappingType>
          <srcFieldId>deliverTo</srcFieldId>
          <dstFieldId>sampleDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,25">
          <mappingType>Section</mappingType>
          <srcFieldId>colorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,29">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SelectRequestTemplateDetailPreProcessor</implementationClass>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectTemplate,30">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.TrackerSelectRequestTemplateDetailProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="materialDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate">
    <DataMappingRule description="Mapping from MaterialDetailTemplate to SampleTracker" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="materialDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,1" srcEntityName="MaterialReqTmplDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>materialDetail.seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>materialDetail.description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultQuantity</srcFieldId>
          <dstFieldId>materialDetail.requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,13">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>materialDetail.weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,14">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>materialDetail.instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,15">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>materialDetail.approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>1900-01-01</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,17">
          <mappingType>Field</mappingType>
          <srcFieldId>yardage</srcFieldId>
          <dstFieldId>materialDetail.yardage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,18">
          <mappingType>Field</mappingType>
          <srcFieldId>dimension</srcFieldId>
          <dstFieldId>materialDetail.dimension</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,19">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorAndPattern</srcFieldId>
          <dstFieldId>materialDetail.altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.sizeCode.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,21">
          <mappingType>Section</mappingType>
          <srcFieldId>component</srcFieldId>
          <dstFieldId>materialDetail.material</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,22">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>materialDetail.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,23">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>materialDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,24">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUom</srcFieldId>
          <dstFieldId>materialDetail.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,25">
          <mappingType>Section</mappingType>
          <srcFieldId>deliverTo</srcFieldId>
          <dstFieldId>materialDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,26">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleType</srcFieldId>
          <dstFieldId>materialDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,27">
          <mappingType>Section</mappingType>
          <srcFieldId>colorAndPattern</srcFieldId>
          <dstFieldId>materialDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,31">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SelectRequestTemplateDetailPreProcessor</implementationClass>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectTemplate,32">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.TrackerSelectMaterialTemplateDetailProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="documentDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate">
    <DataMappingRule description="Mapping from DocumentRequestTemplate to SampleTracker" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="documentDetailSelectTemplate" position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,1" srcEntityName="DocumentReqTmplDetail" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>documentDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,10">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>documentDetail.seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,11">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>documentDetail.description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,12">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultQuantity</srcFieldId>
          <dstFieldId>documentDetail.requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,13">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>documentDetail.name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,14">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>documentDetail.instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,15">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>documentDetail.approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>documentDetail.dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>1900-01-01</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,17">
          <mappingType>Section</mappingType>
          <srcFieldId>deliverTo</srcFieldId>
          <dstFieldId>documentDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,18">
          <mappingType>Section</mappingType>
          <srcFieldId>documentType</srcFieldId>
          <dstFieldId>documentDetail.documentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,documentDetailSelectTemplate,22">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.TrackerSelectDocumentTemplateDetailProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleDetailSelectSampleType" position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectSampleType">
    <DataMappingRule description="Mapping from Component to MaterialRequestDetail" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="sampleDetailSelectSampleType" position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectSampleType,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectSampleType,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectSampleType,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleDetailSelectSampleType,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="materialDetailSelectMaterial" position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial">
    <DataMappingRule description="Mapping from Component to MaterialRequestDetail" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="materialDetailSelectMaterial" position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,1" srcEntityName="Component" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,10">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,11">
          <mappingType>Field</mappingType>
          <srcFieldId>componentNo</srcFieldId>
          <dstFieldId>materialNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,12">
          <mappingType>Field</mappingType>
          <srcFieldId>materialName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>materialDetail.material</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,materialDetailSelectMaterial,14">
          <mappingType>Section</mappingType>
          <srcFieldId>materialType</srcFieldId>
          <dstFieldId>materialDetail.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="docDetailSelectDocumentType" position="sampleTracker_dataMappingRule.xlsx,docDetailSelectDocumentType">
    <DataMappingRule description="Mapping from Component to MaterialRequestDetail" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-02-20" id="docDetailSelectDocumentType" position="sampleTracker_dataMappingRule.xlsx,docDetailSelectDocumentType,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,docDetailSelectDocumentType,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,docDetailSelectDocumentType,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>documentDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,docDetailSelectDocumentType,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>documentDetail.documentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copySampleTrackerImage" position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerImage">
    <DataMappingRule description="Mapping from SampleTrackerImage to SampleTracker" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="copySampleTrackerImage" position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerImage,1" srcEntityName="SampleTrackerImage" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerImage,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerImage,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleTrackerImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerImage,10">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>FALSE</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copySampleTrackerAttachment" position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerAttachment">
    <DataMappingRule description="Mapping from SampleTrackerAttachment to SampleTracker" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="copySampleTrackerAttachment" position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerAttachment,1" srcEntityName="SampleTrackerAttachment" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,copySampleTrackerAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleTrackerAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleTrackerSEToSampleRequest" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest">
    <DataMappingRule description="Mapping from SampleTracker to Sample Request" domain="/" dstEntityName="SampleRequest" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleTrackerSEToSampleRequest" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,9">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,10">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,11">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleRequestTemplate</srcFieldId>
          <dstFieldId>sampleRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,12">
          <mappingType>Section</mappingType>
          <srcFieldId>materialRequestTemplate</srcFieldId>
          <dstFieldId>materialRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,13">
          <mappingType>Section</mappingType>
          <srcFieldId>documentRequestTemplate</srcFieldId>
          <dstFieldId>documentRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,14">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,15">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleTracker</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition>entity.vendor != null</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,18">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor.vendorName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,19">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor.vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,21">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,22">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.factory</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,23">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition>entity.item != null</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,24">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,25">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,26">
          <mappingType>Field</mappingType>
          <srcFieldId>item.shortDesc</srcFieldId>
          <dstFieldId>itemShortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,27">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,28">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemType</srcFieldId>
          <dstFieldId>sampleRequestItem.itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,29">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.item</srcFieldId>
          <dstFieldId>sampleRequestItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,30">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.sourcingRecord</srcFieldId>
          <dstFieldId>sampleRequestItem.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,31">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,32">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,33">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail</srcFieldId>
          <dstFieldId>documentRequestDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isSelected = '1'</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,34">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,35">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,37">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,38">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,39">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,40">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,41">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,42">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,43">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,44">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.documentType</srcFieldId>
          <dstFieldId>documentRequestDetail.documentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,45">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.deliverTo</srcFieldId>
          <dstFieldId>documentRequestDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,46">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment1</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,47">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment2</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,48">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment3</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,52">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.PrepareRelatedEntityProcessor</implementationClass>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerSEToSampleRequest,53">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SampleTrackerNewSampleRequestProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleTrackerToSampleRequest" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest">
    <DataMappingRule description="Mapping from SampleTracker to Sample Request" domain="/" dstEntityName="SampleRequest" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleTrackerToSampleRequest" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,9">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,10">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,11">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleRequestTemplate</srcFieldId>
          <dstFieldId>sampleRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,12">
          <mappingType>Section</mappingType>
          <srcFieldId>materialRequestTemplate</srcFieldId>
          <dstFieldId>materialRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,13">
          <mappingType>Section</mappingType>
          <srcFieldId>documentRequestTemplate</srcFieldId>
          <dstFieldId>documentRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,14">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,15">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleTracker</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestVendor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition>entity.vendor != null</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,18">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor.vendorName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,19">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor.vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vendor.vendorEmail</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,21">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.vendor</srcFieldId>
          <dstFieldId>sampleRequestVendor.vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,22">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.factory</srcFieldId>
          <dstFieldId>sampleRequestVendor.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,23">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleRequestItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition>entity.item != null</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,24">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,25">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,26">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemShortDesc</srcFieldId>
          <dstFieldId>itemShortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,27">
          <mappingType>Field</mappingType>
          <srcFieldId>item.itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,28">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.item</srcFieldId>
          <dstFieldId>sampleRequestItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,29">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.sourcingRecord</srcFieldId>
          <dstFieldId>sampleRequestItem.sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,30">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.season</srcFieldId>
          <dstFieldId>sampleRequestItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,31">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root.year</srcFieldId>
          <dstFieldId>sampleRequestItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,32">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail</srcFieldId>
          <dstFieldId>sampleRequestDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isSelected = '1'</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,33">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,34">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,35">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,37">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,38">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,39">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorAndPattern</srcFieldId>
          <dstFieldId>altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,40">
          <mappingType>Field</mappingType>
          <srcFieldId>altSizeCode</srcFieldId>
          <dstFieldId>altSizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,41">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,42">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,43">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,44">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,45">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.sizeCode</srcFieldId>
          <dstFieldId>sampleRequestDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,46">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleType</srcFieldId>
          <dstFieldId>sampleRequestDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,47">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.colorAndPattern</srcFieldId>
          <dstFieldId>sampleRequestDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,48">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.pattern</srcFieldId>
          <dstFieldId>sampleRequestDetail.pattern</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,49">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.uom</srcFieldId>
          <dstFieldId>sampleRequestDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,50">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.weightUOM</srcFieldId>
          <dstFieldId>sampleRequestDetail.weightUom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,51">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.deliverTo</srcFieldId>
          <dstFieldId>sampleRequestDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,52">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment1</srcFieldId>
          <dstFieldId>sampleRequestDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,53">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment2</srcFieldId>
          <dstFieldId>sampleRequestDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,54">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment3</srcFieldId>
          <dstFieldId>sampleRequestDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,55">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail</srcFieldId>
          <dstFieldId>materialRequestDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isSelected = '1'</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,56">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,57">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,58">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,59">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,60">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,61">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,62">
          <mappingType>Field</mappingType>
          <srcFieldId>altColorAndPattern</srcFieldId>
          <dstFieldId>altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,63">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,64">
          <mappingType>Field</mappingType>
          <srcFieldId>altSizeCode</srcFieldId>
          <dstFieldId>altSizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,65">
          <mappingType>Field</mappingType>
          <srcFieldId>dimension</srcFieldId>
          <dstFieldId>dimension</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,66">
          <mappingType>Field</mappingType>
          <srcFieldId>yardage</srcFieldId>
          <dstFieldId>yardage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,67">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,68">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,69">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,70">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,71">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.sampleType</srcFieldId>
          <dstFieldId>materialRequestDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,72">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.materialType</srcFieldId>
          <dstFieldId>materialRequestDetail.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,73">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.material</srcFieldId>
          <dstFieldId>materialRequestDetail.material</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,74">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.uom</srcFieldId>
          <dstFieldId>materialRequestDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,75">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.colorAndPattern</srcFieldId>
          <dstFieldId>materialRequestDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,76">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.weightUOM</srcFieldId>
          <dstFieldId>materialRequestDetail.weightUom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,77">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.deliverTo</srcFieldId>
          <dstFieldId>materialRequestDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,78">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment1</srcFieldId>
          <dstFieldId>materialRequestDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,79">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment2</srcFieldId>
          <dstFieldId>materialRequestDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,80">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment3</srcFieldId>
          <dstFieldId>materialRequestDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,81">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail</srcFieldId>
          <dstFieldId>documentRequestDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isSelected = '1'</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,82">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>id</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>SYS_UUID</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,83">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,84">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,85">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,86">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,87">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,88">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,89">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,90">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,91">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,92">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.documentType</srcFieldId>
          <dstFieldId>documentRequestDetail.documentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,93">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.deliverTo</srcFieldId>
          <dstFieldId>documentRequestDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,94">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment1</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,95">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment2</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,96">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment3</srcFieldId>
          <dstFieldId>documentRequestDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,100">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.PrepareRelatedEntityProcessor</implementationClass>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerToSampleRequest,101">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SampleTrackerNewSampleRequestProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampletrackerDeliver" position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver">
    <DataMappingRule description="Mapping from SampleTracke (Hub domain)  to SampleTracker (Vendor domain) for document deliver" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampletrackerDeliver" position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>vendor,vendorCode,vendorName,vendorEmail,vendorRating,rank,rankName,assessmentLevel,assessmentLevelName</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>entityVersion</srcFieldId>
          <dstFieldId>entityVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>createdOn</srcFieldId>
          <dstFieldId>createdOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>trackerNo</srcFieldId>
          <dstFieldId>trackerNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleRequestNo</srcFieldId>
          <dstFieldId>sampleRequestNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedDate</srcFieldId>
          <dstFieldId>requestedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>reviewedOn</srcFieldId>
          <dstFieldId>reviewedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>comments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>reviewUserNamePretend</srcFieldId>
          <dstFieldId>reviewUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedUserNamePretend</srcFieldId>
          <dstFieldId>requestedUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,36">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,37">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,38">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,39">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,40">
          <mappingType>Section</mappingType>
          <srcFieldId>result</srcFieldId>
          <dstFieldId>result</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,41">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,42">
          <mappingType>Section</mappingType>
          <srcFieldId>requestFrom</srcFieldId>
          <dstFieldId>requestFrom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,43">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleRequest</srcFieldId>
          <dstFieldId>sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,44">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,45">
          <mappingType>Section</mappingType>
          <srcFieldId>rank</srcFieldId>
          <dstFieldId>rank</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,46">
          <mappingType>Section</mappingType>
          <srcFieldId>assessmentLevel</srcFieldId>
          <dstFieldId>assessmentLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,47">
          <mappingType>Section</mappingType>
          <srcFieldId>requestedUser</srcFieldId>
          <dstFieldId>requestedUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,48">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,49">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord</srcFieldId>
          <dstFieldId>sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,50">
          <mappingType>Section</mappingType>
          <srcFieldId>project</srcFieldId>
          <dstFieldId>project</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,51">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleRequestTemplate</srcFieldId>
          <dstFieldId>sampleRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,52">
          <mappingType>Section</mappingType>
          <srcFieldId>materialRequestTemplate</srcFieldId>
          <dstFieldId>materialRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,53">
          <mappingType>Section</mappingType>
          <srcFieldId>documentRequestTemplate</srcFieldId>
          <dstFieldId>documentRequestTemplate</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,54">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>approvalSeq</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>allowVendorSubmission</srcFieldId>
          <dstFieldId>allowVendorSubmission</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedDate</srcFieldId>
          <dstFieldId>requestedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>altSizeCode</srcFieldId>
          <dstFieldId>altSizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,68">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,69">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,70">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,71">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,72">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,73">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedDate</srcFieldId>
          <dstFieldId>receivedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,74">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedQuantity</srcFieldId>
          <dstFieldId>receivedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,75">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,76">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalComments</srcFieldId>
          <dstFieldId>additionalComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,77">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,78">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,79">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,80">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedUserNamePretend</srcFieldId>
          <dstFieldId>requestedUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId>isInactive</srcFieldId>
          <dstFieldId>isInactive</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>isCopiedInVendor</srcFieldId>
          <dstFieldId>isCopiedInVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.sizeCode</srcFieldId>
          <dstFieldId>sampleDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,84">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.altColorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,85">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.isReqVendorEva</srcFieldId>
          <dstFieldId>sampleDetail.isReqVendorEva</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,86">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.isSampleSent</srcFieldId>
          <dstFieldId>sampleDetail.isSampleSent</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.item</srcFieldId>
          <dstFieldId>sampleDetail.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.spec</srcFieldId>
          <dstFieldId>sampleDetail.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.requestedUser</srcFieldId>
          <dstFieldId>sampleDetail.requestedUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment3</srcFieldId>
          <dstFieldId>sampleDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment2</srcFieldId>
          <dstFieldId>sampleDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment1</srcFieldId>
          <dstFieldId>sampleDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,96">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleEvaluation</srcFieldId>
          <dstFieldId>sampleDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,97">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.status</srcFieldId>
          <dstFieldId>sampleDetail.status</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,98">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.deliverTo</srcFieldId>
          <dstFieldId>sampleDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,99">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.weightUOM</srcFieldId>
          <dstFieldId>sampleDetail.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,100">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.weight</srcFieldId>
          <dstFieldId>sampleDetail.weight</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,101">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.uom</srcFieldId>
          <dstFieldId>sampleDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,102">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleRequest</srcFieldId>
          <dstFieldId>sampleDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,103">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleType</srcFieldId>
          <dstFieldId>sampleDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,104">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.result</srcFieldId>
          <dstFieldId>sampleDetail.result</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,105">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.colorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,106">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail</srcFieldId>
          <dstFieldId>materialDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,107">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,108">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,109">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,110">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,111">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,112">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,113">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,114">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedDate</srcFieldId>
          <dstFieldId>requestedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,115">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,116">
          <mappingType>Field</mappingType>
          <srcFieldId>dimension</srcFieldId>
          <dstFieldId>dimension</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,117">
          <mappingType>Field</mappingType>
          <srcFieldId>yardage</srcFieldId>
          <dstFieldId>yardage</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,118">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,119">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,120">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,121">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,122">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,123">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,124">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,125">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedDate</srcFieldId>
          <dstFieldId>receivedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,126">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedQuantity</srcFieldId>
          <dstFieldId>receivedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,127">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,128">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalComments</srcFieldId>
          <dstFieldId>additionalComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,129">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,130">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,131">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,132">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,133">
          <mappingType>Field</mappingType>
          <srcFieldId>materialName</srcFieldId>
          <dstFieldId>materialName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,134">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedUserNamePretend</srcFieldId>
          <dstFieldId>requestedUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,135">
          <mappingType>Field</mappingType>
          <srcFieldId>isInactive</srcFieldId>
          <dstFieldId>isInactive</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,136">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>materialDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.sizeCode.name</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,137">
          <mappingType>Field</mappingType>
          <srcFieldId>materialDetail.altColorAndPattern</srcFieldId>
          <dstFieldId>materialDetail.altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,138">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.requestedUser</srcFieldId>
          <dstFieldId>materialDetail.requestedUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,139">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment1</srcFieldId>
          <dstFieldId>materialDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,140">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment2</srcFieldId>
          <dstFieldId>materialDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,141">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.attachment3</srcFieldId>
          <dstFieldId>materialDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,142">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,143">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,144">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,145">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.result</srcFieldId>
          <dstFieldId>materialDetail.result</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,146">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.status</srcFieldId>
          <dstFieldId>materialDetail.status</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,147">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.deliverTo</srcFieldId>
          <dstFieldId>materialDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,148">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.weightUOM</srcFieldId>
          <dstFieldId>materialDetail.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,149">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.uom</srcFieldId>
          <dstFieldId>materialDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,150">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.sampleType</srcFieldId>
          <dstFieldId>materialDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,151">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.materialType</srcFieldId>
          <dstFieldId>materialDetail.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,152">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.materialSubType</srcFieldId>
          <dstFieldId>materialDetail.materialSubType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,153">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.sampleRequest</srcFieldId>
          <dstFieldId>materialDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,154">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.spec</srcFieldId>
          <dstFieldId>materialDetail.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,155">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.sampleEvaluation</srcFieldId>
          <dstFieldId>materialDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,156">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.item</srcFieldId>
          <dstFieldId>materialDetail.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,157">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.material</srcFieldId>
          <dstFieldId>materialDetail.material</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,158">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.colorAndPattern</srcFieldId>
          <dstFieldId>materialDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,159">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail</srcFieldId>
          <dstFieldId>documentDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,160">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,161">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,162">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,163">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,164">
          <mappingType>Field</mappingType>
          <srcFieldId>name</srcFieldId>
          <dstFieldId>name</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,165">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,166">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,167">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,168">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,169">
          <mappingType>Field</mappingType>
          <srcFieldId>qcFile</srcFieldId>
          <dstFieldId>qcFile</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,170">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedDate</srcFieldId>
          <dstFieldId>requestedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,171">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,172">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,173">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,174">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,175">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,176">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,177">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedDate</srcFieldId>
          <dstFieldId>receivedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,178">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedQuantity</srcFieldId>
          <dstFieldId>receivedQuantity</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,179">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,180">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalComments</srcFieldId>
          <dstFieldId>additionalComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,181">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,182">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,183">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,184">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedUserNamePretend</srcFieldId>
          <dstFieldId>requestedUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,185">
          <mappingType>Field</mappingType>
          <srcFieldId>isInactive</srcFieldId>
          <dstFieldId>isInactive</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,186">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment1</srcFieldId>
          <dstFieldId>documentDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,187">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment2</srcFieldId>
          <dstFieldId>documentDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,188">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.attachment3</srcFieldId>
          <dstFieldId>documentDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,189">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,190">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,191">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,192">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.result</srcFieldId>
          <dstFieldId>documentDetail.result</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,193">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.requestedUser</srcFieldId>
          <dstFieldId>documentDetail.requestedUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,194">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.status</srcFieldId>
          <dstFieldId>documentDetail.status</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,195">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.deliverTo</srcFieldId>
          <dstFieldId>documentDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,196">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.documentType</srcFieldId>
          <dstFieldId>documentDetail.documentType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,197">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.item</srcFieldId>
          <dstFieldId>documentDetail.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,198">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.spec</srcFieldId>
          <dstFieldId>documentDetail.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,199">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.sampleRequest</srcFieldId>
          <dstFieldId>documentDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,200">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerImages</srcFieldId>
          <dstFieldId>sampleTrackerImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,201">
          <mappingType>Field</mappingType>
          <srcFieldId>isDefault</srcFieldId>
          <dstFieldId>isDefault</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,202">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,203">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,204">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,205">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,206">
          <mappingType>Field</mappingType>
          <srcFieldId>qcFile</srcFieldId>
          <dstFieldId>qcFile</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,207">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerImages.image</srcFieldId>
          <dstFieldId>sampleTrackerImages.image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,208">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerImages.imageTypes</srcFieldId>
          <dstFieldId>sampleTrackerImages.imageTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,209">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerAttachments</srcFieldId>
          <dstFieldId>sampleTrackerAttachments</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,210">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,211">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,212">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,213">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,214">
          <mappingType>Field</mappingType>
          <srcFieldId>fileAddress</srcFieldId>
          <dstFieldId>fileAddress</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,215">
          <mappingType>Field</mappingType>
          <srcFieldId>qcFile</srcFieldId>
          <dstFieldId>qcFile</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,216">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerAttachments.attachment</srcFieldId>
          <dstFieldId>sampleTrackerAttachments.attachment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,217">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleTrackerAttachments.attachmentTypes</srcFieldId>
          <dstFieldId>sampleTrackerAttachments.attachmentTypes</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,218">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,219">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerDeliver,223">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SelectRequestTemplateDetailPreProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="sampletrackerVendorDeliver" position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver">
    <DataMappingRule description="Mapping from SampleTracke (Vendor domain)  to SampleTracker (Hub domain) for document deliver" domain="/" dstEntityName="SampleTracker" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampletrackerVendorDeliver" position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldToUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>domainId,hubDomainId,updateUser,updateUserName,updatedOn,version,status,editingStatus,isLatest,refNo,trackerNo,version,updatedOn,factory,sampleDetail,materialDetail,documentDetail,businessRefNo,factoryName,country,rank,rankName,assessmentLevel,assessmentLevelName,shortDesc</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>domainId</srcFieldId>
          <dstFieldId>domainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>hubDomainId</srcFieldId>
          <dstFieldId>hubDomainId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isLatest</srcFieldId>
          <dstFieldId>isLatest</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>trackerNo</srcFieldId>
          <dstFieldId>trackerNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,25">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,26">
          <mappingType>Section</mappingType>
          <srcFieldId>rank</srcFieldId>
          <dstFieldId>rank</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,27">
          <mappingType>Section</mappingType>
          <srcFieldId>assessmentLevel</srcFieldId>
          <dstFieldId>assessmentLevel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,28">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,29">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail</srcFieldId>
          <dstFieldId>sampleDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldToUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>sentDate,sentQuantity,courier,trackingNo,vendorAttachment1,vendorAttachment2,vendorAttachment3,updateUserName,updatedOn,vendorComments,seqNo,sampleId,sampleVersion,description,allowVendorSubmission,requestedQuantity,requestedDate,altSizeCode,weight,dueDate,receivedDate,receivedQuantity,instructions,additionalComments,approvalSeq,requestedUserNamePretend,isInactive,sizeCode,altColorAndPattern,isReqVendorEva,isSampleSent,item,spec,requestedUser,attachment3,attachment2,attachment1,sampleEvaluation,status,deliverTo,weightUOM,weight,uom,sampleRequest,sampleType,result,colorAndPattern</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,38">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,39">
          <mappingType>Field</mappingType>
          <srcFieldId>seqNo</srcFieldId>
          <dstFieldId>seqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,40">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleId</srcFieldId>
          <dstFieldId>sampleId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,41">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleVersion</srcFieldId>
          <dstFieldId>sampleVersion</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,42">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,43">
          <mappingType>Field</mappingType>
          <srcFieldId>allowVendorSubmission</srcFieldId>
          <dstFieldId>allowVendorSubmission</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,44">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedQuantity</srcFieldId>
          <dstFieldId>requestedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,45">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedDate</srcFieldId>
          <dstFieldId>requestedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,46">
          <mappingType>Field</mappingType>
          <srcFieldId>altSizeCode</srcFieldId>
          <dstFieldId>altSizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,47">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,48">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,49">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedDate</srcFieldId>
          <dstFieldId>receivedDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,50">
          <mappingType>Field</mappingType>
          <srcFieldId>receivedQuantity</srcFieldId>
          <dstFieldId>receivedQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,51">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalComments</srcFieldId>
          <dstFieldId>additionalComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>approvalSeq</srcFieldId>
          <dstFieldId>approvalSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedUserNamePretend</srcFieldId>
          <dstFieldId>requestedUserNamePretend</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>isInactive</srcFieldId>
          <dstFieldId>isInactive</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>isCopiedInVendor</srcFieldId>
          <dstFieldId>isCopiedInVendor</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.sizeCode</srcFieldId>
          <dstFieldId>sampleDetail.sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.altColorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.altColorAndPattern</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.isReqVendorEva</srcFieldId>
          <dstFieldId>sampleDetail.isReqVendorEva</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>sampleDetail.isSampleSent</srcFieldId>
          <dstFieldId>sampleDetail.isSampleSent</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,61">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.item</srcFieldId>
          <dstFieldId>sampleDetail.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,62">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.spec</srcFieldId>
          <dstFieldId>sampleDetail.spec</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,63">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.requestedUser</srcFieldId>
          <dstFieldId>sampleDetail.requestedUser</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,64">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment3</srcFieldId>
          <dstFieldId>sampleDetail.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,65">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment2</srcFieldId>
          <dstFieldId>sampleDetail.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,66">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.attachment1</srcFieldId>
          <dstFieldId>sampleDetail.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,67">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,68">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,69">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>sampleDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,70">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleEvaluation</srcFieldId>
          <dstFieldId>sampleDetail.sampleEvaluation</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,71">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.status</srcFieldId>
          <dstFieldId>sampleDetail.status</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,72">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.deliverTo</srcFieldId>
          <dstFieldId>sampleDetail.deliverTo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,73">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.weightUOM</srcFieldId>
          <dstFieldId>sampleDetail.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,74">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.weight</srcFieldId>
          <dstFieldId>sampleDetail.weight</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,75">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.uom</srcFieldId>
          <dstFieldId>sampleDetail.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,76">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleRequest</srcFieldId>
          <dstFieldId>sampleDetail.sampleRequest</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,77">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.sampleType</srcFieldId>
          <dstFieldId>sampleDetail.sampleType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,78">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.result</srcFieldId>
          <dstFieldId>sampleDetail.result</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,79">
          <mappingType>Section</mappingType>
          <srcFieldId>sampleDetail.colorAndPattern</srcFieldId>
          <dstFieldId>sampleDetail.colorAndPattern</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,80">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail</srcFieldId>
          <dstFieldId>materialDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldToUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>sentDate,sentQuantity,courier,trackingNo,vendorAttachment1,vendorAttachment2,vendorAttachment3,updateUserName,updatedOn,vendorComments</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,84">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,85">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,86">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,87">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,88">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,89">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,92">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>materialDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,93">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.materialType</srcFieldId>
          <dstFieldId>materialDetail.materialType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,94">
          <mappingType>Section</mappingType>
          <srcFieldId>materialDetail.materialSubType</srcFieldId>
          <dstFieldId>materialDetail.materialSubType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,95">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail</srcFieldId>
          <dstFieldId>documentDetail</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,96">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldToUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>sentDate,sentQuantity,courier,trackingNo,vendorAttachment1,vendorAttachment2,vendorAttachment3,updateUserName,updatedOn,vendorComments</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,97">
          <mappingType>Field</mappingType>
          <srcFieldId>sentDate</srcFieldId>
          <dstFieldId>sentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,98">
          <mappingType>Field</mappingType>
          <srcFieldId>sentQuantity</srcFieldId>
          <dstFieldId>sentQuantity</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,99">
          <mappingType>Field</mappingType>
          <srcFieldId>courier</srcFieldId>
          <dstFieldId>courier</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,100">
          <mappingType>Field</mappingType>
          <srcFieldId>trackingNo</srcFieldId>
          <dstFieldId>trackingNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,101">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,102">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,103">
          <mappingType>Field</mappingType>
          <srcFieldId>qcFile</srcFieldId>
          <dstFieldId>qcFile</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,104">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,105">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,106">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment1</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,107">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment2</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampletrackerVendorDeliver,108">
          <mappingType>Section</mappingType>
          <srcFieldId>documentDetail.vendorAttachment3</srcFieldId>
          <dstFieldId>documentDetail.vendorAttachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="sampleTrackerNewSE" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE">
    <DataMappingRule description="Mapping from SampleTracke to SampleEvaluation" domain="/" dstEntityName="SampleEvaluation" dstEntityVersion="1" effectiveDate="2012-03-15" id="sampleTrackerNewSE" position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,1" srcEntityName="SampleTracker" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,9">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>sampleTracker</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,11">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,12">
          <mappingType>Field</mappingType>
          <srcFieldId>additionalDesc</srcFieldId>
          <dstFieldId>additionalDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,13">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,14">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,15">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord</srcFieldId>
          <dstFieldId>defaultSourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,16">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,17">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="sampleTracker_dataMappingRule.xlsx,sampleTrackerNewSE,18">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
