<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="sampleEvaluation" position="sampleEvaluation_form_security.xlsx">
  <sheet id="_system" position="sampleEvaluation_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="sampleEvaluation_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="sampleEvaluation_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/16</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="sampleEvaluation_form_security.xlsx,generalInfo">
    <GeneralInfo position="sampleEvaluation_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="sampleEvaluation_form_security.xlsx,condition">
    <ConditionList position="sampleEvaluation_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,condition,4">
          <conditionId>statusSubmitted</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,5">
          <conditionId>statusInProgress</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,6">
          <conditionId>statusApproved</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,7">
          <conditionId>statusRejected</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,8">
          <conditionId>statusWaive</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,9">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,10">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,11">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,12">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,13">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,14">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,15">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,16">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,17">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,18">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,19">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,20">
          <conditionId>isNotEvaluateForItemSample</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,21">
          <conditionId>isSampleEvaluationReadOnlyFromST</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,22">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,condition,23">
          <conditionId>isNotCurrentUserApprover</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="sampleEvaluation_form_security.xlsx,default">
    <ActionConditionMatrix position="sampleEvaluation_form_security.xlsx,default,1">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,default,4">
          <actionId>loadDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,5">
          <actionId>newDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,6">
          <actionId>editDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,7">
          <actionId>amendDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,8">
          <actionId>saveDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,9">
          <actionId>baseSaveDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,10">
          <actionId>saveAndConfirm</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,11">
          <actionId>discardDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,12">
          <actionId>openApprovalApprove</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>disallowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,13">
          <actionId>openApprovalReject</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>disallowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,14">
          <actionId>activateDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,15">
          <actionId>deactivateDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,16">
          <actionId>cancelDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,17">
          <actionId>initializeCpm</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,18">
          <actionId>sampleRequestSE</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,19">
          <actionId>copyDoc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,20">
          <actionId>markAsSubmit</actionId>
          <statusSubmitted>disallowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,21">
          <actionId>markAsInProgress</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>disallowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,22">
          <actionId>markAsApproved</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>disallowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,23">
          <actionId>markAsRejected</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>disallowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,24">
          <actionId>markAsWaive</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>disallowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,25">
          <actionId>sampleEvaluationSendToVendor</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>disallowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,26">
          <actionId>sampleEvaluationSendToBuyer</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>disallowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,27">
          <actionId>refreshFromItem</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,28">
          <actionId>updateToItem</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,29">
          <actionId>sampleEvaluationViewCapa</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,30">
          <actionId>sampleEvaluationCustom01</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,31">
          <actionId>sampleEvaluationCustom02</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,32">
          <actionId>sampleEvaluationCustom03</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,33">
          <actionId>sampleEvaluationCustom04</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,34">
          <actionId>sampleEvaluationCustom05</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,35">
          <actionId>sampleEvaluationCustom06</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,36">
          <actionId>sampleEvaluationCustom07</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,37">
          <actionId>sampleEvaluationCustom08</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,38">
          <actionId>sampleEvaluationCustom09</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,39">
          <actionId>sampleEvaluationCustom10</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,40">
          <actionId>markAsCustomStatus01Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,41">
          <actionId>markAsCustomStatus02Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,42">
          <actionId>markAsCustomStatus03Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,43">
          <actionId>markAsCustomStatus04Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,44">
          <actionId>markAsCustomStatus05Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,45">
          <actionId>markAsCustomStatus06Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,46">
          <actionId>markAsCustomStatus07Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,47">
          <actionId>markAsCustomStatus08Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,48">
          <actionId>markAsCustomStatus09Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,49">
          <actionId>markAsCustomStatus10Doc</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>disallowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,50">
          <actionId>customExport01</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,51">
          <actionId>customExport02</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,52">
          <actionId>customExport03</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,53">
          <actionId>customExport04</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,54">
          <actionId>customExport05</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,55">
          <actionId>customExport06</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,56">
          <actionId>customExport07</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,57">
          <actionId>customExport08</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,58">
          <actionId>customExport09</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,59">
          <actionId>customExport10</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,60">
          <actionId>customPrint01</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,61">
          <actionId>customPrint02</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,62">
          <actionId>customPrint03</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,63">
          <actionId>customPrint04</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,64">
          <actionId>customPrint05</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,65">
          <actionId>customPrint06</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,66">
          <actionId>customPrint07</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,67">
          <actionId>customPrint08</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,68">
          <actionId>customPrint09</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,69">
          <actionId>customPrint10</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,70">
          <actionId>reinitializeCpm</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,71">
          <actionId>refreshCpmTemplate</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,72">
          <actionId>refreshCpmPlanDate</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,73">
          <actionId>submitSampleEvaluation</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,74">
          <actionId>forumDownloadAttachment</actionId>
          <statusSubmitted>allowed</statusSubmitted>
          <statusInProgress>allowed</statusInProgress>
          <statusApproved>allowed</statusApproved>
          <statusRejected>allowed</statusRejected>
          <statusWaive>allowed</statusWaive>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isSampleEvaluationReadOnlyFromST>allowed</isSampleEvaluationReadOnlyFromST>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
          <isNotCurrentUserApprover>allowed</isNotCurrentUserApprover>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="sampleEvaluation_form_security.xlsx,default,77">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,default,80">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isNotEvaluateForItemSample>editable</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>editable</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,81">
          <componentId>ui.sampleEvaluationLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,82">
          <componentId>ui.sampleEvaluationMenubar.editDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,83">
          <componentId>ui.sampleEvaluationMenubar.amendDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,84">
          <componentId>ui.tabHeader.generalInfoSection.sampleType</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>hidden</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,85">
          <componentId>ui.tabHeader.generalInfoSection.materialSampleType</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>hidden</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,86">
          <componentId>ui.tabHeader.productInfoSection.sourcingRecordNo</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>hidden</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,87">
          <componentId>ui.tabFitAssessments</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>hidden</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>inherit</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,88">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.subItemNo</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>hidden</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,89">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.subItemNo</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>hidden</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,default,90">
          <componentId>ui.tabFitAssessments.fitAdditionalInfo.subItemNo</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isNotEvaluateForItemSample>inherit</isNotEvaluateForItemSample>
          <isMultipleSourcingRecord>inherit</isMultipleSourcingRecord>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isItem>hidden</isItem>
          <isEvaluateForItemSample>inherit</isEvaluateForItemSample>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="sampleEvaluation_form_security.xlsx,acl">
    <ActionRule position="sampleEvaluation_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,acl,4">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,5">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,6">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,7">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,8">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,9">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,10">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,11">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,12">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,13">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,14">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,15">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,16">
          <actionId>sampleRequestSE</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,17">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,18">
          <actionId>markAsSubmit</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,19">
          <actionId>markAsInProgress</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,20">
          <actionId>markAsApproved</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,21">
          <actionId>markAsRejected</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,22">
          <actionId>markAsWaive</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,23">
          <actionId>sampleEvaluationSendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,24">
          <actionId>sampleEvaluationSendToBuyer</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,25">
          <actionId>refreshFromItem</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,26">
          <actionId>updateToItem</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,27">
          <actionId>sampleEvaluationViewCapa</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,28">
          <actionId>sampleEvaluationCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,29">
          <actionId>sampleEvaluationCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,30">
          <actionId>sampleEvaluationCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,31">
          <actionId>sampleEvaluationCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,32">
          <actionId>sampleEvaluationCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,33">
          <actionId>sampleEvaluationCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,34">
          <actionId>sampleEvaluationCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,35">
          <actionId>sampleEvaluationCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,36">
          <actionId>sampleEvaluationCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,37">
          <actionId>sampleEvaluationCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,38">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,39">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,40">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,41">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,42">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,43">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,44">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,45">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,46">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,47">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,48">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,49">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,50">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,51">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,52">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,53">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,54">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,55">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,56">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,57">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,58">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,59">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,60">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,61">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,62">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,63">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,64">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,65">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,66">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,67">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,68">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,69">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,70">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,71">
          <actionId>submitSampleEvaluation</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <sampleEvaluation.Author>not-has</sampleEvaluation.Author>
          <sampleEvaluation.Editor>not-has</sampleEvaluation.Editor>
          <sampleEvaluation.ReadOnly>not-has</sampleEvaluation.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="sampleEvaluation_form_security.xlsx,acl,74">
      <elements id="default">
        <element position="sampleEvaluation_form_security.xlsx,acl,77">
          <componentId>ui</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,78">
          <componentId>ui.tabHeader.classificationSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,79">
          <componentId>ui.tabHeader.productInfoSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,80">
          <componentId>ui.tabHeader.productInfoSection.sourcingRecordNo</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,81">
          <componentId>ui.tabHeader.generalInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,82">
          <componentId>ui.tabHeader.generalInfoSection.evaluationType</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,83">
          <componentId>ui.tabHeader.generalInfoSection.sampleType</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,84">
          <componentId>ui.tabHeader.generalInfoSection.materialType</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,85">
          <componentId>ui.tabHeader.generalInfoSection.component</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,86">
          <componentId>ui.tabHeader.generalInfoSection.materialName</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,87">
          <componentId>ui.tabHeader.generalInfoSection.requestedQuantity</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,88">
          <componentId>ui.tabHeader.generalInfoSection.uom</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,89">
          <componentId>ui.tabHeader.generalInfoSection.colorAndPattern</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,90">
          <componentId>ui.tabHeader.generalInfoSection.altColorAndPattern</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,91">
          <componentId>ui.tabHeader.generalInfoSection.sizeCode</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,92">
          <componentId>ui.tabHeader.generalInfoSection.altSizeCode</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,93">
          <componentId>ui.tabHeader.generalInfoSection.dimension</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,94">
          <componentId>ui.tabHeader.generalInfoSection.yardage</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,95">
          <componentId>ui.tabHeader.generalInfoSection.weight</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,96">
          <componentId>ui.tabHeader.generalInfoSection.weightUOM</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,97">
          <componentId>ui.tabHeader.generalInfoSection.requestedDescription</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,98">
          <componentId>ui.tabHeader.generalInfoSection.requestedBy</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,99">
          <componentId>ui.tabHeader.generalInfoSection.requestedDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,100">
          <componentId>ui.tabHeader.sampleDetailsSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,101">
          <componentId>ui.tabHeader.sampleDetailsSection.sampleDeliverTo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,102">
          <componentId>ui.tabHeader.sampleDetailsSection.dueDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,103">
          <componentId>ui.tabHeader.sampleDetailsSection.attachment1</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,104">
          <componentId>ui.tabHeader.sampleDetailsSection.attachment2</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,105">
          <componentId>ui.tabHeader.sampleDetailsSection.attachment3</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,106">
          <componentId>ui.tabHeader.sampleDetailsSection.receivedDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,107">
          <componentId>ui.tabHeader.sampleDetailsSection.receivedQuantity</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,108">
          <componentId>ui.tabHeader.sampleDetailsSection.sampleStatus</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,109">
          <componentId>ui.tabHeader.sampleDetailsSection.sampleResult</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,110">
          <componentId>ui.tabHeader.sampleDetailsSection.expiryDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,111">
          <componentId>ui.tabHeader.sampleDetailsSection.instructions</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,112">
          <componentId>ui.tabHeader.sampleDetailsSection.additionalComments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,113">
          <componentId>ui.tabHeader.sampleDetailsSection.displaySeq</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,114">
          <componentId>ui.tabHeader.vendorInfoSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,115">
          <componentId>ui.tabHeader.vendorInfoSection.vendor</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,116">
          <componentId>ui.tabHeader.vendorInfoSection.vendorCode</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,117">
          <componentId>ui.tabHeader.vendorInfoSection.vendorEmail</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,118">
          <componentId>ui.tabHeader.vendorInfoSection.vendorRating</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,119">
          <componentId>ui.tabHeader.vendorInfoSection.factory</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,120">
          <componentId>ui.tabHeader.vendorInfoSection.factCountryOfOrigin</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,121">
          <componentId>ui.tabHeader.vendorInfoSection.factRank</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,122">
          <componentId>ui.tabHeader.vendorInfoSection.factAssessmentLevel</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,123">
          <componentId>ui.tabEvaluation.evaluationSummarySection.evaluateBy</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,124">
          <componentId>ui.tabEvaluation.evaluationSummarySection.evaluateDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,125">
          <componentId>ui.tabEvaluation.evaluationSummarySection.evaluateResult</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,126">
          <componentId>ui.tabEvaluation.evaluationSummarySection.overallExpiryDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,127">
          <componentId>ui.tabEvaluation.evaluationSummarySection.comments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,128">
          <componentId>ui.tabEvaluation.evaluationDtls.selectFromTemplate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,129">
          <componentId>ui.tabEvaluation.evaluationDtls.copyEvaluationDtl</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,130">
          <componentId>ui.tabEvaluation.evaluationDtls.delEvaluationDtl</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,131">
          <componentId>ui.tabEvaluation.evaluationDtls.addEvaluationDtl</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,132">
          <componentId>ui.tabEvaluation.evaluationDtls.seq</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,133">
          <componentId>ui.tabEvaluation.evaluationDtls.section</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,134">
          <componentId>ui.tabEvaluation.evaluationDtls.code</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,135">
          <componentId>ui.tabEvaluation.evaluationDtls.evaluation</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,136">
          <componentId>ui.tabEvaluation.evaluationDtls.description</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,137">
          <componentId>ui.tabEvaluation.evaluationDtls.comments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,138">
          <componentId>ui.tabEvaluation.evaluationDtls.correctionAction</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,139">
          <componentId>ui.tabEvaluation.evaluationDtls.expiryDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,140">
          <componentId>ui.tabEvaluation.evaluationDtls.attachment1</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,141">
          <componentId>ui.tabEvaluation.evaluationDtls.attachment2</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,142">
          <componentId>ui.tabEvaluation.evaluationDtls.attachment3</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,143">
          <componentId>ui.tabEvaluation.evaluationDtls.updateUserName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,144">
          <componentId>ui.tabEvaluation.evaluationDtls.updatedOn</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,145">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentTrackerNo</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,146">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentSampleId</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,147">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentSampleVersion</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,148">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentSampleType</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,149">
          <componentId>ui.tabFitAssessments.currentSampleSection.itemSampleSize</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,150">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitSampleSize</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,151">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitQty</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,152">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitUOM</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,153">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitUser</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,154">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitDate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,155">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentFitResult</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,156">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentComments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,157">
          <componentId>ui.tabFitAssessments.currentSampleSection.currentVendorQualityComment</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,158">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousSampleTracker</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,159">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousSampleId</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,160">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousSampleVersion</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,161">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousSampleTypeName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,162">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousItemSampleSize</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,163">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitSampleSize</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,164">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitQty</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,165">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitUOMName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,166">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitUser</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,167">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitDate</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,168">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousFitResultName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,169">
          <componentId>ui.tabFitAssessments.previousSampleSection.previousComments</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,170">
          <componentId>ui.tabFitAssessments.previousSampleSection.preVendorQualityComment</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,171">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.mfRefreshFromItem</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,172">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.mfUpdateToItem</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,173">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.seq</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,174">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.imageId</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,175">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.code</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,176">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.position</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,177">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.description</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,178">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.remarks</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,179">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.tolerancePositive</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,180">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.toleranceNegative</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,181">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.sampleMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,182">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.revisedMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,183">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.qaMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,184">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.vendorVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,185">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.qaVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,186">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.curRevisedMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,187">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.qaResult</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,188">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.qaComments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,189">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preVendorMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,190">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preQaMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,191">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preVendorVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,192">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preQaVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,193">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preRevisedMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,194">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preQaResult</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,195">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.preQaComments</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,196">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.refKey</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,197">
          <componentId>ui.tabFitAssessments.evalMeasurementFit.vendorMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,198">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.amfRefreshFromItem</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,199">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.amfUpdateToItem</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,200">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.seq</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,201">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.code</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,202">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.position</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,203">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.description</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,204">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.remarks</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,205">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.tolerancePositive</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,206">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.toleranceNegative</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,207">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.sampleMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,208">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.revisedMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,209">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.vendorVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,210">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.qaMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,211">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.qaVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,212">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.curRevisedMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,213">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.qaResult</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,214">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.qaComments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,215">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preVendorMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,216">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preQaMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,217">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preVendorVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,218">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preQaVariance</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,219">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preRevisedMeasurement</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,220">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preQaResult</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,221">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.preQaComments</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,222">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.refKey</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,223">
          <componentId>ui.tabFitAssessments.evalAccessoriesMeasurementFit.vendorMeasurement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,224">
          <componentId>ui.tabFitAssessments.fitAdditionalInfo.addFitAdditionalInfo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,225">
          <componentId>ui.tabFitAssessments.fitAdditionalInfo.copyFitAdditionalInfo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,226">
          <componentId>ui.tabFitAssessments.fitAdditionalInfo.delFitAdditionalInfo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,227">
          <componentId>ui.tabImage</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,228">
          <componentId>ui.tabHeader.custInfoSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,229">
          <componentId>ui.tabHeader.custInfoSection.custId</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,230">
          <componentId>ui.tabHeader.sampleEvaluationShareFile.selectShareFile</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="sampleEvaluation_form_security.xlsx,acl,231">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="sampleEvaluation_form_security.xlsx,acl,234">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
