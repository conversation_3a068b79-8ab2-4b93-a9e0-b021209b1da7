<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="tag" position="tag_entity.xlsx">
  <sheet id="_system" position="tag_entity.xlsx,_system">
    <ProjectInfo client="Base" position="tag_entity.xlsx,_system,1" project="SCMS" release_no="1.00"/>
    <ProductVersion position="tag_entity.xlsx,_system,7">
      <elements id="default">
        <element position="tag_entity.xlsx,_system,10">
          <updated_on>19-Aug-2015</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="tag_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="Y" main_entity="Tag" module="tag" position="tag_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
  </sheet>
  <sheet id="entityDef" position="tag_entity.xlsx,entityDef">
    <Entity name="Tag" position="tag_entity.xlsx,entityDef,1" ref_pattern="${tagCode}" table_name="CNT_TAG">
      <elements id="header">
        <element position="tag_entity.xlsx,entityDef,8">
          <entity_field_id>tagCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <data1/>
          <dataType/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="tag_entity.xlsx,entityDef,9">
          <entity_field_id>content</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <data1/>
          <dataType/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="tag_entity.xlsx,entityDef,10">
          <entity_field_id>type</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <data1/>
          <dataType/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
        <element position="tag_entity.xlsx,entityDef,11">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <data1/>
          <dataType/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <ref_pattern/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="tag_entity.xlsx,status">
    <Status position="tag_entity.xlsx,status,1">
      <elements id="workflow"/>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
