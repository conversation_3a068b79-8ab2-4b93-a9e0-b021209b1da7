<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="qc" position="qc_view.xlsx">
  <sheet id="qcView" position="qc_view.xlsx,qcView">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcView" label="Quality Checklists - All" moduleId="qc" position="qc_view.xlsx,qcView,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcView,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcView,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(Any)</value>
        </element>
        <element position="qc_view.xlsx,qcView,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcView,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcView,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcView,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcView,14">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcView,15">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcView,16">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcView,20">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcView,21">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,22">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcView,23">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,24">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,25">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcView,26">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,27">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,28">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,29">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,30">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,31">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcView,39">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,40">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,41">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,42">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcView,43">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,44">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,45">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,46">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,47">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,48">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,49">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,50">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,51">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,52">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcView,53">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcView,58">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,59">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,60">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,61">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,62">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,63">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,64">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,65">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,66">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,67">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,68">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,69">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,70">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,71">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;fieldId=vendorId&amp;view=searchView&amp;naviModule=master</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,72">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,73">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,74">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,75">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,76">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,77">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,78">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,79">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,81">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,83">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,84">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,85">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,86">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,87">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcView,88">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,89">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,90">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcView,91">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcActiveView " position="qc_view.xlsx,qcActiveView ">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcActiveView" label="Quality Checklists - Active" moduleId="qc" position="qc_view.xlsx,qcActiveView ,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcActiveView ,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,14">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,15">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,16">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcActiveView ,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,34">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,35">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,36">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,37">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,38">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,39">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,40">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,41">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,42">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,43">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,44">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,45">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,46">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,47">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,48">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,49">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,50">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,51">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,52">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,53">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcActiveView ,58">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,59">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,60">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,61">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,62">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,63">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,64">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,65">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,66">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,67">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,68">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,69">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,70">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,71">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,72">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,73">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,74">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,75">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,76">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,77">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,78">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,79">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,81">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,83">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,84">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,85">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,86">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,87">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,88">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,89">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,90">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcActiveView ,91">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcItemView" position="qc_view.xlsx,qcItemView">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcItemView" label="Quality Checklists - Items" moduleId="qc" position="qc_view.xlsx,qcItemView,1" queryId="qcItemList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcItemView,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,9">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,10">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,11">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,12">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,13">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,14">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,15">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,16">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcItemView,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity.qcItem</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcItemView,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcItemView,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcItemView,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcItemView,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcItemView,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcItemView,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcItemView,27">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcItemView,28">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcItemView,29">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcItemView,30">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcItemView,35">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,36">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,37">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,38">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,39">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,40">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,41">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,42">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,43">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,44">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,45">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,46">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,47">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,48">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,49">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,50">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,51">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,52">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,53">
          <id>vendorPo</id>
          <label>Vendor PO No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vpo&amp;view=searchView&amp;refNo=vpoRef&amp;version=vpoVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>qcItem.VPO_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,54">
          <id>shipNo</id>
          <label>Shipment No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>qcItem.VPO_SHIP_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,55">
          <id>remarks</id>
          <label>Shipment Items Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>qcItem.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,56">
          <id>exFactoryDate</id>
          <label>Ex-Factory Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.EX_FACTORY_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,57">
          <id>closingDate</id>
          <label>Closing Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.CLOSING_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,58">
          <id>shipmentDate</id>
          <label>Shipment Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.SHIPMENT_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,59">
          <id>forwarderDate</id>
          <label>Forwarder Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.FORWARDER_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,60">
          <id>arrivalDate</id>
          <label>Arrival Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.ARRIVAL_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,61">
          <id>inDcDate</id>
          <label>In DC Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>qcItem.IN_DC_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,62">
          <id>QcItem</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QCITEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,63">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,64">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,65">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,66">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,67">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,68">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,69">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,70">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,71">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,72">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,73">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,74">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcItemView,75">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,76">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,77">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcItemView,78">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus01View" position="qc_view.xlsx,qcStatus01View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus01View" label="Quality Checklists - Custom Status 01" moduleId="qc" position="qc_view.xlsx,qcStatus01View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus01View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus01'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus01View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,29">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,30">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,31">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus01View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus01View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus02View" position="qc_view.xlsx,qcStatus02View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus02View" label="Quality Checklists - Custom Status 02" moduleId="qc" position="qc_view.xlsx,qcStatus02View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus02View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus02'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus02View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,30">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,31">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus02View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus02View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus03View" position="qc_view.xlsx,qcStatus03View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus03View" label="Quality Checklists - Custom Status 03" moduleId="qc" position="qc_view.xlsx,qcStatus03View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus03View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus03'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus03View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,31">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus03View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus03View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus04View" position="qc_view.xlsx,qcStatus04View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus04View" label="Quality Checklists - Custom Status 04" moduleId="qc" position="qc_view.xlsx,qcStatus04View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus04View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus04'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus04View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus04View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus04View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus05View" position="qc_view.xlsx,qcStatus05View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus05View" label="Quality Checklists - Custom Status 05" moduleId="qc" position="qc_view.xlsx,qcStatus05View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus05View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus05View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus05View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus05View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus06View" position="qc_view.xlsx,qcStatus06View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus06View" label="Quality Checklists - Custom Status 06" moduleId="qc" position="qc_view.xlsx,qcStatus06View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus06View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus06'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus06View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus06View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus06View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus07View" position="qc_view.xlsx,qcStatus07View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus07View" label="Quality Checklists - Custom Status 07" moduleId="qc" position="qc_view.xlsx,qcStatus07View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus07View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus07'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus07View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,34">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus07View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus07View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus08View" position="qc_view.xlsx,qcStatus08View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus08View" label="Quality Checklists - Custom Status 08" moduleId="qc" position="qc_view.xlsx,qcStatus08View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus08View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus08'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus08View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,34">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,35">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus08View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus08View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus09View" position="qc_view.xlsx,qcStatus09View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus09View" label="Quality Checklists - Custom Status 09" moduleId="qc" position="qc_view.xlsx,qcStatus09View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus09View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus09'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus09View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,34">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,35">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,36">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus09View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus09View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="qcStatus10View" position="qc_view.xlsx,qcStatus10View">
    <ViewDefinition advancedSearchId="" description="QC View" id="qcStatus10View" label="Quality Checklists - Custom Status 10" moduleId="qc" position="qc_view.xlsx,qcStatus10View,1" queryId="qcList" searchCriterion="">
      <elements id="options">
        <element position="qc_view.xlsx,qcStatus10View,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,9">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,12">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,13">
          <id>PAGE_SIZE</id>
          <label/>
          <value>50</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,14">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,15">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,16">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus10'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,17">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="qc_view.xlsx,qcStatus10View,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,22">
          <id>searchNewDoc</id>
          <label>New Quality Checklist</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,26">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,27">
          <id>searchDraftStatus</id>
          <label>Draft</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,28">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,29">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,30">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,31">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,32">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,33">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,34">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,35">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,36">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,37">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,39">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,40">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,41">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,42">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,43">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,44">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,45">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,46">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,47">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,48">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,49">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,50">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,51">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=qc&amp;entityName=Qc</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,52">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="qc_view.xlsx,qcStatus10View,57">
          <id>qcNo</id>
          <label>Quality Checklist No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,58">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,59">
          <id>planstartdate</id>
          <label>Planned Start Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.PLAN_START_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,60">
          <id>notesInstructions</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>QC.REMARKS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,61">
          <id>itemno</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;view=searchView&amp;refNo=itemRef&amp;version=itemVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,62">
          <id>itemName</id>
          <label>Item Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,63">
          <id>itemdesc</id>
          <label>Item Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.ITEM_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,64">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,65">
          <id>year</id>
          <label>Year</label>
          <type>CodeList</type>
          <format>bookName=YEAR</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.YEAR</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,66">
          <id>itemType</id>
          <label>Item Type</label>
          <type>CodeList</type>
          <format>bookName=ITEM_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.ITEM_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,67">
          <id>sourcingRecordNo</id>
          <label>Sourcing No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=sourcingRecord&amp;view=searchView&amp;refNo=sourcingRecordRef&amp;version=sourcingRecordVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.SOURCING_RECORD_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,68">
          <id>itemCheckList</id>
          <label>Item Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>itemCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,69">
          <id>shipmentCheckList</id>
          <label>Shipment Checklist</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>shipmentCheckListTempl.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,70">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;view=searchView&amp;refNo=vendorRef&amp;version=vendorVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,71">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.VENDOR_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,72">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,73">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,74">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <format>entityName=Qc</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.TOTAL_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,75">
          <id>notes</id>
          <label>Costs Summary Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,76">
          <id>Qc</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,77">
          <id>Qc</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,79">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.cpo.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,80">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,81">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,82">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>QC.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>QC.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>QC.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>QC.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="qc_view.xlsx,qcStatus10View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>QC.QC_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
