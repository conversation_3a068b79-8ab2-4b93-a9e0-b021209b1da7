<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="component" position="component_view.xlsx">
  <sheet id="componentView" position="component_view.xlsx,componentView">
    <ViewDefinition advancedSearchId="" description="Component View" id="componentView" label="Materials - All" moduleId="component" position="component_view.xlsx,componentView,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>4</value>
        </element>
        <element position="component_view.xlsx,componentView,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="component_view.xlsx,componentView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=materialName</value>
        </element>
        <element position="component_view.xlsx,componentView,14">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Component</value>
        </element>
        <element position="component_view.xlsx,componentView,15">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,thumbnailUrl:imageEstc.thumbnailUrl:string,thumbnail:imageEstc.thumbnail:string,thumbnailMockUpId:imageEstc.thumbnailMockUp:string,thumbnailFileId:imageEstc.thumbnailFileId:string,businessRefNo:businessReference:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentView,19">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,20">
          <id>searchNewDoc</id>
          <label>New Material</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,21">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,22">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,23">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,24">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,25">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,26">
          <id>searchConceptStatus</id>
          <label>Concept</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,27">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,28">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,29">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,30">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,31">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,32">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,33">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,34">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,35">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,36">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,37">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,38">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,39">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,40">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,41">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,42">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentView,43">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,44">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,45">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,46">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,47">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,48">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,49">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,50">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,51">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,52">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentView,53">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentView,58">
          <id>componentNo</id>
          <label>Material No.</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>150px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>componentNo:materialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,59">
          <id>altRefNo</id>
          <label>Alt. Reference No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.ALT_REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>altRefNo:altReferenceNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,60">
          <id>materialName</id>
          <label>Material Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialName:materialName:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,61">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,62">
          <id>attributeSummary</id>
          <label>Attribute Summary</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.ATTRIBUTE_SUMMARY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>attributeSummary:attributeSummary:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,63">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialType:materialType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,64">
          <id>materialSubType</id>
          <label>Material Sub-type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_SUB_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_SUB_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSubType:materialSubType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,65">
          <id>customer</id>
          <label>Customer Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=cust&amp;fieldId=customerId&amp;view=searchView&amp;naviModule=master&amp;refNo=custRef</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CUST.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customer:customer.businessName:string,custRef:customer.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,66">
          <id>customerCode</id>
          <label>Customer ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CUST.CUST_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customerCode:customer.custCode:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,67">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>season:season.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,68">
          <id>vendorName</id>
          <label>Material Supplier</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;fieldId=vendorId&amp;view=searchView&amp;naviModule=master</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>vendorName:supplier.businessName:string,vendorId:supplier.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,69">
          <id>millVendor</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>MILL_VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>millVendor:millVendor.businessName:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,70">
          <id>materialSupplierReferenceNo</id>
          <label>Supplier's Material  No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MATERIAL_SUPPLIER_REFERENCE_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSupplierReferenceNo:supplierMaterialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,71">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>countryOfOrigin:countryOfOrigin.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,72">
          <id>materialFactory</id>
          <label>Factory Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=fact&amp;fieldId=materialFactoryId&amp;view=searchView&amp;naviModule=master</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FACT.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialFactory:materialFactory.businessName:string,materialFactoryId:materialFactory.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,73">
          <id>materialSubContractors</id>
          <label>Sub-Contractor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MATERIAL_SUB_CONTRACTORS_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSubContractors:materialSubContractorsValue:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,74">
          <id>targetShipDate</id>
          <label>Target Ship Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.TARGET_SHIP_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>targetShipDate:targetShipDate:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,75">
          <id>virtualMaterialItemNo</id>
          <label>Material Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;fieldId=virtualMaterialItemId&amp;view=searchView&amp;naviModule=product&amp;refNo=itemRef</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>IT.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>virtualMaterialItemNo:materialItem.refNo:string,itemRef:materialItem.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,76">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>composition:composition:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,77">
          <id>notes</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notes:notes:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,78">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>unitCost:unitCost:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,79">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>currency:currency.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,80">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>uom:uom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,81">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>wastagePercentage:wastagePercentage:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,82">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumption:consumption:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,83">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumptionUOM:consumptionUOM.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,84">
          <id>compSpecificationTempName</id>
          <label>Specification Template</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=factoryAuditTemplate&amp;view=searchView&amp;refNo=compSpecificationTempRef&amp;version=compSpecificationTempVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.COMP_SPECIFICATION_TEMP_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>compSpecificationTempName:compSpecificationTempName:string,compSpecificationTempRef:compSpecificationTempId.refNo:string,compSpecificationTempVer:compSpecificationTempId.version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,85">
          <id>finish</id>
          <label>Finish</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>finish:finish:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,86">
          <id>fabricDensity</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricDensity:fabricDensity:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,87">
          <id>fabricYarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricYarnCount:fabricYarnCount:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,88">
          <id>fabricStructure</id>
          <label>Structure</label>
          <type>CodeList</type>
          <format>bookName=FABRIC_STRUCTURE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_STRUCTURE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricStructure:fabricStructure.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,89">
          <id>fabricConstruction</id>
          <label>Fabric Construction</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CONSTRUCTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricConstruction:fabricConstruction:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,90">
          <id>fabricFullWidth</id>
          <label>Fabric Full Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_FULL_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricFullWidth:fabricFullWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,91">
          <id>fabricCutWidth</id>
          <label>Fabric Cut Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CUT_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricCutWidth:fabricCutWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,92">
          <id>fabricWidthUom</id>
          <label>Width UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WIDTH_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WIDTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWidthUom:fabricWidthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,93">
          <id>fabricWeightBeforeWash</id>
          <label>Fabric Weight (before wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_BEFORE_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightBeforeWash:fabricWeightBeforeWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,94">
          <id>fabricWeightAfterWash</id>
          <label>Fabric Weight (after wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_AFTER_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightAfterWash:fabricWeightAfterWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,95">
          <id>fabricWeigthUom</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeigthUom:fabricWeigthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,96">
          <id>fabShrinkageAfterGarWash</id>
          <label>Fabric Shrinkage after Garment Washing (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FAB_SHRINKAGE_AFTER_GAR_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabShrinkageAfterGarWash:fabShrinkageAfterGarWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,97">
          <id>mcq</id>
          <label>MCQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MCQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>mcq:mcq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,98">
          <id>moq</id>
          <label>MOQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MOQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>moq:moq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,99">
          <id>leadtimeWithGreige</id>
          <label>Leadtime with Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITH_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithGreige:leadtimeWithGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,100">
          <id>leadtimeWithoutGreige</id>
          <label>Leadtime without Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITHOUT_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithoutGreige:leadtimeWithoutGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,101">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentView,102">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentView,103">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,104">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,105">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,106">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,107">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,108">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,109">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,110">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,111">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,112">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:docRef:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,113">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,114">
          <id>createUserName</id>
          <label>Created By</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.CREATE.USER.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,115">
          <id>createdOn</id>
          <label>Created On</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.CREATED.ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentView,116">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>cpmDocId:cpmDoc.cpmId:string,refDocRefNo:cpmDoc.refDocRefNo:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentActiveView " position="component_view.xlsx,componentActiveView ">
    <ViewDefinition advancedSearchId="" description="Component View" id="componentActiveView" label="Materials - Active" moduleId="component" position="component_view.xlsx,componentActiveView ,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentActiveView ,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>4</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=materialName</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,14">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,15">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,16">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Component</value>
        </element>
        <element position="component_view.xlsx,componentActiveView ,17">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,thumbnail:imageEstc.thumbnail:string,thumbnailUrl:imageEstc.thumbnailUrl:string,thumbnailMockUpId:imageEstc.thumbnailMockUp:string,thumbnailFileId:imageEstc.thumbnailFileId:string,businessRefNo:businessReference:string,isLatest:isLatest:boolean,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentActiveView ,21">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,22">
          <id>searchNewDoc</id>
          <label>New Material</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,23">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,24">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,25">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,26">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,27">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,28">
          <id>searchConceptStatus</id>
          <label>Concept</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,29">
          <id>searchOfficialStatus</id>
          <label>Official</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,30">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,31">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,32">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,33">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,34">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,35">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,36">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,37">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,38">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,39">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,40">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,41">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,42">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,43">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,44">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,45">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,46">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,47">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,48">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,49">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,50">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,51">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,52">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,53">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentActiveView ,54">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentActiveView ,59">
          <id>componentNo</id>
          <label>Material No.</label>
          <type>RefNo</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>150px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>componentNo:materialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,60">
          <id>altRefNo</id>
          <label>Alt. Reference No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.ALT_REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>altRefNo:altReferenceNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,61">
          <id>materialName</id>
          <label>Material Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialName:materialName:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,62">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,63">
          <id>attributeSummary</id>
          <label>Attribute Summary</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.ATTRIBUTE_SUMMARY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>attributeSummary:attributeSummary:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,64">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialType:materialType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,65">
          <id>materialSubType</id>
          <label>Material Sub-type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_SUB_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_SUB_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSubType:materialSubType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,66">
          <id>customer</id>
          <label>Customer Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=cust&amp;fieldId=customerId&amp;view=searchView&amp;naviModule=master&amp;refNo=custRef</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CUST.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customer:customer.businessName:string,custRef:customer.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,67">
          <id>customerCode</id>
          <label>Customer ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CUST.CUST_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customerCode:customer.custCode:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,68">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>season:season.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,69">
          <id>vendorName</id>
          <label>Material Supplier</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vendor&amp;fieldId=vendorId&amp;view=searchView&amp;naviModule=master</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>vendorName:supplier.businessName:string,vendorId:supplier.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,70">
          <id>millVendor</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>MILL_VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>millVendor:millVendor.businessName:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,71">
          <id>materialSupplierReferenceNo</id>
          <label>Supplier's Material  No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MATERIAL_SUPPLIER_REFERENCE_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSupplierReferenceNo:supplierMaterialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,72">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>countryOfOrigin:countryOfOrigin.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,73">
          <id>materialFactory</id>
          <label>Factory Name</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=fact&amp;fieldId=materialFactoryId&amp;view=searchView&amp;naviModule=master</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>FACT.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialFactory:materialFactory.businessName:string,materialFactoryId:materialFactory.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,74">
          <id>materialSubContractors</id>
          <label>Sub-Contractor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MATERIAL_SUB_CONTRACTORS_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSubContractors:materialSubContractorsValue:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,75">
          <id>targetShipDate</id>
          <label>Target Ship Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.TARGET_SHIP_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>targetShipDate:targetShipDate:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,76">
          <id>virtualMaterialItemNo</id>
          <label>Material Item No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;fieldId=virtualMaterialItemId&amp;view=searchView&amp;naviModule=product&amp;refNo=itemRef</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>IT.ITEM_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>virtualMaterialItemNo:materialItem.refNo:string,itemRef:materialItem.refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,77">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>composition:composition:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,78">
          <id>notes</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notes:notes:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,79">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>unitCost:unitCost:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,80">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>currency:currency.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,81">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>uom:uom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,82">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>wastagePercentage:wastagePercentage:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,83">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumption:consumption:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,84">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumptionUOM:consumptionUOM.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,85">
          <id>compSpecificationTempName</id>
          <label>Specification Template</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=factoryAuditTemplate&amp;view=searchView&amp;refNo=compSpecificationTempRef&amp;version=compSpecificationTempVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.COMP_SPECIFICATION_TEMP_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>compSpecificationTempName:compSpecificationTempName:string,compSpecificationTempRef:compSpecificationTempId.refNo:string,compSpecificationTempVer:compSpecificationTempId.version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,86">
          <id>finish</id>
          <label>Finish</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>finish:finish:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,87">
          <id>fabricDensity</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricDensity:fabricDensity:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,88">
          <id>fabricYarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricYarnCount:fabricYarnCount:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,89">
          <id>fabricStructure</id>
          <label>Structure</label>
          <type>CodeList</type>
          <format>bookName=FABRIC_STRUCTURE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_STRUCTURE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricStructure:fabricStructure.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,90">
          <id>fabricConstruction</id>
          <label>Fabric Construction</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CONSTRUCTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricConstruction:fabricConstruction:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,91">
          <id>fabricFullWidth</id>
          <label>Fabric Full Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_FULL_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricFullWidth:fabricFullWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,92">
          <id>fabricCutWidth</id>
          <label>Fabric Cut Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CUT_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricCutWidth:fabricCutWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,93">
          <id>fabricWidthUom</id>
          <label>Width UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WIDTH_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WIDTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWidthUom:fabricWidthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,94">
          <id>fabricWeightBeforeWash</id>
          <label>Fabric Weight (before wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_BEFORE_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightBeforeWash:fabricWeightBeforeWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,95">
          <id>fabricWeightAfterWash</id>
          <label>Fabric Weight (after wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_AFTER_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightAfterWash:fabricWeightAfterWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,96">
          <id>fabricWeigthUom</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeigthUom:fabricWeigthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,97">
          <id>fabShrinkageAfterGarWash</id>
          <label>Fabric Shrinkage after Garment Washing (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FAB_SHRINKAGE_AFTER_GAR_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabShrinkageAfterGarWash:fabShrinkageAfterGarWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,98">
          <id>mcq</id>
          <label>MCQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MCQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>mcq:mcq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,99">
          <id>moq</id>
          <label>MOQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MOQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>moq:moq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,100">
          <id>leadtimeWithGreige</id>
          <label>Leadtime with Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITH_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithGreige:leadtimeWithGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,101">
          <id>leadtimeWithoutGreige</id>
          <label>Leadtime without Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITHOUT_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithoutGreige:leadtimeWithoutGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,102">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,103">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentActiveView ,104">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,105">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,106">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,107">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,108">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,109">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,110">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,111">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,112">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,113">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:docRef:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,114">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,115">
          <id>createUserName</id>
          <label>Created By</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.CREATE.USER.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createdBy:string</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,116">
          <id>createdOn</id>
          <label>Created On</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.CREATED.ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,componentActiveView ,117">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>cpmDocId:cpmDoc.cpmId:string,refDocRefNo:cpmDoc.refDocRefNo:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popComponentView" position="component_view.xlsx,popComponentView">
    <ViewDefinition advancedSearchId="" description="Popup Component View" id="popComponentView" label="Materials Lookup" moduleId="component" position="component_view.xlsx,popComponentView,1" queryId="listPopComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,popComponentView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,popComponentView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,popComponentView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>DETAIL</value>
        </element>
        <element position="component_view.xlsx,popComponentView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,popComponentView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'') AND (editingStatus =|STRING ''confirmed'')</value>
        </element>
        <element position="component_view.xlsx,popComponentView,13">
          <id>POPUP_SHOW_THUMBNAIL</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="component_view.xlsx,popComponentView,14">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>Thumbnail_tooltiptext=materialName</value>
        </element>
        <element position="component_view.xlsx,popComponentView,15">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string,editingStatus=confirmed=string</value>
        </element>
        <element position="component_view.xlsx,popComponentView,16">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Component</value>
        </element>
        <element position="component_view.xlsx,popComponentView,17">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,refNo:refNo:string,thumbnail:image.original.filePath:string,thumbnailUrl:image.original.thumbnailUrl:string</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="component_view.xlsx,popComponentView,25">
          <id>componentNo</id>
          <label>Material No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>componentNo:materialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,26">
          <id>altRefNo</id>
          <label>Alt. Reference No</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.ALT_REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>altRefNo:altReferenceNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,27">
          <id>materialName</id>
          <label>Material Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialName:materialName:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,28">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialType:materialType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,29">
          <id>materialSubType</id>
          <label>Material sub-type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_SUB_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_SUB_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSubType:materialSubType.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,30">
          <id>season</id>
          <label>Season</label>
          <type>CodeList</type>
          <format>bookName=SEASON</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.SEASON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>season:season.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,31">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,32">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>composition:composition:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,33">
          <id>notes</id>
          <label>Notes / Instructions</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.NOTES</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>notes:notes:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,34">
          <id>customer</id>
          <label>Customer Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CUST.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customer:customerName.businessName:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,35">
          <id>customerCode</id>
          <label>Customer ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CUST.CUST_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customerCode:customerName.custCode:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,36">
          <id>customerReferenceNo</id>
          <label>Customer's Material No</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CUSTOMER_REFERENCE_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>customerReferenceNo:customerMaterialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,37">
          <id>vendorName</id>
          <label>Supplier</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>vendorName:supplier.businessName:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,38">
          <id>materialSupplierReferenceNo</id>
          <label>Supplier's Material No</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MATERIAL_SUPPLIER_REFERENCE_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>materialSupplierReferenceNo:supplierMaterialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,39">
          <id>millVendor</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>MILL_VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>millVendor:millVendor.businessName:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,40">
          <id>millReferenceNo</id>
          <label>Mill's Material No</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL_REFERENCE_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>millReferenceNo:millMaterialNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,41">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>countryOfOrigin:countryOfOrigin.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,42">
          <id>targetShipDate</id>
          <label>Target Ship Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.TARGET_SHIP_DATE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>targetShipDate:targetShipDate:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,43">
          <id>virtualMaterialItemId</id>
          <label>Material Item ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VIRTUAL_MATERIAL_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>virtualMaterialItemId:virtualMaterialItemId:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,44">
          <id>virtualMaterialItemNo</id>
          <label>Material Item No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VIRTUAL_MATERIAL_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>virtualMaterialItemNo:virtualMaterialItemNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,45">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CURRENCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>currency:currency.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,46">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>unitCost:unitCost:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,47">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>uom:uom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,48">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>wastagePercentage:wastagePercentage:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,49">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumption:consumption:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,50">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>consumptionUOM:consumptionUOM.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,51">
          <id>attributeSummary</id>
          <label>Attribute Summary</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.ATTRIBUTE_SUMMARY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>attributeSummary:attributeSummary:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,52">
          <id>compSpecificationTempName</id>
          <label>Specification Template</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=factoryAuditTemplate&amp;view=searchView&amp;refNo=compSpecificationTempRef&amp;version=compSpecificationTempVer</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.COMP_SPECIFICATION_TEMP_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>compSpecificationTempName:compSpecificationTempName:string,compSpecificationTempRef:compSpecificationTempId.refNo:string,compSpecificationTempVer:compSpecificationTempId.version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,53">
          <id>finish</id>
          <label>Finish</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>finish:finish:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,54">
          <id>fabricDensity</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricDensity:fabricDensity:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,55">
          <id>fabricYarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricYarnCount:fabricYarnCount:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,56">
          <id>fabricStructure</id>
          <label>Structure</label>
          <type>CodeList</type>
          <format>bookName=FABRIC_STRUCTURE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_STRUCTURE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricStructure:fabricStructure.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,57">
          <id>fabricConstruction</id>
          <label>Fabric Construction</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CONSTRUCTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricConstruction:fabricConstruction:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,58">
          <id>fabricFullWidth</id>
          <label>Fabric Full Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_FULL_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricFullWidth:fabricFullWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,59">
          <id>fabricCutWidth</id>
          <label>Fabric Cut Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_CUT_WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricCutWidth:fabricCutWidth:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,60">
          <id>fabricWidthUom</id>
          <label>Width UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WIDTH_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WIDTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWidthUom:fabricWidthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,61">
          <id>fabricWeightBeforeWash</id>
          <label>Fabric Weight (before wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_BEFORE_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightBeforeWash:fabricWeightBeforeWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,62">
          <id>fabricWeightAfterWash</id>
          <label>Fabric Weight (after wash)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGHT_AFTER_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeightAfterWash:fabricWeightAfterWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,63">
          <id>fabricWeigthUom</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=MATERIAL_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FABRIC_WEIGTH_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabricWeigthUom:fabricWeigthUom.code:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,64">
          <id>fabShrinkageAfterGarWash</id>
          <label>Fabric Shrinkage after Garment Washing (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FAB_SHRINKAGE_AFTER_GAR_WASH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>fabShrinkageAfterGarWash:fabShrinkageAfterGarWash:decimal</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,65">
          <id>mcq</id>
          <label>MCQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MCQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>mcq:mcq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,66">
          <id>moq</id>
          <label>MOQ</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MOQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>moq:moq:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,67">
          <id>leadtimeWithGreige</id>
          <label>Leadtime with Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITH_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithGreige:leadtimeWithGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,68">
          <id>leadtimeWithoutGreige</id>
          <label>Leadtime without Greige (days)</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.LEADTIME_WITHOUT_GREIGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>leadtimeWithoutGreige:leadtimeWithoutGreige:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,69">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>Component</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,70">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,71">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,72">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,73">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,74">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,75">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,76">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,77">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,78">
          <id>createUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createUserName:createUserName:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,79">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,80">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,81">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,82">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="component_view.xlsx,popComponentView,83">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>Component</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus01View" position="component_view.xlsx,componentForCustomStatus01View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 01 View" id="componentForCustomStatus01View" label="Components - Custom Status 01" moduleId="component" position="component_view.xlsx,componentForCustomStatus01View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus01View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus01'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus01View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,23">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,24">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus01View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus01View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus02View" position="component_view.xlsx,componentForCustomStatus02View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 02 View" id="componentForCustomStatus02View" label="Components - Custom Status 02" moduleId="component" position="component_view.xlsx,componentForCustomStatus02View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus02View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus02'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus02View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,24">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus02View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus02View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus03View" position="component_view.xlsx,componentForCustomStatus03View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 03 View" id="componentForCustomStatus03View" label="Components - Custom Status 03" moduleId="component" position="component_view.xlsx,componentForCustomStatus03View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus03View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus03'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus03View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,25">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus03View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus03View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus04View" position="component_view.xlsx,componentForCustomStatus04View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 04 View" id="componentForCustomStatus04View" label="Components - Custom Status 04" moduleId="component" position="component_view.xlsx,componentForCustomStatus04View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus04View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus04'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus04View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,26">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus04View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus04View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus05View" position="component_view.xlsx,componentForCustomStatus05View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 05 View" id="componentForCustomStatus05View" label="Components - Custom Status 05" moduleId="component" position="component_view.xlsx,componentForCustomStatus05View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus05View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus05'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus05View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,27">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus05View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus05View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus06View" position="component_view.xlsx,componentForCustomStatus06View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 06 View" id="componentForCustomStatus06View" label="Components - Custom Status 06" moduleId="component" position="component_view.xlsx,componentForCustomStatus06View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus06View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus06'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus06View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,28">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus06View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus06View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus07View" position="component_view.xlsx,componentForCustomStatus07View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 07 View" id="componentForCustomStatus07View" label="Components - Custom Status 07" moduleId="component" position="component_view.xlsx,componentForCustomStatus07View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus07View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus07'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus07View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,29">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus07View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus07View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus08View" position="component_view.xlsx,componentForCustomStatus08View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 08 View" id="componentForCustomStatus08View" label="Components - Custom Status 08" moduleId="component" position="component_view.xlsx,componentForCustomStatus08View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus08View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus08'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus08View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,30">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus08View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus08View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus09View" position="component_view.xlsx,componentForCustomStatus09View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 09 View" id="componentForCustomStatus09View" label="Components - Custom Status 09" moduleId="component" position="component_view.xlsx,componentForCustomStatus09View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus09View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus09'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus09View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,30">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,31">
          <id>searchMarkAsCustomStatus10</id>
          <label>Custom Status 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus09View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus09View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentForCustomStatus10View" position="component_view.xlsx,componentForCustomStatus10View">
    <ViewDefinition advancedSearchId="" description="Component For Custom Status 10 View" id="componentForCustomStatus10View" label="Components - Custom Status 10" moduleId="component" position="component_view.xlsx,componentForCustomStatus10View,1" queryId="listComponents" searchCriterion="">
      <elements id="options">
        <element position="component_view.xlsx,componentForCustomStatus10View,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(status =|STRING ''customStatus10'') AND (docStatus =|STRING ''active'')</value>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,13">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="component_view.xlsx,componentForCustomStatus10View,17">
          <id>create</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,18">
          <id>searchNewDoc</id>
          <label>New Component</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>create</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,22">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,23">
          <id>searchMarkAsCustomStatus01</id>
          <label>Custom Status 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,24">
          <id>searchMarkAsCustomStatus02</id>
          <label>Custom Status 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,25">
          <id>searchMarkAsCustomStatus03</id>
          <label>Custom Status 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,26">
          <id>searchMarkAsCustomStatus04</id>
          <label>Custom Status 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,27">
          <id>searchMarkAsCustomStatus05</id>
          <label>Custom Status 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,28">
          <id>searchMarkAsCustomStatus06</id>
          <label>Custom Status 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,29">
          <id>searchMarkAsCustomStatus07</id>
          <label>Custom Status 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,30">
          <id>searchMarkAsCustomStatus08</id>
          <label>Custom Status 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,31">
          <id>searchMarkAsCustomStatus09</id>
          <label>Custom Status 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,32">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,33">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,34">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,35">
          <id>moreGroup</id>
          <label>More</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,36">
          <id>searchCustomAction01</id>
          <label>Custom Action 1</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,37">
          <id>searchCustomAction02</id>
          <label>Custom Action 2</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,38">
          <id>searchCustomAction03</id>
          <label>Custom Action 3</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,39">
          <id>searchCustomAction04</id>
          <label>Custom Action 4</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,40">
          <id>searchCustomAction05</id>
          <label>Custom Action 5</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,41">
          <id>searchCustomAction06</id>
          <label>Custom Action 6</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,42">
          <id>searchCustomAction07</id>
          <label>Custom Action 7</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,43">
          <id>searchCustomAction08</id>
          <label>Custom Action 8</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,44">
          <id>searchCustomAction09</id>
          <label>Custom Action 9</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,45">
          <id>searchCustomAction10</id>
          <label>Custom Action 10</label>
          <type>button</type>
          <actionParams>moduleId=component&amp;entityName=Component</actionParams>
          <buttonGroup>moreGroup</buttonGroup>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,46">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="component_view.xlsx,componentForCustomStatus10View,51">
          <id>componentNo</id>
          <label>Component No.</label>
          <type>Hyperlink</type>
          <format/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=component&amp;fieldId=id&amp;view=searchView&amp;naviModule=products</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,53">
          <id>shortDesc</id>
          <label>Short Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.SHORT_DESC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,54">
          <id>componentType</id>
          <label>Component Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,55">
          <id>materialType</id>
          <label>Material Type</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_MATERIAL_TYPE</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.MATERIAL_TYPE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,56">
          <id>vendorName</id>
          <label>Vendor</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>VENDOR.BUSINESS_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,57">
          <id>mill</id>
          <label>Mill</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.MILL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,58">
          <id>countryOfOrigin</id>
          <label>Country of Origin</label>
          <type>CodeList</type>
          <format>bookName=COUNTRY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COUNTRY_OF_ORIGIN</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,59">
          <id>composition</id>
          <label>Composition</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPOSITION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,60">
          <id>dyeMethod</id>
          <label>Dye Method</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_DYE_METHOD</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DYE_METHOD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,61">
          <id>finishing</id>
          <label>Finishing</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_FINISHING</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.FINISHING</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,62">
          <id>yarnCount</id>
          <label>Yarn Count</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.YARN_COUNT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,63">
          <id>density</id>
          <label>Density</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.DENSITY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,64">
          <id>width</id>
          <label>Width</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WIDTH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,65">
          <id>weight</id>
          <label>Weight</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,66">
          <id>weightUOM</id>
          <label>Weight UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_WEIGHT_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.WEIGHT_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,67">
          <id>isHazardous</id>
          <label>Hazardous</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_HAZARDOUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,68">
          <id>isWithPrint</id>
          <label>With Print</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_PRINT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,69">
          <id>isWithEmbellishment</id>
          <label>With Embellishment</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.IS_WITH_EMBELLISHMENT</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,70">
          <id>unitCost</id>
          <label>Unit Cost</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UNIT_COST</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,71">
          <id>currency</id>
          <label>Currency</label>
          <type>CodeList</type>
          <format>bookName=CURRENCY</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CCY</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,72">
          <id>uom</id>
          <label>UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_COST_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,73">
          <id>wastagePercentage</id>
          <label>Wastage (%)</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.WASTAGE_PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,74">
          <id>consumption</id>
          <label>Consumption</label>
          <type>Decimal</type>
          <format>entityName=Component</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,75">
          <id>consumptionUOM</id>
          <label>Consumption UOM</label>
          <type>CodeList</type>
          <format>bookName=COMPONENT_CONSUMPTION_UOM</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.CONSUMPTION_UOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,76">
          <id>Component</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,77">
          <id>Component</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,78">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,79">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,80">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.component.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>100px</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,81">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,82">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,83">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,84">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,85">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,86">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>COMPONENT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,87">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,88">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>COMPONENT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="component_view.xlsx,componentForCustomStatus10View,89">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>COMPONENT.COMPONENT_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="componentCompositionDialogView" position="component_view.xlsx,componentCompositionDialogView">
    <ViewDefinition advancedSearchId="" description="Component Composition Dialog" id="componentCompositionDialogView" label="Component Composition Dialog" moduleId="item" position="component_view.xlsx,componentCompositionDialogView,1" queryId="componentCompositionDialog" searchCriterion="">
      <elements id="options"/>
      <elements id="actions"/>
      <elements id="columns">
        <element position="component_view.xlsx,componentCompositionDialogView,16">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Seq</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>IT.SEQ_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentCompositionDialogView,17">
          <id>component</id>
          <label>Component</label>
          <type>Dropdown</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>IT.COMPOSITION_COMPONENT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="component_view.xlsx,componentCompositionDialogView,18">
          <id>percentage</id>
          <label>Percentage(%)</label>
          <type>Decimal</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>IT.PERCENTAGE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
