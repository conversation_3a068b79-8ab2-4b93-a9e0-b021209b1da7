<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="shipmentAdvice" position="shipmentAdvice_entity.xlsx">
  <sheet id="_system" position="shipmentAdvice_entity.xlsx,_system">
    <ProjectInfo client="Base" position="shipmentAdvice_entity.xlsx,_system,1" project="SCMS" release_no="5.0"/>
    <ProductVersion position="shipmentAdvice_entity.xlsx,_system,7">
      <elements id="default">
        <element position="shipmentAdvice_entity.xlsx,_system,10">
          <updated_on>05-十二月-2012</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="shipmentAdvice_entity.xlsx,generalInfo">
    <GeneralInfo is_for_external="Y" is_system_entity="" main_entity="ShipmentAdvice" module="shipmentAdvice" position="shipmentAdvice_entity.xlsx,generalInfo,1" revision_tracking="ALL" version="1" version_tracking="ALL"/>
    <CustomField position="shipmentAdvice_entity.xlsx,generalInfo,7">
      <elements id="default">
        <element position="shipmentAdvice_entity.xlsx,generalInfo,10">
          <custom_field_type>Text</custom_field_type>
          <count>10</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,11">
          <custom_field_type>MemoText</custom_field_type>
          <count>10</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,12">
          <custom_field_type>Codelist</custom_field_type>
          <count>20</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,13">
          <custom_field_type>Number</custom_field_type>
          <count>20</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,14">
          <custom_field_type>Decimal</custom_field_type>
          <count>20</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,15">
          <custom_field_type>Date</custom_field_type>
          <count>10</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,16">
          <custom_field_type>Hcl</custom_field_type>
          <count>5</count>
        </element>
        <element position="shipmentAdvice_entity.xlsx,generalInfo,17">
          <custom_field_type>Checkbox</custom_field_type>
          <count>10</count>
        </element>
      </elements>
    </CustomField>
  </sheet>
  <sheet id="entityDef" position="shipmentAdvice_entity.xlsx,entityDef">
    <Entity name="ShipmentAdvice" position="shipmentAdvice_entity.xlsx,entityDef,1" ref_pattern="${shipmentAdviceNo}" report_table_name="SHIPMENT_ADVICE" table_name="CNT_SHIPMENT_ADVICE">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,8">
          <entity_field_id>shipmentAdviceNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_SHIPMENT_ADVICE_NO","system.pattern.ShipmentAdviceNo", "SA#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ADVICE_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,9">
          <entity_field_id>shortDesc</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHORT_DESC</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,10">
          <entity_field_id>shipmentAdviceDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ADVICE_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,11">
          <entity_field_id>shipmentOrderNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ORDER_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,12">
          <entity_field_id>vendorIncoterm</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>INCOTERM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_INCOTERM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,13">
          <entity_field_id>shipmentMethod</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SHIPMENT_METHOD</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_METHOD</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,14">
          <entity_field_id>isfNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ISF_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,15">
          <entity_field_id>merchandiseHierarchy</entity_field_id>
          <entity_field_type>alias</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>//--DEPRECATED IN CNT-13429</description>
          <generator/>
          <dataType/>
          <data1>hierarchy</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,16">
          <entity_field_id>hierarchy</entity_field_id>
          <entity_field_type>hcl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MERCHANDISE_HIERARCHY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,17">
          <entity_field_id>deliveryDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DELIVERY_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,18">
          <entity_field_id>arrivalDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ARRIVAL_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,19">
          <entity_field_id>closingDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CLOSING_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,20">
          <entity_field_id>forwarderDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FORWARDER_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,21">
          <entity_field_id>dispatchDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DISPATCH_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,22">
          <entity_field_id>inDCDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IN_DC_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,23">
          <entity_field_id>carrier</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CARRIER</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARRIER</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,24">
          <entity_field_id>scacCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>SCAC_CODE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>SCAC_CODE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,25">
          <entity_field_id>bolNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>BOL_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,26">
          <entity_field_id>vesselOrFlightNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VESSEL_OR_FLIGHT_NO</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,27">
          <entity_field_id>voyageOrMAWB</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VOYAGE_OR_MAWB</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,28">
          <entity_field_id>countryOfShipment</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>COUNTRY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>COUNTRY_OF_SHIPMENT</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,29">
          <entity_field_id>portOfLoading</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>PORT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>PORT_OF_LOADING</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,30">
          <entity_field_id>countryOfDestination</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DESTINATION_COUNTRY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>COUNTRY_OF_DESTINATION</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,31">
          <entity_field_id>portOfDischarge</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>PORT</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>PORT_OF_DISCHARGE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,32">
          <entity_field_id>finalDestination</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DESTINATION</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>FINAL_DESTINATION</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,33">
          <entity_field_id>totalContainersQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_CONTAINERS_QTY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,34">
          <entity_field_id>currency</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>CURRENCY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>CURRENCY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,35">
          <entity_field_id>totalCostOfContainers</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_COST_OF_CONTAINERS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,36">
          <entity_field_id>totalShipmentItemsQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_SHIPMENT_ITEMS_QTY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,37">
          <entity_field_id>totalQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_QTY</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,38">
          <entity_field_id>instructions</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INSTRUCTIONS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,39">
          <entity_field_id>commitmentNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>COMMITMENT_NO</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,40">
          <entity_field_id>estimatedExFactoryDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_EX_FACTORY_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,41">
          <entity_field_id>estimatedShipmentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_SHIPMENT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,42">
          <entity_field_id>estimatedClosingDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_CLOSING_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,43">
          <entity_field_id>estimatedForwarderDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_FORWARDER_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,44">
          <entity_field_id>estimatedArrivalDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_ARRIVAL_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,45">
          <entity_field_id>estimatedInDCDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ESTIMATED_IN_DC_DATE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,46">
          <entity_field_id>movementType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>MOVEMENT_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>MOVEMENT_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,47">
          <entity_field_id>delayReasonType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DELAY_REASON_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DELAY_REASON_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,48">
          <entity_field_id>delayReason</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>DELAY_REASON</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DELAY_REASON</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,49">
          <entity_field_id>delayDays</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DELAY_DAYS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,50">
          <entity_field_id>totalCartonQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_CARTON_QTY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,51">
          <entity_field_id>isFromMultipleVendors</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_FROM_MULTIPLE_VENDORS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,52">
          <entity_field_id>customer</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Cust.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_SYS_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,53">
          <entity_field_id>customerName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,54">
          <entity_field_id>customerCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_CODE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,55">
          <entity_field_id>vendor</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vendor.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_SYS_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,56">
          <entity_field_id>vendorName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,57">
          <entity_field_id>vendorCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_CODE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,58">
          <entity_field_id>forwarder</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Forwarder.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>forwarderCode, companyName</transitive_fields>
          <report_column_name>FORWARDER_SYS_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,59">
          <entity_field_id>companyName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>COMPANY_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,60">
          <entity_field_id>forwarderCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FORWARDER_CODE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,61">
          <entity_field_id>destinationForwarder</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Forwarder.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields>forwarderCode, companyName</transitive_fields>
          <report_column_name>DESTINATION_FORWARDER</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,62">
          <entity_field_id>destinationCompanyName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DESTINATION_COMPANY_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,63">
          <entity_field_id>destinationForwarderCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>DESTINATION_FORWARDER_CODE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,64">
          <entity_field_id>saShipmentStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>VpoShipmentStatus</dataType>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SA_SHIPMENT_STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,65">
          <entity_field_id>orderType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <data1>ORDER_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>ORDER_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,66">
          <entity_field_id>vesselName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VESSEL_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="shipmentAdvice_entity.xlsx,entityDef,70">
          <entity_field_id>shipmentAdviceContainers</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceContainer.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,71">
          <entity_field_id>shipmentAdviceSummary</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceSummary.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,72">
          <entity_field_id>shipmentAdviceShipmentItems</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceShipmentItem.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,73">
          <entity_field_id>shipmentAdviceAddresses</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceAddress.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,74">
          <entity_field_id>shipmentAdviceContacts</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceContact.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,75">
          <entity_field_id>shipmentAdviceAttachments</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceAttachment.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,76">
          <entity_field_id>shipmentAdviceShareFiles</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceShareFile.shipmentAdviceId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name>SHIPMENT_ADVICE_SHARE_FILES</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,77">
          <entity_field_id>shipmentAdviceOther1</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceOther1.shipmentAdviceId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name>1</report_column_name>
          <tracking_level/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,78">
          <entity_field_id>shipmentAdviceOther2</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceOther2.shipmentAdviceId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name>1</report_column_name>
          <tracking_level/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,79">
          <entity_field_id>shipmentAdviceOther3</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceOther3.shipmentAdviceId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType/>
          <data1/>
          <report_column_name>1</report_column_name>
          <tracking_level/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,80">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceContainer" position="shipmentAdvice_entity.xlsx,entityDef,83" ref_pattern="${containerNo}" report_table_name="SHIPMENT_ADVICE_CONTAINER" table_name="CNT_SHIPMENT_ADVICE_CONTAINER">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,90">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,91">
          <entity_field_id>containerNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CONTAINER_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,92">
          <entity_field_id>containerType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>CONTAINER_TYPE</data1>
          <data2/>
          <report_column_name>CONTAINER_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,93">
          <entity_field_id>containerSize</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>CONTAINER_SIZE</data1>
          <data2/>
          <report_column_name>CONTAINER_SIZE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,94">
          <entity_field_id>loadingMethod</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>LOADING_METHOD</data1>
          <data2/>
          <report_column_name>LOADING_METHOD</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,95">
          <entity_field_id>sealNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SEAL_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,96">
          <entity_field_id>ssccNo</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SSCC_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,97">
          <entity_field_id>totalItemQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_ITEM_QTY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,98">
          <entity_field_id>totalNoOfCartons</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_NO_OF_CARTONS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,99">
          <entity_field_id>totalCartonCBM</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_CARTON_CBM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,100">
          <entity_field_id>totalCartonCFT</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_CARTON_CFT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,101">
          <entity_field_id>totalCartonGrossWeight</entity_field_id>
          <entity_field_type>decimal-weight</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_CARTON_GROSS_WEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,102">
          <entity_field_id>onTimeStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ON_TIME_STATUS</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,103">
          <entity_field_id>trackedReferenceId</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TRACKED_REFERENCE_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,104">
          <entity_field_id>isTracking</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_TRACKING</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection"/>
    </Entity>
    <Entity name="ShipmentAdviceSummary" position="shipmentAdvice_entity.xlsx,entityDef,109" ref_pattern="${containerSize}" report_table_name="SHIPMENT_ADVICE_SUMMARY" table_name="CNT_SHIPMENT_ADVICE_SUMMARY">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,116">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,117">
          <entity_field_id>containerSize</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>CONTAINER_SIZE</data1>
          <data2/>
          <report_column_name>CONTAINER_SIZE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,118">
          <entity_field_id>totalQuantity</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOTAL_QUANTITY</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,119">
          <entity_field_id>currency</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>CURRENCY</data1>
          <data2/>
          <report_column_name>CURRENCY</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,120">
          <entity_field_id>unitCost</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>UNIT_COST</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,121">
          <entity_field_id>additionalCost</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDITIONAL_COST</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,122">
          <entity_field_id>calculatedCost</entity_field_id>
          <entity_field_type>decimal-money</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>CALCULATED_COST</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
      <elements id="collection"/>
    </Entity>
    <Entity name="ShipmentAdviceShipmentItem" position="shipmentAdvice_entity.xlsx,entityDef,127" ref_pattern="${vendorPOShipmentItem.vpoShipId.shipmentNo}/${vendorPOShipmentItem.vpoItemId.itemNo}-${vendorPOShipmentItem.vpoItemId.lotNo}" report_table_name="SHIPMENT_ADVICE_SHIPMENT_ITEM" table_name="CNT_SHIPMENT_ADVICE_ITEM">
      <elements id="reference">
        <element position="shipmentAdvice_entity.xlsx,entityDef,134">
          <entity_field_id>vendorPOShipmentItem</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>VpoShipDtl.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_SHIPMENT_ITEM_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,135">
          <entity_field_id>vendorPOShipmentQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShipmentItem.qty</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_SHIPMENT_QTY</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,136">
          <entity_field_id>packType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShipmentItem.packType</snapshot_field>
          <description/>
          <dataType>codelist</dataType>
          <data1>PACK_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>PACK_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,137">
          <entity_field_id>vendorPO</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vpo.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,138">
          <entity_field_id>vpoNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPO.vpoNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,139">
          <entity_field_id>vpoMerchandiseHierarchy</entity_field_id>
          <entity_field_type>alias</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPO.merchandiseHierarchy</snapshot_field>
          <description>//--DEPRECATED IN CNT-13429</description>
          <dataType/>
          <data1>hierarchy2</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,140">
          <entity_field_id>hierarchy2</entity_field_id>
          <entity_field_type>hcl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPO.hierarchy</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_MERCHANDISE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,141">
          <entity_field_id>customerVpoRef</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPO.customerVpoRef</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_VPO_REF</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,142">
          <entity_field_id>vendorPOItem</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>VpoItem.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_ITEM_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,143">
          <entity_field_id>factCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.factCode</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACT_CODE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,144">
          <entity_field_id>image</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Image.id</entity_lookup>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.itemFileId</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_IMAGE_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,145">
          <entity_field_id>itemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.itemNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,146">
          <entity_field_id>lotNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.lotNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>LOT_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,147">
          <entity_field_id>customerItemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.customerItemNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_ITEM_NO</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,148">
          <entity_field_id>vendorItemNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.vendorItemNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_ITEM_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,149">
          <entity_field_id>itemName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.itemName</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,150">
          <entity_field_id>itemDesc</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.itemDesc</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_DESC</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,151">
          <entity_field_id>isSet</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.isSet</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_SET</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,152">
          <entity_field_id>vendorPOUOM</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.uom</snapshot_field>
          <description/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_UNIT_OF_MEASURE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,153">
          <entity_field_id>htsCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.htsCode</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>HTS_CODE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,154">
          <entity_field_id>countryofOrigin</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.countryOfShipment</snapshot_field>
          <description/>
          <dataType>codelist</dataType>
          <data1>COUNTRY</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>COUNTRY_OF_SHIPMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,155">
          <entity_field_id>cpoId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Cpo.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field>vendorPOItem.cpoId</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CPO_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,156">
          <entity_field_id>customerPONo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.cpoNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_PO_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,157">
          <entity_field_id>customerPORef</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.custPoNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_PO_NO_2</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,158">
          <entity_field_id>vendorPOShip</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>VpoShip.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_PO_SHIP_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,159">
          <entity_field_id>vendorPOShipmentNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.shipmentNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,160">
          <entity_field_id>vendorPOExfactoryDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.exFactoryDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>EXIT_FACTORY_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,161">
          <entity_field_id>vendorPOForwarderDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.forwarderDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FORWARDER_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,162">
          <entity_field_id>vendorPOShipmentDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.shipmentDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,163">
          <entity_field_id>vendorPOArrivalDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.arrivalDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ARRIVAL_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,164">
          <entity_field_id>vendorPOInDCDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.inDcDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IN_DC_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,165">
          <entity_field_id>vendorPOClosingDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOShip.closingDate</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CLOSING_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,166">
          <entity_field_id>shipmentBooking</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShipmentBooking.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_BOOKING_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,167">
          <entity_field_id>shipmentBookingNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>shipmentBooking.shipmentBookingNo</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_BOOKING_NO</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,168">
          <entity_field_id>shipmentBookingShipmentItem</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShipmentBookingShipmentItem.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_BOOKING_SHIP_ITEM_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,169">
          <entity_field_id>bookedQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>shipmentBookingShipmentItem.bookedQty</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>BOOKED_QTY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,170">
          <entity_field_id>bookedUOM</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>shipmentBookingShipmentItem.uom</snapshot_field>
          <description/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>BOOKED_UNIT_OF_MEASURE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,171">
          <entity_field_id>customerItemName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>vendorPOItem.customerItemName</snapshot_field>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CUSTOMER_ITEM_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,172">
          <entity_field_id>item</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Item.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,173">
          <entity_field_id>itemType</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field>item.itemType</snapshot_field>
          <description/>
          <dataType>codelist</dataType>
          <data1>ITEM_TYPE</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>ITEM_TYPE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,174">
          <entity_field_id>totalCartonCBM</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_CARTON_CBM</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,175">
          <entity_field_id>totalCartonCFT</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_CARTON_CFT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,176">
          <entity_field_id>grossWeightPerCarton</entity_field_id>
          <entity_field_type>decimal-weight</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>GROSS_WEIGHT_PER_CARTON</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,180">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,181">
          <entity_field_id>shipmentAdviceContainer</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShipmentAdviceContainer.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ADVICE_CONTAINER_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,182">
          <entity_field_id>uom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNIT_OF_MEASURE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,183">
          <entity_field_id>sentQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>SENT_QTY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,184">
          <entity_field_id>assortmentQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>ASSORTMENT_QTY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,185">
          <entity_field_id>variance</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VARIANCE</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,186">
          <entity_field_id>totalNoOfShippedCartons</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>TOTAL_SHIPPED_CARTONS_QTY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,187">
          <entity_field_id>unitsPerOuterCarton</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>UNITS_PER_OUTER_CARTON</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,188">
          <entity_field_id>dimensionUOM</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>DIMENSION_UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>DIMENSION_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,189">
          <entity_field_id>cartonLength</entity_field_id>
          <entity_field_type>decimal-dimension</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_LENGTH</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,190">
          <entity_field_id>cartonWidth</entity_field_id>
          <entity_field_type>decimal-dimension</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_WIDTH</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,191">
          <entity_field_id>cartonHeight</entity_field_id>
          <entity_field_type>decimal-dimension</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_HEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,192">
          <entity_field_id>cartonCBM</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_CBM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,193">
          <entity_field_id>cartonCFT</entity_field_id>
          <entity_field_type>decimal-cbm</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_CFT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,194">
          <entity_field_id>weightUOM</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>WEIGHT_UOM</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name>WEIGHT_UOM</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,195">
          <entity_field_id>cartonGrossWeight</entity_field_id>
          <entity_field_type>decimal-weight</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_GROSS_WEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,196">
          <entity_field_id>cartonNetWeight</entity_field_id>
          <entity_field_type>decimal-weight</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CARTON_NET_WEIGHT</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,197">
          <entity_field_id>notes</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REMARKS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,198">
          <entity_field_id>merchandiseHierarchy</entity_field_id>
          <entity_field_type>alias</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>//--DEPRECATED IN CNT-13429</description>
          <dataType/>
          <data1>hierarchy</data1>
          <data2/>
          <transitive_fields/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,199">
          <entity_field_id>hierarchy</entity_field_id>
          <entity_field_type>hcl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>MERCHANDISE_HIERARCHY</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,200">
          <entity_field_id>noOfPallets</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>NO_OF_PALLETS</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,201">
          <entity_field_id>vendorName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,202">
          <entity_field_id>factoryName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>FACTORY_NAME</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
      <elements id="collection">
        <element position="shipmentAdvice_entity.xlsx,entityDef,206">
          <entity_field_id>shipmentAdviceItemSizes</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceItemSize.shipmentAdviceShipmentItemId</entity_lookup>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,207">
          <entity_field_id>shipmentAdviceItemColors</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceItemColor.shipmentAdviceShipmentItemId</entity_lookup>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,208">
          <entity_field_id>shipmentAdviceItemCS</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>ShipmentAdviceItemCS.shipmentAdviceShipmentItemId</entity_lookup>
          <dataType/>
          <data1/>
          <report_column_name/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,209">
          <entity_field_id>productCategory</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <dataType>selection-codelist</dataType>
          <data1>PRODUCT_CATEGORY</data1>
          <report_column_name>PRODUCT_CATEGORY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceAddress" position="shipmentAdvice_entity.xlsx,entityDef,212" ref_pattern="" report_table_name="SHIPMENT_ADVICE_ADDRESS" table_name="CNT_SHIPMENT_ADVICE_ADDRESS">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,219">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,220">
          <entity_field_id>companyName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>COMPANY_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,221">
          <entity_field_id>address1</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDRESS1</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,222">
          <entity_field_id>address2</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDRESS2</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,223">
          <entity_field_id>address3</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDRESS3</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,224">
          <entity_field_id>address4</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>ADDRESS4</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,225">
          <entity_field_id>townOrCity</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>TOWN_OR_CITY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,226">
          <entity_field_id>stateOrProvince</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>STATE_OR_PROVINCE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,227">
          <entity_field_id>postalCode</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>POSTAL_CODE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,228">
          <entity_field_id>country</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>COUNTRY</data1>
          <data2/>
          <report_column_name>COUNTRY</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,229">
          <entity_field_id>portOfLoadingOrDischarge</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>PORT</data1>
          <data2/>
          <report_column_name>PORT_OF_LOADING_OR_DISCHARGE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,230">
          <entity_field_id>language</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>LANGUAGE</data1>
          <data2/>
          <report_column_name>LANGUAGE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="shipmentAdvice_entity.xlsx,entityDef,234">
          <entity_field_id>addressTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <data1>ADDRESS_TYPE</data1>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceContact" position="shipmentAdvice_entity.xlsx,entityDef,237" ref_pattern="${firstName} ${lastName}" report_table_name="SHIPMENT_ADVICE_CONTACT" table_name="CNT_SHIPMENT_ADVICE_CONTACT">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,244">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,245">
          <entity_field_id>salutation</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType>codelist</dataType>
          <data1>SALUTATION</data1>
          <data2/>
          <report_column_name>TITLE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,246">
          <entity_field_id>firstName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>FIRST_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,247">
          <entity_field_id>lastName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>LAST_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,248">
          <entity_field_id>position</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>POSITION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,249">
          <entity_field_id>department</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>DEPARTMENT</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,250">
          <entity_field_id>telNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>PHONE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,251">
          <entity_field_id>mobileNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>MOBILE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,252">
          <entity_field_id>faxNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>FAX</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,253">
          <entity_field_id>email</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>EMAIL</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="shipmentAdvice_entity.xlsx,entityDef,257">
          <entity_field_id>contactTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <data1>CONTACT_TYPE</data1>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceAttachment" position="shipmentAdvice_entity.xlsx,entityDef,260" ref_pattern="" report_table_name="SHIPMENT_ADVICE_ATTACHMENT" table_name="CNT_SHIPMENT_ADVICE_ATT">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,267">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <report_column_name>SHIPMENT_ADVICE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,268">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,269">
          <entity_field_id>attachment</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Attachment.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields>ALL</transitive_fields>
          <report_column_name>ATTACHMENT_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,270">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <report_column_name>UPDATE_USER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,271">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <transitive_fields/>
          <report_column_name>UPDATED_ON</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="shipmentAdvice_entity.xlsx,entityDef,275">
          <entity_field_id>attachmentTypes</entity_field_id>
          <entity_field_type>selection</entity_field_type>
          <entity_lookup>SelectionField.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceItemSize" position="shipmentAdvice_entity.xlsx,entityDef,278" ref_pattern="" report_table_name="SHIPMENT_ADVICE_ITEM_SIZE" table_name="CNT_SHIPMENT_ADVICE_ITEM_SIZE">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,285">
          <entity_field_id>shipmentAdviceShipmentItemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,286">
          <entity_field_id>itemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,287">
          <entity_field_id>itemSizeId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,288">
          <entity_field_id>vpoItemSizeId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,289">
          <entity_field_id>seqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,290">
          <entity_field_id>sizeCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,291">
          <entity_field_id>sizeLabel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,292">
          <entity_field_id>displayName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceItemColor" position="shipmentAdvice_entity.xlsx,entityDef,295" ref_pattern="" report_table_name="SHIPMENT_ADVICE_ITEM_COLOR" table_name="CNT_SHIPMENT_ADVICE_ITEM_COLOR">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,302">
          <entity_field_id>shipmentAdviceShipmentItemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,303">
          <entity_field_id>itemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,304">
          <entity_field_id>itemColorId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,305">
          <entity_field_id>vpoItemColorId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,306">
          <entity_field_id>colorQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,307">
          <entity_field_id>seqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,308">
          <entity_field_id>colorCode</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,309">
          <entity_field_id>colorLabel</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,310">
          <entity_field_id>shortName</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceItemCS" position="shipmentAdvice_entity.xlsx,entityDef,313" ref_pattern="" report_table_name="SHIPMENT_ADVICE_ITEM_CS" table_name="CNT_SHIPMENT_ADVICE_ITEM_CS">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,320">
          <entity_field_id>shipmentAdviceShipmentItemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,321">
          <entity_field_id>vpoShipmentNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,322">
          <entity_field_id>itemLotNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,323">
          <entity_field_id>ean</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,324">
          <entity_field_id>upc</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,325">
          <entity_field_id>skuNo</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,326">
          <entity_field_id>shipmentAdviceItemColorId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShipmentAdviceItemColor.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,327">
          <entity_field_id>colorSeq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,328">
          <entity_field_id>shipmentAdviceItemSizeId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShipmentAdviceItemSize.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,329">
          <entity_field_id>sizeSeq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,330">
          <entity_field_id>itemId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,331">
          <entity_field_id>itemColorId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,332">
          <entity_field_id>itemSizeId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,333">
          <entity_field_id>shipmentBookingItemCSId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,334">
          <entity_field_id>vendorPOShipmentItemCSId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description>unused</description>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,335">
          <entity_field_id>colorSizeQty</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceOther1" position="shipmentAdvice_entity.xlsx,entityDef,338" ref_pattern="" report_table_name="SHIPMENT_ADVICE_OTHER1" table_name="CNT_SHIPMENT_ADVICE_OTHER1">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,345">
          <entity_field_id>shipmentAdviceId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,346">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,347">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceOther2" position="shipmentAdvice_entity.xlsx,entityDef,350" ref_pattern="" report_table_name="SHIPMENT_ADVICE_OTHER2" table_name="CNT_SHIPMENT_ADVICE_OTHER2">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,357">
          <entity_field_id>shipmentAdviceId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,358">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,359">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceOther3" position="shipmentAdvice_entity.xlsx,entityDef,362" ref_pattern="" report_table_name="SHIPMENT_ADVICE_OTHER3" table_name="CNT_SHIPMENT_ADVICE_OTHER3">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,369">
          <entity_field_id>shipmentAdviceId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>VPO_SID</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,370">
          <entity_field_id>seq</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>SEQ</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,371">
          <entity_field_id>description</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <report_column_name>DESCRIPTION</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
    </Entity>
    <Entity name="ShipmentAdviceShareFile" position="shipmentAdvice_entity.xlsx,entityDef,374" ref_pattern="" report_table_name="SHIPMENT_ADVICE_SHARE_FILE" table_name="CNT_SHIPMENT_ADVICE_SHARE_FILE">
      <elements id="header">
        <element position="shipmentAdvice_entity.xlsx,entityDef,381">
          <entity_field_id>shipmentAdviceId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHIPMENT_ADVICE_SID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,382">
          <entity_field_id>shareFileId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>ShareFile.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>SHARE_FILE_SYS_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="shipmentAdvice_entity.xlsx,entityDef,383">
          <entity_field_id>isAddInDoc</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>IS_ADD_IN_DOC</report_column_name>
          <tracking_level/>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="shipmentAdvice_entity.xlsx,status">
    <Status position="shipmentAdvice_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="shipmentAdvice_entity.xlsx,status,4">
          <code>draft</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,5">
          <code>customsFilingAccepted</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,6">
          <code>shipmentOnBoard</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,7">
          <code>documentsUploaded</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,8">
          <code>shipmentClosed</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,9">
          <code>allInvoiceIssued</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,10">
          <code>customStatus01</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,11">
          <code>customStatus02</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,12">
          <code>customStatus03</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,13">
          <code>customStatus04</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,14">
          <code>customStatus05</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,15">
          <code>customStatus06</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,16">
          <code>customStatus07</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,17">
          <code>customStatus08</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,18">
          <code>customStatus09</code>
        </element>
        <element position="shipmentAdvice_entity.xlsx,status,19">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
