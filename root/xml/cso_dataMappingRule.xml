<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="cso" position="cso_dataMappingRule.xlsx">
  <sheet id="csoContactSelect" position="cso_dataMappingRule.xlsx,csoContactSelect">
    <DataMappingRule description="CSO Select Contact Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoContactSelect" position="cso_dataMappingRule.xlsx,csoContactSelect,1" srcEntityName="VendorContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>csoContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>csoContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoAddressSelect" position="cso_dataMappingRule.xlsx,csoAddressSelect">
    <DataMappingRule description="CSO Select Address Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoAddressSelect" position="cso_dataMappingRule.xlsx,csoAddressSelect,1" srcEntityName="VendorAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>businessName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Vendor</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>csoAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>csoAddress.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>csoAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelect,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>csoAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoShipCopy" position="cso_dataMappingRule.xlsx,csoShipCopy">
    <DataMappingRule description="CSO Shipment Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoShipCopy" position="cso_dataMappingRule.xlsx,csoShipCopy,1" srcEntityName="CsoShip" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoShipCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoShipCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoShip</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoItemSelect" position="cso_dataMappingRule.xlsx,csoItemSelect">
    <DataMappingRule description="Mapping for CSO Item Select" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoItemSelect" position="cso_dataMappingRule.xlsx,csoItemSelect,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,12">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,13">
          <mappingType>Field</mappingType>
          <srcFieldId>refPrice</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,14">
          <mappingType>Field</mappingType>
          <srcFieldId>materialConsumption</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,15">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,16">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,17">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,18">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSize</srcFieldId>
          <dstFieldId>csoItem.csoItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,20">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,21">
          <mappingType>Field</mappingType>
          <srcFieldId>code</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,22">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,23">
          <mappingType>Field</mappingType>
          <srcFieldId>altLabel</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,24">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,25">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,26">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,27">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,28">
          <mappingType>Field</mappingType>
          <srcFieldId>altColor</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoItemSelect,32">
          <type>PreProcessor</type>
          <templateName>CSO Item select config</templateName>
          <templateFile>cso_item_select_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemExtraChildEntityProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoItemCopy" position="cso_dataMappingRule.xlsx,csoItemCopy">
    <DataMappingRule description="Mapping from CSO Item Copy" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoItemCopy" position="cso_dataMappingRule.xlsx,csoItemCopy,1" srcEntityName="CsoItem" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoItemCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoItemCopy,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoItemCopyPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoCopy" position="cso_dataMappingRule.xlsx,csoCopy">
    <DataMappingRule description="Mapping for CSO Copy" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoCopy" position="cso_dataMappingRule.xlsx,csoCopy,1" srcEntityName="Cso" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopy,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoItemColorCopy" position="cso_dataMappingRule.xlsx,csoItemColorCopy">
    <DataMappingRule description="CSO Copy CsoItemColor Mapping" domain="/" dstEntityName="CsoShipDtl" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoItemColorCopy" position="cso_dataMappingRule.xlsx,csoItemColorCopy,1" srcEntityName="CsoItemColor" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoShipDtlColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>csoItem</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>specColorId</srcFieldId>
          <dstFieldId>specColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemColorCopy,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoShipDtlColor.csoItemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoItemSizeCopy" position="cso_dataMappingRule.xlsx,csoItemSizeCopy">
    <DataMappingRule description="CSO Copy CsoItemSize Mapping" domain="/" dstEntityName="CsoShipDtl" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoItemSizeCopy" position="cso_dataMappingRule.xlsx,csoItemSizeCopy,1" srcEntityName="CsoItemSize" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoShipDtlSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>csoItem</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>specId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>specSizeId</srcFieldId>
          <dstFieldId>specSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemSizeCopy,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoShipDtlSize.csoItemSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoChargeCopy" position="cso_dataMappingRule.xlsx,csoChargeCopy">
    <DataMappingRule description="CSO Charge Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoChargeCopy" position="cso_dataMappingRule.xlsx,csoChargeCopy,1" srcEntityName="CsoCharge" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoChargeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoChargeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoCharge</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoChargeCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>identity</srcFieldId>
          <dstFieldId>identity</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoChargeCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>flag</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>new</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoContactCopy" position="cso_dataMappingRule.xlsx,csoContactCopy">
    <DataMappingRule description="CSO Contact Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoContactCopy" position="cso_dataMappingRule.xlsx,csoContactCopy,1" srcEntityName="CsoContact" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoContactCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoAddressCopy" position="cso_dataMappingRule.xlsx,csoAddressCopy">
    <DataMappingRule description="CSO Address Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoAddressCopy" position="cso_dataMappingRule.xlsx,csoAddressCopy,1" srcEntityName="CsoAddress" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoAttachmentCopy" position="cso_dataMappingRule.xlsx,csoAttachmentCopy">
    <DataMappingRule description="CSO Attachment Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoAttachmentCopy" position="cso_dataMappingRule.xlsx,csoAttachmentCopy,1" srcEntityName="CsoAttachment" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoAttachmentCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAttachmentCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectVendor" position="cso_dataMappingRule.xlsx,csoSelectVendor">
    <DataMappingRule description="Mapping from Vendor" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-03-14" id="csoSelectVendor" position="cso_dataMappingRule.xlsx,csoSelectVendor,1" srcEntityName="Vendor" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectVendor,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVendor,9">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVendor,10">
          <mappingType>Section</mappingType>
          <srcFieldId/>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectVendor,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectVendorPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectCustomer" position="cso_dataMappingRule.xlsx,csoSelectCustomer">
    <DataMappingRule description="Mapping from Customer" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-03-14" id="csoSelectCustomer" position="cso_dataMappingRule.xlsx,csoSelectCustomer,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,9">
          <mappingType>Field</mappingType>
          <srcFieldId>contactName</srcFieldId>
          <dstFieldId>customerContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>phoneNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectCustomer,13">
          <mappingType>Section</mappingType>
          <srcFieldId/>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoChargeOnDocCopy" position="cso_dataMappingRule.xlsx,csoChargeOnDocCopy">
    <DataMappingRule description="CSO Charge on Doc Copy Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-01-01" id="csoChargeOnDocCopy" position="cso_dataMappingRule.xlsx,csoChargeOnDocCopy,1" srcEntityName="CsoChargeOnDoc" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoChargeOnDocCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoChargeOnDocCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectProjectItem" position="cso_dataMappingRule.xlsx,csoSelectProjectItem">
    <DataMappingRule description="Mapping for Project Item to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoSelectProjectItem" position="cso_dataMappingRule.xlsx,csoSelectProjectItem,1" srcEntityName="ProjectItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>projectId</srcFieldId>
          <dstFieldId>project</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Project</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.htsNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.moq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerOuter</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerInner</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.totalCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>csoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,22">
          <mappingType>Section</mappingType>
          <srcFieldId>item.productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultUom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,27">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,29">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,30">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>csoItem.quoteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.countryOfOrigin</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.factory</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,39">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectProjectItemPreProcessor</implementationClass>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectProjectItem,40">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SetOuterCartonToCsoItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectMpoItem" position="cso_dataMappingRule.xlsx,csoSelectMpoItem">
    <DataMappingRule description="Mapping from MpoItem to CsoItem" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2013-09-06" id="csoSelectMpoItem" position="cso_dataMappingRule.xlsx,csoSelectMpoItem,1" srcEntityName="MpoItem" srcEntityVersion="1" status="1" updatedDate="2013-09-06">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>mpoItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.htsNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.moq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerOuter</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerInner</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>mpoId</srcFieldId>
          <dstFieldId>mpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Mpo</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>custItemNo</srcFieldId>
          <dstFieldId>csoItem.customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>csoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>csoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>csoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>brand</srcFieldId>
          <dstFieldId>csoItem.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.mpoItemColors</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,36">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,37">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,38">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,39">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,40">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>mpoItems.mpoItemColors.itemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor.itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectMpoItem,46">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectMpoItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectVpoItem" position="cso_dataMappingRule.xlsx,csoSelectVpoItem">
    <DataMappingRule description="Mapping from VPO Item to CsoItem" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2013-09-06" id="csoSelectVpoItem" position="cso_dataMappingRule.xlsx,csoSelectVpoItem,1" srcEntityName="VpoItem" srcEntityVersion="1" status="1" updatedDate="2013-09-06">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>sellPrice</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>planedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoId</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Vpo</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfCartons</srcFieldId>
          <dstFieldId>noOfCartons</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerInnerCarton</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerExportCarton</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,23">
          <mappingType>Field</mappingType>
          <srcFieldId>l</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,24">
          <mappingType>Field</mappingType>
          <srcFieldId>w</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>h</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>csoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,31">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId.itemDesc</srcFieldId>
          <dstFieldId>csoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>factId</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,36">
          <mappingType>Section</mappingType>
          <srcFieldId>itemType</srcFieldId>
          <dstFieldId>csoItem.itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,37">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>csoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,38">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>csoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,39">
          <mappingType>Section</mappingType>
          <srcFieldId>brand</srcFieldId>
          <dstFieldId>csoItem.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,40">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>csoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,43">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionUOM</srcFieldId>
          <dstFieldId>csoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,44">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.vpoItemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,45">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,46">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,47">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,48">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,49">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,50">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.vpoItemColor.itemColorDoc</srcFieldId>
          <dstFieldId>csoItem.csoItemColor.itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,51">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteId</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectVpoItem,55">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectMpoItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectClaim" position="cso_dataMappingRule.xlsx,csoSelectClaim">
    <DataMappingRule description="Mapping for Claim to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoSelectClaim" position="cso_dataMappingRule.xlsx,csoSelectClaim,1" srcEntityName="Claim" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectClaim,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectClaim,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectClaim,10">
          <mappingType>Field</mappingType>
          <srcFieldId>claimNo</srcFieldId>
          <dstFieldId>referencedDoc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectClaim,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>chargeDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.claimReasonName+' - '+entity.currencyName+' - '+entity.finalAmount</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectClaim,15">
          <type>PostProcessor</type>
          <templateName>Cso select claim config</templateName>
          <templateFile>cso_select_claim_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SelectClaimPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectItemTask1" position="cso_dataMappingRule.xlsx,csoSelectItemTask1">
    <DataMappingRule description="Mapping for CSO Item Select" domain="/" dstEntityName="CsoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoSelectItemTask1" position="cso_dataMappingRule.xlsx,csoSelectItemTask1,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,9">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType/>
          <mappedValue>entity.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,11">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,12">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,13">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,14">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,15">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,16">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,17">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,19">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,20">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,21">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,22">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,23">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,24">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,25">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask1,29">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectItemTask2" position="cso_dataMappingRule.xlsx,csoSelectItemTask2">
    <DataMappingRule description="Mapping for CSO Item Select" domain="/" dstEntityName="CsoItem" dstEntityVersion="1" effectiveDate="2012-02-20" id="csoSelectItemTask2" position="cso_dataMappingRule.xlsx,csoSelectItemTask2,1" srcEntityName="ItemCust" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask2,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask2,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>itemCust</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectItemTask2,13">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ExtraItemCustEntityProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoContactSelectFromCustomer" position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer">
    <DataMappingRule description="CSO Select Contact From Customer Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2014-09-25" id="csoContactSelectFromCustomer" position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,1" srcEntityName="CustContact" srcEntityVersion="1" status="1" updatedDate="2014-09-25">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,17">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Customer</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,19">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>csoContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoContactSelectFromCustomer,20">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>csoContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoAddressSelectFromCustomer" position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer">
    <DataMappingRule description="CSO Select Address From Customer Mapping" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2014-09-25" id="csoAddressSelectFromCustomer" position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,1" srcEntityName="CustAddress" srcEntityVersion="1" status="1" updatedDate="2014-09-25">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,10">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,14">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>fromPartyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>Customer</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,19">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>csoAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,20">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>csoAddress.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,21">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>csoAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoAddressSelectFromCustomer,22">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>csoAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoCopyDefaultShipment" position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment">
    <DataMappingRule description="Mapping from Cso to CsoShip" domain="/" dstEntityName="CsoShip" dstEntityVersion="1" effectiveDate="2014-10-17" id="csoCopyDefaultShipment" position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,1" srcEntityName="Cso" srcEntityVersion="1" status="1" updatedDate="2014-10-17">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,9">
          <mappingType>Field</mappingType>
          <srcFieldId>originalShipmentDate</srcFieldId>
          <dstFieldId>originalShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,10">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,11">
          <mappingType>Field</mappingType>
          <srcFieldId>originalArrivalDate</srcFieldId>
          <dstFieldId>originalArrivalDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,12">
          <mappingType>Field</mappingType>
          <srcFieldId>originalExfactoryDate</srcFieldId>
          <dstFieldId>originalExfactoryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,13">
          <mappingType>Field</mappingType>
          <srcFieldId>originalInDcDate</srcFieldId>
          <dstFieldId>originalInDcDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,14">
          <mappingType>Field</mappingType>
          <srcFieldId>originalForwarderDate</srcFieldId>
          <dstFieldId>originalForwarderDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,15">
          <mappingType>Field</mappingType>
          <srcFieldId>originalClosingDate</srcFieldId>
          <dstFieldId>originalClosingDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,16">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedInspectionDate</srcFieldId>
          <dstFieldId>requestedInspectionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,17">
          <mappingType>Section</mappingType>
          <srcFieldId>shipMode</srcFieldId>
          <dstFieldId>shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,18">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,19">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,20">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfDestination</srcFieldId>
          <dstFieldId>countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,21">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,22">
          <mappingType>Section</mappingType>
          <srcFieldId>finalDestination</srcFieldId>
          <dstFieldId>finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoCopyDefaultShipment,26">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoCopyDefaultShipmentPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoSelectOsItem" position="cso_dataMappingRule.xlsx,csoSelectOsItem">
    <DataMappingRule description="Mapping from Offersheet to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-06-03" id="csoSelectOsItem" position="cso_dataMappingRule.xlsx,csoSelectOsItem,1" srcEntityName="OsItem" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemRef</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId>innerQty</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>outerQty</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>outerLength</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>outerWidth</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,23">
          <mappingType>Field</mappingType>
          <srcFieldId>outerHeight</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,24">
          <mappingType>Field</mappingType>
          <srcFieldId>outerGrossWeight</srcFieldId>
          <dstFieldId>gw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>outerNetWeight</srcFieldId>
          <dstFieldId>nw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,27">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId>osId</srcFieldId>
          <dstFieldId>offerSheet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,30">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId.itemDesc</srcFieldId>
          <dstFieldId>csoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>itemId.fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>csoItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>factId</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,36">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,37">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,38">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,39">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,40">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>csoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>csoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>csoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,43">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionUOM</srcFieldId>
          <dstFieldId>csoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,44">
          <mappingType>Section</mappingType>
          <srcFieldId>osItemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,45">
          <mappingType>Field</mappingType>
          <srcFieldId>osItemId</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,46">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,47">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,48">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,49">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,50">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,51">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,52">
          <mappingType>Section</mappingType>
          <srcFieldId>vqId</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoSelectOsItem,56">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoSelectOsItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="offerSheetGenCso" position="cso_dataMappingRule.xlsx,offerSheetGenCso">
    <DataMappingRule description="Mapping from Offersheet to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-06-03" id="offerSheetGenCso" position="cso_dataMappingRule.xlsx,offerSheetGenCso,1" srcEntityName="Offersheet" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,10">
          <mappingType>Field</mappingType>
          <srcFieldId>projectReference</srcFieldId>
          <dstFieldId>projRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,11">
          <mappingType>Field</mappingType>
          <srcFieldId>termsConditions</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.customer.paymentInstruction</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.customer.contactName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,14">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,15">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,16">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,17">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,19">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,20">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,21">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,22">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,23">
          <mappingType>Field</mappingType>
          <srcFieldId>osId</srcFieldId>
          <dstFieldId>offerSheet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,24">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,25">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,26">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,27">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemRef</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,28">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,29">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,30">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,31">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,32">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,33">
          <mappingType>Field</mappingType>
          <srcFieldId>innerQty</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,34">
          <mappingType>Field</mappingType>
          <srcFieldId>outerQty</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,35">
          <mappingType>Field</mappingType>
          <srcFieldId>outerLength</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,36">
          <mappingType>Field</mappingType>
          <srcFieldId>outerWidth</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,37">
          <mappingType>Field</mappingType>
          <srcFieldId>outerHeight</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,38">
          <mappingType>Field</mappingType>
          <srcFieldId>outerGrossWeight</srcFieldId>
          <dstFieldId>gw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,39">
          <mappingType>Field</mappingType>
          <srcFieldId>outerNetWeight</srcFieldId>
          <dstFieldId>nw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,40">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,41">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,42">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,43">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.itemId.itemDesc</srcFieldId>
          <dstFieldId>csoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,44">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,45">
          <mappingType>Section</mappingType>
          <srcFieldId>specId</srcFieldId>
          <dstFieldId>csoItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,46">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,47">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,48">
          <mappingType>Section</mappingType>
          <srcFieldId>factId</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,49">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,50">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,51">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,52">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,53">
          <mappingType>Section</mappingType>
          <srcFieldId>market</srcFieldId>
          <dstFieldId>csoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,54">
          <mappingType>Section</mappingType>
          <srcFieldId>channel</srcFieldId>
          <dstFieldId>csoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,55">
          <mappingType>Section</mappingType>
          <srcFieldId>weightUOM</srcFieldId>
          <dstFieldId>csoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,56">
          <mappingType>Section</mappingType>
          <srcFieldId>dimensionUOM</srcFieldId>
          <dstFieldId>csoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,57">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.osItemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,58">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,59">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,60">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,61">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,62">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,63">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,64">
          <mappingType>Section</mappingType>
          <srcFieldId>vqId</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,offerSheetGenCso,68">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetOrVpoGenCsoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="vpoGenCso" position="cso_dataMappingRule.xlsx,vpoGenCso">
    <DataMappingRule description="Mapping from Vpo to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-06-03" id="vpoGenCso" position="cso_dataMappingRule.xlsx,vpoGenCso,1" srcEntityName="Vpo" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,10">
          <mappingType>Field</mappingType>
          <srcFieldId>originalShipmentDate</srcFieldId>
          <dstFieldId>originalShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,11">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,12">
          <mappingType>Field</mappingType>
          <srcFieldId>originalExfactoryDate</srcFieldId>
          <dstFieldId>originalExfactoryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,13">
          <mappingType>Field</mappingType>
          <srcFieldId>originalClosingDate</srcFieldId>
          <dstFieldId>originalClosingDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,14">
          <mappingType>Field</mappingType>
          <srcFieldId>originalForwarderDate</srcFieldId>
          <dstFieldId>originalForwarderDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,15">
          <mappingType>Field</mappingType>
          <srcFieldId>originalArrivalDate</srcFieldId>
          <dstFieldId>originalArrivalDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,16">
          <mappingType>Field</mappingType>
          <srcFieldId>originalInDcDate</srcFieldId>
          <dstFieldId>originalInDcDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,17">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedInspectionDate</srcFieldId>
          <dstFieldId>requestedInspectionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,18">
          <mappingType>Field</mappingType>
          <srcFieldId>custContact</srcFieldId>
          <dstFieldId>customerContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,19">
          <mappingType>Field</mappingType>
          <srcFieldId>paymentInstructions</srcFieldId>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,20">
          <mappingType>Field</mappingType>
          <srcFieldId>otherTerms</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,21">
          <mappingType>Section</mappingType>
          <srcFieldId>shipMode</srcFieldId>
          <dstFieldId>shipmentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,22">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,23">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,24">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,25">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfDestination</srcFieldId>
          <dstFieldId>countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,26">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,27">
          <mappingType>Section</mappingType>
          <srcFieldId>finalDestination</srcFieldId>
          <dstFieldId>finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,28">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>customer</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,29">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.mobileNo</srcFieldId>
          <dstFieldId>phoneNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,30">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,31">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,32">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorId</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,33">
          <mappingType>Section</mappingType>
          <srcFieldId>headerFactory</srcFieldId>
          <dstFieldId>headerFactory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,34">
          <mappingType>Section</mappingType>
          <srcFieldId>subContractor</srcFieldId>
          <dstFieldId>subContractor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,35">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,36">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,37">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,38">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,39">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,40">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,41">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,42">
          <mappingType>Field</mappingType>
          <srcFieldId>vpoId</srcFieldId>
          <dstFieldId>vpoId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,43">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemName</srcFieldId>
          <dstFieldId>customerItemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,44">
          <mappingType>Field</mappingType>
          <srcFieldId>customerItemNo</srcFieldId>
          <dstFieldId>customerItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,45">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,46">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,47">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,48">
          <mappingType>Field</mappingType>
          <srcFieldId>sellPrice</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,49">
          <mappingType>Field</mappingType>
          <srcFieldId>buyPrice</srcFieldId>
          <dstFieldId>buyPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,50">
          <mappingType>Field</mappingType>
          <srcFieldId>buyCost</srcFieldId>
          <dstFieldId>buyCost</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,51">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,52">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,53">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerInnerCarton</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,54">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerExportCarton</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,55">
          <mappingType>Field</mappingType>
          <srcFieldId>l</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,56">
          <mappingType>Field</mappingType>
          <srcFieldId>w</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,57">
          <mappingType>Field</mappingType>
          <srcFieldId>h</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,58">
          <mappingType>Field</mappingType>
          <srcFieldId>gw</srcFieldId>
          <dstFieldId>gw</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,59">
          <mappingType>Field</mappingType>
          <srcFieldId>nw</srcFieldId>
          <dstFieldId>nw</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,60">
          <mappingType>Field</mappingType>
          <srcFieldId>cbm</srcFieldId>
          <dstFieldId>cbm</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,61">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,62">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,63">
          <mappingType>Field</mappingType>
          <srcFieldId>planedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,64">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfCartons</srcFieldId>
          <dstFieldId>noOfCartons</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,65">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,66">
          <mappingType>Field</mappingType>
          <srcFieldId>shipQty</srcFieldId>
          <dstFieldId>shipQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,67">
          <mappingType>Field</mappingType>
          <srcFieldId>variance</srcFieldId>
          <dstFieldId>variance</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,68">
          <mappingType>Field</mappingType>
          <srcFieldId>averageAmt</srcFieldId>
          <dstFieldId>averageAmt</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,69">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,70">
          <mappingType>Field</mappingType>
          <srcFieldId>projectNo</srcFieldId>
          <dstFieldId>projectNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,71">
          <mappingType>Field</mappingType>
          <srcFieldId>offerSheetNo</srcFieldId>
          <dstFieldId>offerSheetNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,72">
          <mappingType>Field</mappingType>
          <srcFieldId>mpoNo</srcFieldId>
          <dstFieldId>mpoNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,73">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,74">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,75">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.brand</srcFieldId>
          <dstFieldId>csoItem.brand</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,76">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.uom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,77">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.countryOfShipment</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,78">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,79">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.dimensionUOM</srcFieldId>
          <dstFieldId>csoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,80">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.weightUOM</srcFieldId>
          <dstFieldId>csoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,81">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.itemType</srcFieldId>
          <dstFieldId>csoItem.itemType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,82">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.offerSheetId</srcFieldId>
          <dstFieldId>csoItem.offerSheet</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,83">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,84">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.mpoId</srcFieldId>
          <dstFieldId>csoItem.mpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,85">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.itemSubContractor</srcFieldId>
          <dstFieldId>csoItem.itemSubContractor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,86">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.itemId</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,87">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.itemFileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,88">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.market</srcFieldId>
          <dstFieldId>csoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,89">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.channel</srcFieldId>
          <dstFieldId>csoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,90">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.factId</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,91">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoItem.vpoItemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,92">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,93">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,94">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,95">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,96">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,97">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,98">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip</srcFieldId>
          <dstFieldId>csoShip</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,99">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,100">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>shipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,101">
          <mappingType>Field</mappingType>
          <srcFieldId>originalShipmentDate</srcFieldId>
          <dstFieldId>originalShipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,102">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfContainer</srcFieldId>
          <dstFieldId>noOfContainer</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,103">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfContainer2</srcFieldId>
          <dstFieldId>noOfContainer2</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,104">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfTruck</srcFieldId>
          <dstFieldId>noOfTruck</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,105">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfTruck2</srcFieldId>
          <dstFieldId>noOfTruck2</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,106">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,107">
          <mappingType>Field</mappingType>
          <srcFieldId>originalExfactoryDate</srcFieldId>
          <dstFieldId>originalExfactoryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,108">
          <mappingType>Field</mappingType>
          <srcFieldId>exFactoryDate</srcFieldId>
          <dstFieldId>exFactoryDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,109">
          <mappingType>Field</mappingType>
          <srcFieldId>originalForwarderDate</srcFieldId>
          <dstFieldId>originalForwarderDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,110">
          <mappingType>Field</mappingType>
          <srcFieldId>forwarderDate</srcFieldId>
          <dstFieldId>forwarderDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,111">
          <mappingType>Field</mappingType>
          <srcFieldId>originalClosingDate</srcFieldId>
          <dstFieldId>originalClosingDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,112">
          <mappingType>Field</mappingType>
          <srcFieldId>closingDate</srcFieldId>
          <dstFieldId>closingDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,113">
          <mappingType>Field</mappingType>
          <srcFieldId>originalArrivalDate</srcFieldId>
          <dstFieldId>originalArrivalDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,114">
          <mappingType>Field</mappingType>
          <srcFieldId>arrivalDate</srcFieldId>
          <dstFieldId>arrivalDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,115">
          <mappingType>Field</mappingType>
          <srcFieldId>originalInDcDate</srcFieldId>
          <dstFieldId>originalInDcDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,116">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedInspectionDate</srcFieldId>
          <dstFieldId>requestedInspectionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,117">
          <mappingType>Field</mappingType>
          <srcFieldId>weight</srcFieldId>
          <dstFieldId>weight</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,118">
          <mappingType>Field</mappingType>
          <srcFieldId>cbm</srcFieldId>
          <dstFieldId>cbm</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,119">
          <mappingType>Field</mappingType>
          <srcFieldId>inDcDate</srcFieldId>
          <dstFieldId>inDcDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,120">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,121">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.shipmentStatus</srcFieldId>
          <dstFieldId>csoShip.shipmentStatus</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,122">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.containerType</srcFieldId>
          <dstFieldId>csoShip.containerType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,123">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.containerType2</srcFieldId>
          <dstFieldId>csoShip.containerType2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,124">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.truckType</srcFieldId>
          <dstFieldId>csoShip.truckType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,125">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.truckType2</srcFieldId>
          <dstFieldId>csoShip.truckType2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,126">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.shipMode</srcFieldId>
          <dstFieldId>csoShip.shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,127">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.countryOfOrigin</srcFieldId>
          <dstFieldId>csoShip.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,128">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.countryOfShipment</srcFieldId>
          <dstFieldId>csoShip.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,129">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.portOfLoading</srcFieldId>
          <dstFieldId>csoShip.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,130">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.countryOfDestination</srcFieldId>
          <dstFieldId>csoShip.countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,131">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.portOfDischarge</srcFieldId>
          <dstFieldId>csoShip.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,132">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.forwarder</srcFieldId>
          <dstFieldId>csoShip.forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,133">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.destinationForwarder</srcFieldId>
          <dstFieldId>csoShip.destinationForwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,134">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.finalDestination</srcFieldId>
          <dstFieldId>csoShip.finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,135">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShip.weightUOM</srcFieldId>
          <dstFieldId>csoShip.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,136">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl</srcFieldId>
          <dstFieldId>csoShipDtl</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,137">
          <mappingType>Field</mappingType>
          <srcFieldId>isSet</srcFieldId>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,138">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>qty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,139">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,140">
          <mappingType>Field</mappingType>
          <srcFieldId>originalQty</srcFieldId>
          <dstFieldId>originalQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,141">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyType</srcFieldId>
          <dstFieldId>qtyType</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,142">
          <mappingType>Field</mappingType>
          <srcFieldId>requestedInspectionQty</srcFieldId>
          <dstFieldId>requestedInspectionQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,143">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyVariance</srcFieldId>
          <dstFieldId>qtyVariance</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,144">
          <mappingType>Field</mappingType>
          <srcFieldId>packUnit</srcFieldId>
          <dstFieldId>packUnit</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,145">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.packType</srcFieldId>
          <dstFieldId>csoShipDtl.packType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,146">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.vpoItemId</srcFieldId>
          <dstFieldId>csoShipDtl.csoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,147">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.vpoShipId</srcFieldId>
          <dstFieldId>csoShipDtl.csoShip</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,148">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.vpoShipDtlCs</srcFieldId>
          <dstFieldId>csoShipDtl.csoShipDtlCs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,149">
          <mappingType>Field</mappingType>
          <srcFieldId>ratios</srcFieldId>
          <dstFieldId>ratios</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,150">
          <mappingType>Field</mappingType>
          <srcFieldId>ratio</srcFieldId>
          <dstFieldId>ratio</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,151">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSizeQty</srcFieldId>
          <dstFieldId>colorSizeQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,152">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>shipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,153">
          <mappingType>Field</mappingType>
          <srcFieldId>skuNo</srcFieldId>
          <dstFieldId>skuNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,154">
          <mappingType>Field</mappingType>
          <srcFieldId>upc</srcFieldId>
          <dstFieldId>upc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,155">
          <mappingType>Field</mappingType>
          <srcFieldId>ean</srcFieldId>
          <dstFieldId>ean</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,156">
          <mappingType>Field</mappingType>
          <srcFieldId>itemLotNo</srcFieldId>
          <dstFieldId>itemLotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,157">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>sizeSeq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,158">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.vpoShipDtlCs.vpoShipDtlColorId</srcFieldId>
          <dstFieldId>csoShipDtl.csoShipDtlCs.csoShipDtlColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,159">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoShipDtl.vpoShipDtlCs.vpoShipDtlSizeId</srcFieldId>
          <dstFieldId>csoShipDtl.csoShipDtlCs.csoShipDtlSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,160">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoCharge</srcFieldId>
          <dstFieldId>csoCharge</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,161">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeDesc</srcFieldId>
          <dstFieldId>chargeDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,162">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeValue</srcFieldId>
          <dstFieldId>chargeValue</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,163">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeAmt</srcFieldId>
          <dstFieldId>chargeAmt</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,164">
          <mappingType>Field</mappingType>
          <srcFieldId>notes</srcFieldId>
          <dstFieldId>notes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,165">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoCharge.chargeType</srcFieldId>
          <dstFieldId>csoCharge.chargeType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,166">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoCharge.reasonCode</srcFieldId>
          <dstFieldId>csoCharge.reasonCode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,167">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoCharge.calculateType</srcFieldId>
          <dstFieldId>csoCharge.calculateType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,168">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoCharge.vpoItemId</srcFieldId>
          <dstFieldId>csoCharge.csoItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,169">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoChargeOnDoc</srcFieldId>
          <dstFieldId>csoChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,170">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeDesc</srcFieldId>
          <dstFieldId>chargeDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,171">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeValue</srcFieldId>
          <dstFieldId>chargeValue</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,172">
          <mappingType>Field</mappingType>
          <srcFieldId>chargeAmt</srcFieldId>
          <dstFieldId>chargeAmt</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,173">
          <mappingType>Field</mappingType>
          <srcFieldId>notes</srcFieldId>
          <dstFieldId>notes</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,174">
          <mappingType>Field</mappingType>
          <srcFieldId>referencedDoc</srcFieldId>
          <dstFieldId>referencedDoc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,175">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoChargeOnDoc.chargeType</srcFieldId>
          <dstFieldId>csoChargeOnDoc.chargeType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,176">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoChargeOnDoc.reasonCode</srcFieldId>
          <dstFieldId>csoChargeOnDoc.reasonCode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,177">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoChargeOnDoc.calculateType</srcFieldId>
          <dstFieldId>csoChargeOnDoc.calculateType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,178">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoContact</srcFieldId>
          <dstFieldId>csoContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,179">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,180">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,181">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,182">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,183">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,184">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,185">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,186">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,187">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoContact.title</srcFieldId>
          <dstFieldId>csoContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,188">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoContact.partyType</srcFieldId>
          <dstFieldId>csoContact.partyType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,189">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoContact.contactTypeId</srcFieldId>
          <dstFieldId>csoContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,190">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress</srcFieldId>
          <dstFieldId>csoAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,191">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,192">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,193">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,194">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,195">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,196">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,197">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,198">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,199">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress.country</srcFieldId>
          <dstFieldId>csoAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,200">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress.portOfDischarge</srcFieldId>
          <dstFieldId>csoAddress.portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,201">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress.language</srcFieldId>
          <dstFieldId>csoAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,202">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress.partyType</srcFieldId>
          <dstFieldId>csoAddress.partyType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,203">
          <mappingType>Section</mappingType>
          <srcFieldId>vpoAddress.addressTypeId</srcFieldId>
          <dstFieldId>csoAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,204">
          <mappingType>Section</mappingType>
          <srcFieldId>quoteId</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,vpoGenCso,208">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetOrVpoGenCsoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="itemNewCso" position="cso_dataMappingRule.xlsx,itemNewCso">
    <DataMappingRule description="Mapping from item to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-03-15" id="itemNewCso" position="cso_dataMappingRule.xlsx,itemNewCso,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,itemNewCso,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,11">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,12">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,13">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,14">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,15">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,16">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,18">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>csoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,19">
          <mappingType>Field</mappingType>
          <srcFieldId>refPrice</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,20">
          <mappingType>Field</mappingType>
          <srcFieldId>materialConsumption</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,21">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,22">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,23">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,24">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,25">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,26">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,27">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,28">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,29">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,30">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,31">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,32">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,33">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,itemNewCso,34">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,itemNewCso,38">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ItemNewCsoSetupfieldVauleProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="projectNewCso" position="cso_dataMappingRule.xlsx,projectNewCso">
    <DataMappingRule description="Mapping from Project to Cso" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2012-02-20" id="projectNewCso" position="cso_dataMappingRule.xlsx,projectNewCso,1" srcEntityName="Project" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,projectNewCso,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,9">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,10">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,11">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,12">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,13">
          <mappingType>Section</mappingType>
          <srcFieldId>projectItems</srcFieldId>
          <dstFieldId>csoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,14">
          <mappingType>Field</mappingType>
          <srcFieldId>projectId</srcFieldId>
          <dstFieldId>project</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>Project</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.qtyPerExportCarton</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue>entity.quotation.unitsPerOuter</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.qtyPerInnerCarton</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue>entity.quotation.unitsPerInner</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.moq</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue>entity.quotation.moq</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.htsNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.sellPrice</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue>entity.quotation.totalCost</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,22">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>csoItem.isSet</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue>entity.item.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,23">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>csoItem.planedQty</dstFieldId>
          <dstFieldType>decimal</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,24">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.countryOfOrigin</srcFieldId>
          <dstFieldId>csoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,25">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.factory</srcFieldId>
          <dstFieldId>csoItem.factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,26">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.portOfLoading</srcFieldId>
          <dstFieldId>csoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,27">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>csoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,28">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>csoItem.item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,29">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>csoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,30">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultUom</srcFieldId>
          <dstFieldId>csoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,31">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>csoItem.itemImage</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,32">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,33">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,34">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>csoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,35">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>csoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,36">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>csoItem.csoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,37">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,38">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,39">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,40">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,41">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,42">
          <mappingType>Section</mappingType>
          <srcFieldId>ProjectItem.quotation</srcFieldId>
          <dstFieldId>csoItem.vq</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,projectNewCso,46">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ProjectGenVpoSetupFieldValuePreProcessor</implementationClass>
        </element>
        <element position="cso_dataMappingRule.xlsx,projectNewCso,47">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ProjectNewCsoSetupFieldValueProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoOther1Copy" position="cso_dataMappingRule.xlsx,csoOther1Copy">
    <DataMappingRule description="Mapping from cso other copy" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2020-01-03" id="csoOther1Copy" position="cso_dataMappingRule.xlsx,csoOther1Copy,1" srcEntityName="CsoOther1" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoOther1Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoOther1Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoOther1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoOther2Copy" position="cso_dataMappingRule.xlsx,csoOther2Copy">
    <DataMappingRule description="Mapping from cso other copy" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2020-01-03" id="csoOther2Copy" position="cso_dataMappingRule.xlsx,csoOther2Copy,1" srcEntityName="CsoOther2" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoOther2Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoOther2Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoOther2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoOther3Copy" position="cso_dataMappingRule.xlsx,csoOther3Copy">
    <DataMappingRule description="Mapping from cso other copy" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2020-01-03" id="csoOther3Copy" position="cso_dataMappingRule.xlsx,csoOther3Copy,1" srcEntityName="CsoOther3" srcEntityVersion="1" status="1" updatedDate="2020-01-03">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoOther3Copy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoOther3Copy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoOther3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoTermsAndConditionsCopy" position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy">
    <DataMappingRule description="Mapping for Copy CsoTermsAndConditions" domain="/" dstEntityName="Cso" dstEntityVersion="1" effectiveDate="2020-07-23" id="csoTermsAndConditionsCopy" position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,1" srcEntityName="CsoTermsAndConditions" srcEntityVersion="1" status="1" updatedDate="2020-07-23">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>csoTermsAndConditions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,10">
          <mappingType>Field</mappingType>
          <srcFieldId>tcName</srcFieldId>
          <dstFieldId>csoTermsAndConditions.tcName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,11">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>csoTermsAndConditions.remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,12">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>csoTermsAndConditions.description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,13">
          <mappingType>Field</mappingType>
          <srcFieldId>otherDesc</srcFieldId>
          <dstFieldId>csoTermsAndConditions.otherDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,14">
          <mappingType>Field</mappingType>
          <srcFieldId>effectiveFrom</srcFieldId>
          <dstFieldId>csoTermsAndConditions.effectiveFrom</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,15">
          <mappingType>Field</mappingType>
          <srcFieldId>effectiveTo</srcFieldId>
          <dstFieldId>csoTermsAndConditions.effectiveTo</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,16">
          <mappingType>Section</mappingType>
          <srcFieldId>tcType</srcFieldId>
          <dstFieldId>csoTermsAndConditions.tcType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoTermsAndConditionsCopy,17">
          <mappingType>Section</mappingType>
          <srcFieldId>image</srcFieldId>
          <dstFieldId>csoTermsAndConditions.image</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="csoItemNewVpo" position="cso_dataMappingRule.xlsx,csoItemNewVpo">
    <DataMappingRule description="Mapping from Vpo to Cso" domain="/" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-06-03" id="csoItemNewVpo" position="cso_dataMappingRule.xlsx,csoItemNewVpo,1" srcEntityName="Cso" srcEntityVersion="1" status="1">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,14">
          <mappingType>Field</mappingType>
          <srcFieldId>planedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,15">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerExportCarton</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,16">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,17">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,18">
          <mappingType>Field</mappingType>
          <srcFieldId>l</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,19">
          <mappingType>Field</mappingType>
          <srcFieldId>w</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,20">
          <mappingType>Field</mappingType>
          <srcFieldId>h</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultPrice</srcFieldId>
          <dstFieldId>defaultPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>csoShip</srcFieldId>
          <dstFieldId>vpoShip</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>shipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfDestination</srcFieldId>
          <dstFieldId>countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>forwarder</srcFieldId>
          <dstFieldId>forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>finalDestination</srcFieldId>
          <dstFieldId>finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfContainer</srcFieldId>
          <dstFieldId>noOfContainer</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,37">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoQtyItemNewVpoPreProcessor</implementationClass>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoItemNewVpo,38">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoQtyItemNewVpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="csoQtyShipNewVpo" position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo">
    <DataMappingRule description="Mapping from Vpo to Cso" domain="/" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-06-03" id="csoQtyShipNewVpo" position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,1" srcEntityName="Cso" srcEntityVersion="1" status="1">
      <elements id="mappingRule">
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,10">
          <mappingType>Section</mappingType>
          <srcFieldId>csoItem</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,14">
          <mappingType>Field</mappingType>
          <srcFieldId>planedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,15">
          <mappingType>Field</mappingType>
          <srcFieldId>qtyPerExportCarton</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,16">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,17">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,18">
          <mappingType>Field</mappingType>
          <srcFieldId>l</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,19">
          <mappingType>Field</mappingType>
          <srcFieldId>w</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,20">
          <mappingType>Field</mappingType>
          <srcFieldId>h</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,21">
          <mappingType>Field</mappingType>
          <srcFieldId>defaultPrice</srcFieldId>
          <dstFieldId>defaultPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>csoShip</srcFieldId>
          <dstFieldId>vpoShip</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentNo</srcFieldId>
          <dstFieldId>shipmentNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>countryOfDestination</srcFieldId>
          <dstFieldId>countryOfDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>portOfDischarge</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>forwarder</srcFieldId>
          <dstFieldId>forwarder</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>finalDestination</srcFieldId>
          <dstFieldId>finalDestination</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>noOfContainer</srcFieldId>
          <dstFieldId>noOfContainer</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,37">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoQtyShipNewVpoPreProcessor</implementationClass>
        </element>
        <element position="cso_dataMappingRule.xlsx,csoQtyShipNewVpo,38">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CsoQtyShipNewVpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
