<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="measurementTemplate" position="measurementTemplate_form_security.xlsx">
  <sheet id="_system" position="measurementTemplate_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="measurementTemplate_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="measurementTemplate_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/14</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="measurementTemplate_form_security.xlsx,generalInfo">
    <GeneralInfo position="measurementTemplate_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="measurementTemplate_form_security.xlsx,condition">
    <ConditionList position="measurementTemplate_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,6">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,7">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,8">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,9">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,10">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,11">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="measurementTemplate_form_security.xlsx,condition,12">
          <conditionId>isNotLatest</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="measurementTemplate_form_security.xlsx,default">
    <ActionConditionMatrix position="measurementTemplate_form_security.xlsx,default,1">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,5">
          <actionId>amendDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,6">
          <actionId>saveAndConfirm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,7">
          <actionId>baseSaveDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,8">
          <actionId>discardDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,9">
          <actionId>copyDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,10">
          <actionId>activateDoc</actionId>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,11">
          <actionId>deactivateDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,12">
          <actionId>viewChangeHistoryDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,13">
          <actionId>loadDoc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,14">
          <actionId>initializeCpm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>disallowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,15">
          <actionId>measurementTemplateCustom01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,16">
          <actionId>measurementTemplateCustom02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,17">
          <actionId>measurementTemplateCustom03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,18">
          <actionId>measurementTemplateCustom04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,19">
          <actionId>measurementTemplateCustom05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,20">
          <actionId>measurementTemplateCustom06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,21">
          <actionId>measurementTemplateCustom07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,22">
          <actionId>measurementTemplateCustom08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,23">
          <actionId>measurementTemplateCustom09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,24">
          <actionId>measurementTemplateCustom10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,25">
          <actionId>markAsCustomStatus01Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,26">
          <actionId>markAsCustomStatus02Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,27">
          <actionId>markAsCustomStatus03Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,28">
          <actionId>markAsCustomStatus04Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,29">
          <actionId>markAsCustomStatus05Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,30">
          <actionId>markAsCustomStatus06Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,31">
          <actionId>markAsCustomStatus07Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,32">
          <actionId>markAsCustomStatus08Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,33">
          <actionId>markAsCustomStatus09Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,34">
          <actionId>markAsCustomStatus10Doc</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,35">
          <actionId>customExport01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,36">
          <actionId>customExport02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,37">
          <actionId>customExport03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,38">
          <actionId>customExport04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,39">
          <actionId>customExport05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,40">
          <actionId>customExport06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,41">
          <actionId>customExport07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,42">
          <actionId>customExport08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,43">
          <actionId>customExport09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,44">
          <actionId>customExport10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,45">
          <actionId>customPrint01</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,46">
          <actionId>customPrint02</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,47">
          <actionId>customPrint03</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,48">
          <actionId>customPrint04</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,49">
          <actionId>customPrint05</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,50">
          <actionId>customPrint06</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,51">
          <actionId>customPrint07</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,52">
          <actionId>customPrint08</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,53">
          <actionId>customPrint09</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,54">
          <actionId>customPrint10</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,55">
          <actionId>reinitializeCpm</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,56">
          <actionId>refreshCpmTemplate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,57">
          <actionId>refreshCpmPlanDate</actionId>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <editingStatusPending>allowed</editingStatusPending>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="measurementTemplate_form_security.xlsx,default,60">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,default,63">
          <componentId>ui</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <editingStatusPending>readonly</editingStatusPending>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,64">
          <componentId>ui.measurementTemplateLinkbar.duplicateWindow</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <editingStatusPending>inherit</editingStatusPending>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,65">
          <componentId>ui.measurementTemplateLinkbar.approval</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <editingStatusPending>inherit</editingStatusPending>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,66">
          <componentId>ui.measurementTemplateLinkbar.addToFavorites</componentId>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <editingStatusPending>inherit</editingStatusPending>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
        <element position="measurementTemplate_form_security.xlsx,default,67">
          <componentId>ui.measurementTemplateMenubar.amendDoc</componentId>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <editingStatusPending>hidden</editingStatusPending>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="measurementTemplate_form_security.xlsx,acl">
    <ActionRule position="measurementTemplate_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,5">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,6">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,7">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,8">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,9">
          <actionId>exportDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,10">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,11">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,12">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,13">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,14">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,15">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,16">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,17">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,18">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,19">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,20">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,21">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,22">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,23">
          <actionId>viewHistory</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,24">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,25">
          <actionId>listHistory</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>has</measurementTemplate.Author>
          <measurementTemplate.Editor>has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,26">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,27">
          <actionId>measurementTemplateCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,28">
          <actionId>measurementTemplateCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,29">
          <actionId>measurementTemplateCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,30">
          <actionId>measurementTemplateCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,31">
          <actionId>measurementTemplateCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,32">
          <actionId>measurementTemplateCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,33">
          <actionId>measurementTemplateCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,34">
          <actionId>measurementTemplateCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,35">
          <actionId>measurementTemplateCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,36">
          <actionId>measurementTemplateCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,37">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,38">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,39">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,40">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,41">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,42">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,43">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,44">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,45">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,46">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,47">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,48">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,49">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,50">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,51">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,52">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,53">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,54">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,55">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,56">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,57">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,58">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,59">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <measurementTemplate.Author>not-has</measurementTemplate.Author>
          <measurementTemplate.Editor>not-has</measurementTemplate.Editor>
          <measurementTemplate.ReadOnly>not-has</measurementTemplate.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="measurementTemplate_form_security.xlsx,acl,62">
      <elements id="default">
        <element position="measurementTemplate_form_security.xlsx,acl,65">
          <componentId>ui</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="measurementTemplate_form_security.xlsx,acl,66">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="measurementTemplate_form_security.xlsx,acl,69">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
