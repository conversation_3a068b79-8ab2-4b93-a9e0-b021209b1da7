<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="lineSheet" position="lineSheet_view_security.xlsx">
  <sheet id="generalInfo" position="lineSheet_view_security.xlsx,generalInfo">
    <GeneralInfo position="lineSheet_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="lineSheet_view_security.xlsx,condition">
    <ConditionList position="lineSheet_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="lineSheet_view_security.xlsx,default">
    <ActionConditionMatrix position="lineSheet_view_security.xlsx,default,1">
      <elements id="default">
        <element position="lineSheet_view_security.xlsx,default,4">
          <actionId>searchNewDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,5">
          <actionId>searchActivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,6">
          <actionId>searchDeactivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,7">
          <actionId>searchCancelDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,8">
          <actionId>searchViewAllExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,9">
          <actionId>searchMarkAsProductDevelopment</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,10">
          <actionId>searchMarkAsSourcing</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,11">
          <actionId>searchMarkAsIntendedToBuy</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,12">
          <actionId>searchMarkAsOrdered</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="lineSheet_view_security.xlsx,default,13">
          <actionId>openCpmMode</actionId>
          <ANY>allowed</ANY>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="lineSheet_view_security.xlsx,default,16">
      <elements id="default"/>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="lineSheet_view_security.xlsx,acl">
    <ActionRule position="lineSheet_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="lineSheet_view_security.xlsx,acl,4">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,5">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,6">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,7">
          <actionId>searchCancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,8">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,9">
          <actionId>searchMarkAsProductDevelopment</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,10">
          <actionId>searchMarkAsSourcing</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,11">
          <actionId>searchMarkAsIntendedToBuy</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,12">
          <actionId>searchMarkAsOrdered</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
        <element position="lineSheet_view_security.xlsx,acl,13">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="lineSheet_view_security.xlsx,acl,16">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="lineSheet_view_security.xlsx,acl,21">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
