<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="entityConfig" position="entityConfig_view.xlsx">
  <sheet id="entityConfigView" position="entityConfig_view.xlsx,entityConfigView">
    <ViewDefinition advancedSearchId="" description="Entity Configurations View" id="entityConfigView" label="Entity Configuration" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigView,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigView,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigView,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigView,44">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupEntityConfigView" position="entityConfig_view.xlsx,popupEntityConfigView">
    <ViewDefinition advancedSearchId="" description="Popup Entity Configuration View" id="popupEntityConfigView" label="Entity Configuration Lookup" moduleId="entityConfig" position="entityConfig_view.xlsx,popupEntityConfigView,1" queryId="popupEntityConfig" searchCriterion="" viewCategory="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,popupEntityConfigView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>single</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,13">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,14">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,15">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,popupEntityConfigView,23">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEC.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,24">
          <id>entityId</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEC.ENTITY_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,25">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.entityConfig.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,26">
          <id>EntityConfig</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,27">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,28">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupEntityConfigView,29">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupFieldConfigView" position="entityConfig_view.xlsx,popupFieldConfigView">
    <ViewDefinition advancedSearchId="" description="Popup Field Configuration View" id="popupFieldConfigView" label="Field Configuration Lookup" moduleId="entityConfig" position="entityConfig_view.xlsx,popupFieldConfigView,1" queryId="popupFieldConfig" searchCriterion="" viewCategory="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,popupFieldConfigView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,13">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,14">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,15">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,popupFieldConfigView,23">
          <id>label</id>
          <label>Label</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CFC.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,24">
          <id>fieldId</id>
          <label>Field ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CFC.FIELD_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,25">
          <id>fieldType</id>
          <label>Field Type Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CFC.FIELD_TYPE_NAME</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,26">
          <id>FieldConfig</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CFC</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,27">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,28">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupFieldConfigView,29">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupMainEntityConfigsView" position="entityConfig_view.xlsx,popupMainEntityConfigsView">
    <ViewDefinition advancedSearchId="" description="Popup Entity Configuration View" id="popupMainEntityConfigsView" label="Entity Configuration Lookup" moduleId="entityConfig" position="entityConfig_view.xlsx,popupMainEntityConfigsView,1" queryId="listMainEntityConfigs" searchCriterion="" viewCategory="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>single</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,14">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,15">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,23">
          <id>label</id>
          <label>Entity Label</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,24">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,25">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.entityConfig.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,26">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,27">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,28">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,29">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,30">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,31">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,32">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,33">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,34">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,35">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupMainEntityConfigsView,36">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupChildEntityConfigsView" position="entityConfig_view.xlsx,popupChildEntityConfigsView">
    <ViewDefinition advancedSearchId="" description="Popup Entity Configuration View" id="popupChildEntityConfigsView" label="Child Entity Configuration Lookup" moduleId="entityConfig" position="entityConfig_view.xlsx,popupChildEntityConfigsView,1" queryId="listChildEntityConfigs" searchCriterion="" viewCategory="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,14">
          <id>LATEST_VERSION</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,15">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,23">
          <id>seq</id>
          <label>Seq.No.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEC.SEQ</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,24">
          <id>label</id>
          <label>Entity Label</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEC.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,25">
          <id>entityId</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEC.ENTITY_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,26">
          <id>entityModuleId</id>
          <label>Entity Module Id</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEC.ENTITY_MODULE_ID</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,27">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.entityConfig.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,28">
          <id>EntityConfig</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,29">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,30">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,31">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,32">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,33">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,34">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,35">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,36">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,37">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupChildEntityConfigsView,38">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupDataLookupCodelistView" position="entityConfig_view.xlsx,popupDataLookupCodelistView">
    <ViewDefinition advancedSearchId="" description="Popup Codelist View" id="popupDataLookupCodelistView" label="Code List Lookup" moduleId="codelist" position="entityConfig_view.xlsx,popupDataLookupCodelistView,1" queryId="popupDataLookupCodelist" searchCriterion="" viewCategory="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST,DETAIL</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>single</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,22">
          <id>bookName</id>
          <label>Book Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CCB.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,23">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,24">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CCB.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,popupDataLookupCodelistView,25">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CCB.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus01" position="entityConfig_view.xlsx,entityConfigForCustomStatus01">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 01 View" id="entityConfigForCustomStatus01View" label="Entity Configuration - Custom Status 01" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus01,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus01,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus02" position="entityConfig_view.xlsx,entityConfigForCustomStatus02">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 02 View" id="entityConfigForCustomStatus02View" label="Entity Configuration - Custom Status 02" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus02,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus02,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus03" position="entityConfig_view.xlsx,entityConfigForCustomStatus03">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 03 View" id="entityConfigForCustomStatus03View" label="Entity Configuration - Custom Status 03" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus03,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus03,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus04" position="entityConfig_view.xlsx,entityConfigForCustomStatus04">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 04 View" id="entityConfigForCustomStatus04View" label="Entity Configuration - Custom Status 04" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus04,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus04,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus05" position="entityConfig_view.xlsx,entityConfigForCustomStatus05">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 05 View" id="entityConfigForCustomStatus05View" label="Entity Configuration - Custom Status 05" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus05,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus05,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus06" position="entityConfig_view.xlsx,entityConfigForCustomStatus06">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 06 View" id="entityConfigForCustomStatus06View" label="Entity Configuration - Custom Status 06" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus06,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus06,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus07" position="entityConfig_view.xlsx,entityConfigForCustomStatus07">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 07 View" id="entityConfigForCustomStatus07View" label="Entity Configuration - Custom Status 07" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus07,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus07,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus08" position="entityConfig_view.xlsx,entityConfigForCustomStatus08">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 08 View" id="entityConfigForCustomStatus08View" label="Entity Configuration - Custom Status 08" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus08,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus08,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus09" position="entityConfig_view.xlsx,entityConfigForCustomStatus09">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 09 View" id="entityConfigForCustomStatus09View" label="Entity Configuration - Custom Status 09" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus09,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus09,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="entityConfigForCustomStatus10" position="entityConfig_view.xlsx,entityConfigForCustomStatus10">
    <ViewDefinition advancedSearchId="" description="Entity Configurations Custom Status 10View" id="entityConfigForCustomStatus10View" label="Entity Configuration - Custom Status 10" moduleId="entityConfig" position="entityConfig_view.xlsx,entityConfigForCustomStatus10,1" queryId="listEntityModules" searchCriterion="">
      <elements id="options">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,12">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,13">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,14">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,18">
          <id>entityConfigSearchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;winId=popupSelectEntity</actionParams>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,19">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,20">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,21">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,22">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,23">
          <id>entityConfigDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,24">
          <id>entityConfigImport</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;upload=true&amp;listener=com.core.cbx.ui.listener.EntityConfigUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,25">
          <id>searchGenScript</id>
          <label>Generate SQL</label>
          <type>button</type>
          <actionParams>moduleId=entityConfig&amp;entityName=EntityModule&amp;target=entity</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,30">
          <id>label</id>
          <label>Entity Label</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,31">
          <id>module</id>
          <label>Entity ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CEM.MODULE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,32">
          <id>domainId</id>
          <label>Domain</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOMAIN_ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,33">
          <id>EntityModule</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,36">
          <id>version</id>
          <label>Module Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CEM.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,37">
          <id>editingStatus</id>
          <label>Module Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,38">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,39">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,40">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,41">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CEM.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CEM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="entityConfig_view.xlsx,entityConfigForCustomStatus10,43">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CEM.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
