<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="printFormExportTemplate" position="printFormExportTemplate_view.xlsx">
  <sheet id="printFormExportTemplateView" position="printFormExportTemplate_view.xlsx,printFormExportTemplateView">
    <ViewDefinition advancedSearchId="" description="Print Form Export Template View" id="printFormExportTemplateView" label="Print Forms / Export Templates" moduleId="printFormExportTemplate" position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,1" queryId="printFormExportTemplateList" searchCriterion="">
      <elements id="options">
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>PrintFormExportTemplate</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,isCpmInitialized:isCpmInitialized:boolean,businessRefNo:businessReference:string,isLatest:isLatest:boolean,refNo:refNo:string,version:version:integer</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,18">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,19">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,20">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,22">
          <id>searchActivateDoc</id>
          <label>Set to Active</label>
          <type>button</type>
          <actionParams>moduleId=printFormExportTemplate&amp;entityName=PrintFormExportTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,23">
          <id>searchDeactivateDoc</id>
          <label>Set to Inactive</label>
          <type>button</type>
          <actionParams>moduleId=printFormExportTemplate&amp;entityName=PrintFormExportTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,24">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,25">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=printFormExportTemplate&amp;entityName=PrintFormExportTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,26">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=printFormExportTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,31">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>RefNo</type>
          <format/>
          <action>OpenPrintFormExportTemplateViewDocAction</action>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CL.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>applyTo:applyTo:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,32">
          <id>applyToModule</id>
          <label>Apply to Module</label>
          <type>Hidden</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>0</visibility>
          <mappedField>CED.MODULE</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,33">
          <id>PrintFormExportTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CPFET</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,34">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,35">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CPFET.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,36">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CPFET.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,37">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CPFET.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,38">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CPFET.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,39">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CPFET.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,printFormExportTemplateView,40">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CPFET.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupPrintFormView" position="printFormExportTemplate_view.xlsx,popupPrintFormView">
    <ViewDefinition advancedSearchId="" description="print form" id="popupPrintFormView" label="Select Print Forms" moduleId="printFormExportTemplate" position="printFormExportTemplate_view.xlsx,popupPrintFormView,1" queryId="listPrintForms" searchCriterion="">
      <elements id="options">
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>PrintForm</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,isCpmInitialized:isCpmInitialized:boolean,businessRefNo:businessReference:string,isLatest:isLatest:boolean,refNo:refNo:string,version:version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,22">
          <id>applyTo</id>
          <label>Module</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CPFET.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>applyTo:printFormExportTemplate.applyTo:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,23">
          <id>name</id>
          <label>Print Form Name</label>
          <type>Label</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CPF.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,24">
          <id>PrintFormExportTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPFET</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupPrintFormView,25">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popupExportTemplateView" position="printFormExportTemplate_view.xlsx,popupExportTemplateView">
    <ViewDefinition advancedSearchId="" description="export template" id="popupExportTemplateView" label="Select Export Templates" moduleId="printFormExportTemplate" position="printFormExportTemplate_view.xlsx,popupExportTemplateView,1" queryId="listExportTemplates" searchCriterion="">
      <elements id="options">
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ExportTemplate</value>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,isCpmInitialized:isCpmInitialized:boolean,businessRefNo:businessReference:string,isLatest:isLatest:boolean,refNo:refNo:string,version:version:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,22">
          <id>applyTo</id>
          <label>Module</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CPFET.APPLY_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>applyTo:printFormExportTemplate.applyTo:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,23">
          <id>name</id>
          <label>Export Template Name</label>
          <type>Label</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CET.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,24">
          <id>PrintFormExportTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPFET</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="printFormExportTemplate_view.xlsx,popupExportTemplateView,25">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
