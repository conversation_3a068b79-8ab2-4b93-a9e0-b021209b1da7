<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="inspectcheck" position="inspectcheck_form_security.xlsx">
  <sheet id="_system" position="inspectcheck_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="inspectcheck_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="inspectcheck_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/20</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="inspectcheck_form_security.xlsx,generalInfo">
    <GeneralInfo position="inspectcheck_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="inspectcheck_form_security.xlsx,condition">
    <ConditionList position="inspectcheck_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,condition,4">
          <conditionId>statusDraft</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,5">
          <conditionId>statusOfficial</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,6">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,7">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,8">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,9">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,10">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,11">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,12">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,13">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,14">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,15">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,16">
          <conditionId>isHeaderHclReadOnly</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,17">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="inspectcheck_form_security.xlsx,condition,18">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="inspectcheck_form_security.xlsx,default">
    <ActionConditionMatrix position="inspectcheck_form_security.xlsx,default,1">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,5">
          <actionId>editDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,6">
          <actionId>amendDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,7">
          <actionId>saveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,8">
          <actionId>baseSaveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,9">
          <actionId>saveAndConfirm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,10">
          <actionId>discardDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,11">
          <actionId>updateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,12">
          <actionId>draftStatus</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,13">
          <actionId>officialStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>disallowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,14">
          <actionId>copyDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,15">
          <actionId>activateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,16">
          <actionId>deactivateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,17">
          <actionId>cancelDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,18">
          <actionId>loadDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,19">
          <actionId>initializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,20">
          <actionId>customPrint01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,21">
          <actionId>customPrint02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,22">
          <actionId>customPrint03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,23">
          <actionId>customPrint04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,24">
          <actionId>customPrint05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,25">
          <actionId>customPrint06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,26">
          <actionId>customPrint07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,27">
          <actionId>customPrint08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,28">
          <actionId>customPrint09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,29">
          <actionId>customPrint10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,30">
          <actionId>customExport01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,31">
          <actionId>customExport02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,32">
          <actionId>customExport03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,33">
          <actionId>customExport04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,34">
          <actionId>customExport05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,35">
          <actionId>customExport06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,36">
          <actionId>customExport07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,37">
          <actionId>customExport08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,38">
          <actionId>customExport09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,39">
          <actionId>customExport10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,40">
          <actionId>inspectcheckCustom01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,41">
          <actionId>inspectcheckCustom02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,42">
          <actionId>inspectcheckCustom03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,43">
          <actionId>inspectcheckCustom04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,44">
          <actionId>inspectcheckCustom05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,45">
          <actionId>inspectcheckCustom06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,46">
          <actionId>inspectcheckCustom07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,47">
          <actionId>inspectcheckCustom08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,48">
          <actionId>inspectcheckCustom09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,49">
          <actionId>inspectcheckCustom10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,50">
          <actionId>markAsCustomStatus01Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,51">
          <actionId>markAsCustomStatus02Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,52">
          <actionId>markAsCustomStatus03Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,53">
          <actionId>markAsCustomStatus04Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,54">
          <actionId>markAsCustomStatus05Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,55">
          <actionId>markAsCustomStatus06Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,56">
          <actionId>markAsCustomStatus07Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,57">
          <actionId>markAsCustomStatus08Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,58">
          <actionId>markAsCustomStatus09Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,59">
          <actionId>markAsCustomStatus10Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,60">
          <actionId>reinitializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,61">
          <actionId>refreshCpmTemplate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,62">
          <actionId>refreshCpmPlanDate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusOfficial>allowed</statusOfficial>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="inspectcheck_form_security.xlsx,default,65">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,default,68">
          <componentId>ui</componentId>
          <statusDraft>editable</statusDraft>
          <statusOfficial>editable</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isHeaderHclReadOnly>editable</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,69">
          <componentId>ui.tabHeader.hierarchySection</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>readonly</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,70">
          <componentId>ui.inspectCheckLinkbar.approval</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,71">
          <componentId>ui.inspectCheckLinkbar.duplicateWindow</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,72">
          <componentId>ui.inspectCheckLinkbar.openForum</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,73">
          <componentId>ui.inspectCheckLinkbar.addToFavorites</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,74">
          <componentId>ui.inspectCheckLinkbar.followDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,75">
          <componentId>ui.inspectCheckLinkbar.unfollowDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,76">
          <componentId>ui.inspectCheckMenubar.printDoc</componentId>
          <statusDraft>hidden</statusDraft>
          <statusOfficial>hidden</statusOfficial>
          <docStatusActive>hidden</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>hidden</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,77">
          <componentId>ui.inspectCheckMenubar.editDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
        <element position="inspectcheck_form_security.xlsx,default,78">
          <componentId>ui.inspectCheckMenubar.amendDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusOfficial>inherit</statusOfficial>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="inspectcheck_form_security.xlsx,acl">
    <ActionRule position="inspectcheck_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,5">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,6">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,7">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,8">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,9">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,10">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,11">
          <actionId>updateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,12">
          <actionId>draftStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,13">
          <actionId>officialStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,14">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,15">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,16">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,17">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,18">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,19">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>has</inspectcheck.Author>
          <inspectcheck.Editor>has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,20">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,21">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,22">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,23">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,24">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,25">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,26">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,27">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,28">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,29">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,30">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,31">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,32">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,33">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,34">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,35">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,36">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,37">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,38">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,39">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,40">
          <actionId>inspectcheckCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,41">
          <actionId>inspectcheckCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,42">
          <actionId>inspectcheckCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,43">
          <actionId>inspectcheckCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,44">
          <actionId>inspectcheckCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,45">
          <actionId>inspectcheckCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,46">
          <actionId>inspectcheckCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,47">
          <actionId>inspectcheckCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,48">
          <actionId>inspectcheckCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,49">
          <actionId>inspectcheckCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,50">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,51">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,52">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,53">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,54">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,55">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,56">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,57">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,58">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,59">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,60">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,61">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,62">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <inspectcheck.Author>not-has</inspectcheck.Author>
          <inspectcheck.Editor>not-has</inspectcheck.Editor>
          <inspectcheck.ReadOnly>not-has</inspectcheck.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="inspectcheck_form_security.xlsx,acl,65">
      <elements id="default">
        <element position="inspectcheck_form_security.xlsx,acl,68">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,69">
          <componentId>ui.tabVendorPoShipmentItems</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,70">
          <componentId>ui.tabCosts</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,71">
          <componentId>ui.tabImagesAndAttachments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="inspectcheck_form_security.xlsx,acl,72">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="inspectcheck_form_security.xlsx,acl,75">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
