<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="domain" position="domain_view.xlsx">
  <sheet id="popDomainView" position="domain_view.xlsx,popDomainView">
    <ViewDefinition advancedSearchId="" description="Select Domain" id="popDomainView" label="Domain Lookup" moduleId="domain" position="domain_view.xlsx,popDomainView,1" queryId="listPopDomainView" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,popDomainView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="domain_view.xlsx,popDomainView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,popDomainView,10">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="domain_view.xlsx,popDomainView,11">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="domain_view.xlsx,popDomainView,19">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>DOM.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,popDomainView,20">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>DOM.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,popDomainView,21">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>DOM.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,popDomainView,22">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>DOM.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,popDomainView,23">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>DOM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,popDomainView,24">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,popDomainView,25">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>60px</width>
          <visibility>0</visibility>
          <mappedField>DOM.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainView" position="domain_view.xlsx,domainView">
    <ViewDefinition advancedSearchId="" description="Domain View" id="domainView" label="Domain" moduleId="domain" position="domain_view.xlsx,domainView,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="domain_view.xlsx,domainView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>Domain</value>
        </element>
        <element position="domain_view.xlsx,domainView,14">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(Any)</value>
        </element>
        <element position="domain_view.xlsx,domainView,15">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="domain_view.xlsx,domainView,16">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>businessRefNo:businessReference:string,refNo:refNo:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainView,20">
          <id>newDocs</id>
          <label>Create</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainView,21">
          <id>searchNewDomainFromRd1</id>
          <label>From RD1</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>newDocs</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,22">
          <id>searchNewDomainFromRd2</id>
          <label>From RD2</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>newDocs</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,23">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainView,24">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,25">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,26">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainView,27">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,28">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,29">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainView,30">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainView,31">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainView,36">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>id:id:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,37">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,38">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>parentPath:parentPath:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,39">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,40">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="domain_view.xlsx,domainView,41">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="domain_view.xlsx,domainView,42">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,43">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,44">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,45">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,46">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,47">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,48">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,49">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CD.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,50">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:docRef:string</esMapping>
        </element>
        <element position="domain_view.xlsx,domainView,51">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus01View" position="domain_view.xlsx,domainForCustomStatus01View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 01 View" id="domainForCustomStatus01View" label="Domain - Custom Status 01" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus01View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus01View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus01View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus01View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus01View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus02View" position="domain_view.xlsx,domainForCustomStatus02View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 02 View" id="domainForCustomStatus02View" label="Domain - Custom Status 02" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus02View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus02View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus02View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus02View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus02View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus03View" position="domain_view.xlsx,domainForCustomStatus03View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 03 View" id="domainForCustomStatus03View" label="Domain - Custom Status 03" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus03View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus03View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus03View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus03View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus03View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus04View" position="domain_view.xlsx,domainForCustomStatus04View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 04 View" id="domainForCustomStatus04View" label="Domain - Custom Status 04" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus04View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus04View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus04View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus04View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus04View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus05View" position="domain_view.xlsx,domainForCustomStatus05View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 05 View" id="domainForCustomStatus05View" label="Domain - Custom Status 05" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus05View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus05View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus05View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus05View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus05View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus06View" position="domain_view.xlsx,domainForCustomStatus06View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 06 View" id="domainForCustomStatus06View" label="Domain - Custom Status 06" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus06View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus06View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus06View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus06View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus06View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus07View" position="domain_view.xlsx,domainForCustomStatus07View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 07 View" id="domainForCustomStatus07View" label="Domain - Custom Status 07" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus07View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus07View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus07View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus07View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus07View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus08View" position="domain_view.xlsx,domainForCustomStatus08View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 08 View" id="domainForCustomStatus08View" label="Domain - Custom Status 08" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus08View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus08View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus08View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus08View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus08View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus09View" position="domain_view.xlsx,domainForCustomStatus09View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 09 View" id="domainForCustomStatus09View" label="Domain - Custom Status 09" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus09View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus09View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus09View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus09View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus09View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="domainForCustomStatus10View" position="domain_view.xlsx,domainForCustomStatus10View">
    <ViewDefinition advancedSearchId="" description="Domain Custom Status 10 View" id="domainForCustomStatus10View" label="Domain - Custom Status 10" moduleId="domain" position="domain_view.xlsx,domainForCustomStatus10View,1" queryId="listDomain" searchCriterion="">
      <elements id="options">
        <element position="domain_view.xlsx,domainForCustomStatus10View,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="domain_view.xlsx,domainForCustomStatus10View,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,20">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,21">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,22">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,23">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,24">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;entityName=Domain</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,25">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=domain&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="domain_view.xlsx,domainForCustomStatus10View,30">
          <id>id</id>
          <label>ID</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.ID</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,31">
          <id>name</id>
          <label>Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,32">
          <id>parentPath</id>
          <label>Parent</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>CD.PARENT_PATH</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,33">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>225px</width>
          <visibility>1</visibility>
          <mappedField>CD.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,34">
          <id>Domain</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width/>
          <visibility/>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,35">
          <id>Domain</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CD</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,37">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>1</visibility>
          <mappedField>CD.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,38">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,39">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,40">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,41">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>0</visibility>
          <mappedField>CD.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,42">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>CD.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,43">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CD.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="domain_view.xlsx,domainForCustomStatus10View,44">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>CD.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
