<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="correctiveActionPlans" position="correctiveActionPlans_view_security.xlsx">
  <sheet id="generalInfo" position="correctiveActionPlans_view_security.xlsx,generalInfo">
    <GeneralInfo position="correctiveActionPlans_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="correctiveActionPlans_view_security.xlsx,condition">
    <ConditionList position="correctiveActionPlans_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="correctiveActionPlans_view_security.xlsx,default">
    <ActionConditionMatrix position="correctiveActionPlans_view_security.xlsx,default,1">
      <elements id="default">
        <element position="correctiveActionPlans_view_security.xlsx,default,4">
          <actionId>searchNewDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,5">
          <actionId>searchActivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,6">
          <actionId>searchDeactivateDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,7">
          <actionId>searchCancelDoc</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,8">
          <actionId>searchViewAllExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,9">
          <actionId>searchViewCurExport</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,10">
          <actionId>openBatchUpdateWin</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,11">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,12">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,13">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,14">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,15">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,16">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,17">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,18">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,19">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,20">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,21">
          <actionId>searchCustomAction01</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,22">
          <actionId>searchCustomAction02</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,23">
          <actionId>searchCustomAction03</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,24">
          <actionId>searchCustomAction04</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,25">
          <actionId>searchCustomAction05</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,26">
          <actionId>searchCustomAction06</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,27">
          <actionId>searchCustomAction07</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,28">
          <actionId>searchCustomAction08</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,29">
          <actionId>searchCustomAction09</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,30">
          <actionId>searchCustomAction10</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,31">
          <actionId>openCpmMode</actionId>
          <ANY>allowed</ANY>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,default,32">
          <actionId>batchUpdateIssuesAndActions</actionId>
          <ANY>allowed</ANY>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="correctiveActionPlans_view_security.xlsx,default,35">
      <elements id="default"/>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="correctiveActionPlans_view_security.xlsx,acl">
    <ActionRule position="correctiveActionPlans_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="correctiveActionPlans_view_security.xlsx,acl,4">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,5">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,6">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,7">
          <actionId>searchCancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,8">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,9">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,10">
          <actionId>openBatchUpdateWin</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,11">
          <actionId>searchMarkAsCustomStatus01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,12">
          <actionId>searchMarkAsCustomStatus02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,13">
          <actionId>searchMarkAsCustomStatus03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,14">
          <actionId>searchMarkAsCustomStatus04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,15">
          <actionId>searchMarkAsCustomStatus05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,16">
          <actionId>searchMarkAsCustomStatus06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,17">
          <actionId>searchMarkAsCustomStatus07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,18">
          <actionId>searchMarkAsCustomStatus08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,19">
          <actionId>searchMarkAsCustomStatus09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,20">
          <actionId>searchMarkAsCustomStatus10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,21">
          <actionId>searchCustomAction01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,22">
          <actionId>searchCustomAction02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,23">
          <actionId>searchCustomAction03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,24">
          <actionId>searchCustomAction04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,25">
          <actionId>searchCustomAction05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,26">
          <actionId>searchCustomAction06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,27">
          <actionId>searchCustomAction07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,28">
          <actionId>searchCustomAction08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,29">
          <actionId>searchCustomAction09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,30">
          <actionId>searchCustomAction10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,31">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>not-has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>not-has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="correctiveActionPlans_view_security.xlsx,acl,32">
          <actionId>batchUpdateIssuesAndActions</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <correctiveActionPlans.Author>has</correctiveActionPlans.Author>
          <correctiveActionPlans.Editor>has</correctiveActionPlans.Editor>
          <correctiveActionPlans.ReadOnly>not-has</correctiveActionPlans.ReadOnly>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="correctiveActionPlans_view_security.xlsx,acl,35">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="correctiveActionPlans_view_security.xlsx,acl,40">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
