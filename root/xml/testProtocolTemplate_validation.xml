<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<validation module="testProtocolTemplate" position="testProtocolTemplate_validation.xlsx">
  <sheet id="ValidationProfile" position="testProtocolTemplate_validation.xlsx,ValidationProfile">
    <ValidationProfile position="testProtocolTemplate_validation.xlsx,ValidationProfile,1">
      <elements id="default">
        <element position="testProtocolTemplate_validation.xlsx,ValidationProfile,4">
          <id>testProtocolTemplateProfileID</id>
          <profileName>Default Data Validation Profile TestProtocolTemplate[ver:1]</profileName>
          <inheritFrom/>
          <entityName>TestProtocolTemplate</entityName>
          <entityVer>1</entityVer>
          <action>SaveDoc,SaveAndConfirm,MarkAsCustomStatus01Doc,MarkAsCustomStatus02Doc,MarkAsCustomStatus03Doc,Mark<PERSON><PERSON>ustom<PERSON>tatus04Doc,Mark<PERSON><PERSON>ustomStatus05Doc,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tatus06Doc,MarkAsCustomStatus07Doc,<PERSON><PERSON><PERSON><PERSON><PERSON>Status08Doc,MarkAsCustomStatus09Doc,MarkAsCustomStatus10Doc</action>
          <condition/>
          <priority/>
          <maxError/>
          <consolidateError/>
          <ignoreCustomField/>
          <enabled>Y</enabled>
          <remarks/>
          <updatedOn>15-十二月-2023</updatedOn>
        </element>
      </elements>
    </ValidationProfile>
  </sheet>
  <sheet id="ValidationRule" position="testProtocolTemplate_validation.xlsx,ValidationRule">
    <ValidationRule position="testProtocolTemplate_validation.xlsx,ValidationRule,1">
      <elements id="default">
        <element position="testProtocolTemplate_validation.xlsx,ValidationRule,4">
          <type>MandatoryValidator</type>
          <className>com.core.cbx.validation.validator.MandatoryValidator</className>
          <restapiBeanName>MandatoryValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
        <element position="testProtocolTemplate_validation.xlsx,ValidationRule,5">
          <type>UniqueInModuleValidator</type>
          <className>com.core.cbx.validation.validator.UniqueInModuleValidator</className>
          <restapiBeanName>UniqueInModuleValidator</restapiBeanName>
          <condition/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationRule>
  </sheet>
  <sheet id="MandatoryValidator" position="testProtocolTemplate_validation.xlsx,MandatoryValidator">
    <ValidationField position="testProtocolTemplate_validation.xlsx,MandatoryValidator,1" profileId="testProtocolTemplateProfileID" profileName="">
      <elements id="default">
        <element position="testProtocolTemplate_validation.xlsx,MandatoryValidator,8">
          <entityName>TestProtocolTemplate</entityName>
          <fieldId>name</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID/>
          <LABEL_FIELD_ID>name</LABEL_FIELD_ID>
        </element>
        <element position="testProtocolTemplate_validation.xlsx,MandatoryValidator,9">
          <entityName>TestProtocolTemplateDetail</entityName>
          <fieldId>testResultType</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
          <GRID_ID>testProtocolTemplateDetails</GRID_ID>
          <LABEL_FIELD_ID>testResultType</LABEL_FIELD_ID>
        </element>
        <element position="testProtocolTemplate_validation.xlsx,MandatoryValidator,10">
          <entityName>TestProtocolTemplateDetail</entityName>
          <fieldId>minimumTolerance</fieldId>
          <condition>isAutomatedTestResultType</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>testProtocolTemplateDetails</GRID_ID>
          <LABEL_FIELD_ID>minimumTolerance</LABEL_FIELD_ID>
        </element>
        <element position="testProtocolTemplate_validation.xlsx,MandatoryValidator,11">
          <entityName>TestProtocolTemplateDetail</entityName>
          <fieldId>maxiumumTolerance</fieldId>
          <condition>isAutomatedTestResultType</condition>
          <conditionType>1</conditionType>
          <enabled>Y</enabled>
          <GRID_ID>testProtocolTemplateDetails</GRID_ID>
          <LABEL_FIELD_ID>maxiumumTolerance</LABEL_FIELD_ID>
        </element>
      </elements>
    </ValidationField>
  </sheet>
  <sheet id="UniqueInModuleValidator" position="testProtocolTemplate_validation.xlsx,UniqueInModuleValidator">
    <ValidationField position="testProtocolTemplate_validation.xlsx,UniqueInModuleValidator,1" profileId="testProtocolTemplateProfileID" profileName="">
      <elements id="default">
        <element position="testProtocolTemplate_validation.xlsx,UniqueInModuleValidator,8">
          <entityName>TestProtocolTemplate</entityName>
          <fieldId>name</fieldId>
          <condition/>
          <conditionType/>
          <enabled>Y</enabled>
        </element>
      </elements>
    </ValidationField>
  </sheet>
</validation>
