<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form module="inspectReport" position="inspectReport_form.xlsx">
  <sheet id="_system" position="inspectReport_form.xlsx,_system">
    <ProjectInfo client="cnt" position="inspectReport_form.xlsx,_system,1" project="base" releaseNo="1.0a"/>
    <ProductVersion position="inspectReport_form.xlsx,_system,7">
      <elements id="default">
        <element position="inspectReport_form.xlsx,_system,10">
          <updatedOn>18-五月-2021</updatedOn>
          <summary>Creation</summary>
          <releaseNo>5.12</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="form" position="inspectReport_form.xlsx,form">
    <Form id="inspectReportForm" label="Inspection Report" module="inspectReport" position="inspectReport_form.xlsx,form,1" version="1"/>
    <TabGroup id="inspectReportTabGroup" label="" position="inspectReport_form.xlsx,form,7">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,14">
          <id>tabHeader</id>
          <label>Header</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,15">
          <id>tabParties</id>
          <label>Parties</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,16">
          <id>tabInspectReportItems</id>
          <label>Shipment Items</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,17">
          <id>tabDetails</id>
          <label>Defect Details</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,18">
          <id>tabMeasurement</id>
          <label>Measurement</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,19">
          <id>tabChecklist</id>
          <label>Checklist</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,20">
          <id>tabEanCodeReport</id>
          <label>EAN Code Report</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,21">
          <id>tabProductionStatus</id>
          <label>Production Status</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,22">
          <id>tabCartonDetailList</id>
          <label>Carton Detail List</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,23">
          <id>[Blank]</id>
          <label/>
          <type>EmptyGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,form,24">
          <id>tabCosts</id>
          <label>Costs</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,25">
          <id>tabMobileActivities</id>
          <label>Mobile Activities</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,26">
          <id>tabImages</id>
          <label>Images &amp; Attachments</label>
          <type>Tab</type>
        </element>
        <element position="inspectReport_form.xlsx,form,27">
          <id>tabOther</id>
          <label>Other</label>
          <type>Tab</type>
        </element>
      </elements>
    </TabGroup>
    <Toolbar id="inspectReportToolbar" label="" position="inspectReport_form.xlsx,form,30">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,37">
          <id>inspectReportMenubar</id>
          <label/>
          <type>Menubar</type>
        </element>
      </elements>
    </Toolbar>
    <Menubar align="left" cssClass="cbx-qcchecklisttemplateMenubar" id="inspectReportMenubar" label="" position="inspectReport_form.xlsx,form,40">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,47">
          <id>createGroup</id>
          <label>Create</label>
          <type>MenuGroup</type>
          <action>createGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,48">
          <id>editDoc</id>
          <label>Edit</label>
          <type>MenuItem</type>
          <action>EditDocAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,49">
          <id>amendDoc</id>
          <label>Amend</label>
          <type>MenuItem</type>
          <action>AmendDocAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,50">
          <id>saveDoc</id>
          <label>Save</label>
          <type>MenuItem</type>
          <action>BaseSaveDocAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,51">
          <id>saveAndConfirm</id>
          <label>Save &amp; Confirm</label>
          <type>MenuItem</type>
          <action>SaveAndConfirmAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,52">
          <id>discarddoc</id>
          <label>Cancel</label>
          <type>MenuItem</type>
          <action>DiscardDocAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,53">
          <id>printGroup</id>
          <label>Print</label>
          <type>MenuGroup</type>
          <action>printGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,54">
          <id>exportGroup</id>
          <label>Export</label>
          <type>MenuGroup</type>
          <action>exportGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,55">
          <id>actionsGroup</id>
          <label>Actions</label>
          <type>MenuGroup</type>
          <action>actionsGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,56">
          <id>initializeCpm</id>
          <label>Initialize CPM</label>
          <type>MenuItem</type>
          <action>InitializeCpmAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,57">
          <id>markAsGroup</id>
          <label>Mark as</label>
          <type>MenuGroup</type>
          <action>markAsGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,58">
          <id>moreGroup</id>
          <label>More</label>
          <type>MenuGroup</type>
          <action>moreGroup</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,59">
          <id>inspectReportViewCapa</id>
          <label>View CAPA</label>
          <type>MenuItem</type>
          <action>InspectReportViewCapaAction</action>
          <actionParams>viewName=correctiveActionPlansActiveView&amp;naviId=quality&amp;field=inspectReportItems.itemRef_vendorRef_factoryRef:orNull&amp;colunmId=itemRef_vendorCode_factCode</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,form,60">
          <id>sendToVendor</id>
          <label>Send to Vendor</label>
          <type>MenuItem</type>
          <action>SendToVendorAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,form,61">
          <id>sendToBuyer</id>
          <label>Send to Buyer</label>
          <type>MenuItem</type>
          <action>SendToBuyerAction</action>
          <actionParams/>
        </element>
      </elements>
    </Menubar>
    <Header position="inspectReport_form.xlsx,form,64">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,67">
          <id>docStatus</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>inactive:(inactive),active:,canceled:(canceled)</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxKeyValueLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,68">
          <id>headerName</id>
          <label/>
          <type>Label</type>
          <align>left</align>
          <mapping/>
          <format>{inspectReportNo} - {inspectTypeName} - {inspectFormatName}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength>150</maxLength>
        </element>
        <element position="inspectReport_form.xlsx,form,69">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,70">
          <id>version</id>
          <label>Version</label>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format>{version}({editingStatus})</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel/>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,71">
          <id>headerIntegration</id>
          <label/>
          <type>Label</type>
          <align>right</align>
          <mapping/>
          <format/>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxIntegrationLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,72">
          <id>inspectReportLinkbar</id>
          <label/>
          <type>Linkbar</type>
          <align/>
          <mapping/>
          <format/>
          <labelRenderer/>
          <hideLabel/>
          <maxLength/>
        </element>
      </elements>
    </Header>
    <Footer position="inspectReport_form.xlsx,form,75">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,78">
          <id>createUser</id>
          <label/>
          <type>Label</type>
          <format>Created by: {createUser} on: {createdOn}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength>100</maxLength>
        </element>
        <element position="inspectReport_form.xlsx,form,79">
          <id>[BLANK]</id>
          <label/>
          <type>Label</type>
          <format/>
          <labelRenderer/>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,80">
          <id>updateUser</id>
          <label/>
          <type>Label</type>
          <format>Last Modified by: {updateUser} on: {updatedOn}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength>100</maxLength>
        </element>
        <element position="inspectReport_form.xlsx,form,81">
          <id>[BLANK]</id>
          <label/>
          <type>Label</type>
          <format/>
          <labelRenderer/>
          <hideLabel>TRUE</hideLabel>
          <maxLength/>
        </element>
        <element position="inspectReport_form.xlsx,form,82">
          <id>name</id>
          <label/>
          <type>Label</type>
          <format>System Ref. No.:{name}</format>
          <labelRenderer>com.core.cbx.ui.zk.cul.renderer.CbxPatternLabelRenderer</labelRenderer>
          <hideLabel>TRUE</hideLabel>
          <maxLength>100</maxLength>
        </element>
      </elements>
    </Footer>
    <DropdownStores position="inspectReport_form.xlsx,form,85">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,88">
          <id>selectSizeStore</id>
          <label/>
          <action>GetSelectSizeStoreAction</action>
          <actionParams>field=inspectReportMeasSizeResults&amp;targetField=specSize&amp;targetValue=SIZE</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,89">
          <id>inspectionLevelStore</id>
          <label/>
          <action>LoadInspectionLevelDDStoreUIAction</action>
          <actionParams>field=inspectionLevel&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,90">
          <id>criticalLevelStore</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=criticalLevel&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,91">
          <id>majorLevelStore</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=majorLevel&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,92">
          <id>minorLevelStore</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=minorLevel&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,93">
          <id>measInspection</id>
          <label/>
          <action>LoadInspectionLevelDDStoreUIAction</action>
          <actionParams>field=measSamplingRuleSection&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,94">
          <id>measCritical</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=measSamplingRuleSection&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,95">
          <id>measMajor</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=measSamplingRuleSection&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
        <element position="inspectReport_form.xlsx,form,96">
          <id>measMinor</id>
          <label/>
          <action>LoadAQLDDStoreUIAction</action>
          <actionParams>field=measSamplingRuleSection&amp;disabledFieldId=isNotAvailable</actionParams>
          <lazy/>
          <reload/>
        </element>
      </elements>
    </DropdownStores>
    <Linkbar align="right" cssClass="" id="inspectReportLinkbar" label="" position="inspectReport_form.xlsx,form,99">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,106">
          <id>duplicateWindow</id>
          <label>Duplicate Window</label>
          <action/>
          <actionParams/>
          <rendererClass/>
          <image>duplicateWindow.png</image>
        </element>
        <element position="inspectReport_form.xlsx,form,107">
          <id>followDoc</id>
          <label>Follow</label>
          <action>FollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>follow.png</image>
        </element>
        <element position="inspectReport_form.xlsx,form,108">
          <id>unfollowDoc</id>
          <label>Unfollow</label>
          <action>UnfollowDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>unfollow.png</image>
        </element>
        <element position="inspectReport_form.xlsx,form,109">
          <id>addToFavorites</id>
          <label>Add to Favorites</label>
          <action>AddDocToFavoriteAction</action>
          <actionParams/>
          <rendererClass/>
          <image>favorites.png</image>
        </element>
        <element position="inspectReport_form.xlsx,form,110">
          <id>approval</id>
          <label>Approval</label>
          <action>OpenApprovalByDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>approval.gif</image>
        </element>
        <element position="inspectReport_form.xlsx,form,111">
          <id>relatedActivities</id>
          <label>Related Activities</label>
          <action>ShowRelatedDocAction</action>
          <actionParams/>
          <rendererClass/>
          <image>activities.png</image>
        </element>
      </elements>
    </Linkbar>
    <MenuGroup id="actionsGroup" label="Actions" position="inspectReport_form.xlsx,form,114">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,121">
          <id>copyDoc</id>
          <label>Copy</label>
          <type>MenuItem</type>
          <action>CopyDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,122">
          <id>[BLANK]</id>
          <label/>
          <type>MenuSeparator</type>
          <action/>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,123">
          <id>activateDoc</id>
          <label>Active</label>
          <type>MenuItem</type>
          <action>ActivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,124">
          <id>deactivateDoc</id>
          <label>Inactive</label>
          <type>MenuItem</type>
          <action>DeactivateDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,125">
          <id>cancelDoc</id>
          <label>Canceled</label>
          <type>MenuItem</type>
          <action>CancelDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="printGroup" label="Print" position="inspectReport_form.xlsx,form,128">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,135">
          <id>customPrint01</id>
          <label>Custom Print 01</label>
          <type>MenuItem</type>
          <action>CustomPrint01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,136">
          <id>customPrint02</id>
          <label>Custom Print 02</label>
          <type>MenuItem</type>
          <action>CustomPrint02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,137">
          <id>customPrint03</id>
          <label>Custom Print 03</label>
          <type>MenuItem</type>
          <action>CustomPrint03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,138">
          <id>customPrint04</id>
          <label>Custom Print 04</label>
          <type>MenuItem</type>
          <action>CustomPrint04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,139">
          <id>customPrint05</id>
          <label>Custom Print 05</label>
          <type>MenuItem</type>
          <action>CustomPrint05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,140">
          <id>customPrint06</id>
          <label>Custom Print 06</label>
          <type>MenuItem</type>
          <action>CustomPrint06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,141">
          <id>customPrint07</id>
          <label>Custom Print 07</label>
          <type>MenuItem</type>
          <action>CustomPrint07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,142">
          <id>customPrint08</id>
          <label>Custom Print 08</label>
          <type>MenuItem</type>
          <action>CustomPrint08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,143">
          <id>customPrint09</id>
          <label>Custom Print 09</label>
          <type>MenuItem</type>
          <action>CustomPrint09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,144">
          <id>customPrint10</id>
          <label>Custom Print 10</label>
          <type>MenuItem</type>
          <action>CustomPrint10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="exportGroup" label="Export" position="inspectReport_form.xlsx,form,147">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,154">
          <id>customExport01</id>
          <label>Custom Export 01</label>
          <type>MenuItem</type>
          <action>CustomExport01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,155">
          <id>customExport02</id>
          <label>Custom Export 02</label>
          <type>MenuItem</type>
          <action>CustomExport02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,156">
          <id>customExport03</id>
          <label>Custom Export 03</label>
          <type>MenuItem</type>
          <action>CustomExport03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,157">
          <id>customExport04</id>
          <label>Custom Export 04</label>
          <type>MenuItem</type>
          <action>CustomExport04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,158">
          <id>customExport05</id>
          <label>Custom Export 05</label>
          <type>MenuItem</type>
          <action>CustomExport05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,159">
          <id>customExport06</id>
          <label>Custom Export 06</label>
          <type>MenuItem</type>
          <action>CustomExport06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,160">
          <id>customExport07</id>
          <label>Custom Export 07</label>
          <type>MenuItem</type>
          <action>CustomExport07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,161">
          <id>customExport08</id>
          <label>Custom Export 08</label>
          <type>MenuItem</type>
          <action>CustomExport08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,162">
          <id>customExport09</id>
          <label>Custom Export 09</label>
          <type>MenuItem</type>
          <action>CustomExport09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,form,163">
          <id>customExport10</id>
          <label>Custom Export 10</label>
          <type>MenuItem</type>
          <action>CustomExport10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
          <readonly/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="markAsGroup" label="Mark as" position="inspectReport_form.xlsx,form,166">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,173">
          <id>draftStatus</id>
          <label>Draft</label>
          <type>MenuItem</type>
          <action>DraftStatusAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,174">
          <id>officialStatus</id>
          <label>Official</label>
          <type>MenuItem</type>
          <action>OfficialStatusAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,175">
          <id>markAsCustomStatus01Doc</id>
          <label>Custom Status 1</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus01DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,176">
          <id>markAsCustomStatus02Doc</id>
          <label>Custom Status 2</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus02DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,177">
          <id>markAsCustomStatus03Doc</id>
          <label>Custom Status 3</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus03DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,178">
          <id>markAsCustomStatus04Doc</id>
          <label>Custom Status 4</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus04DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,179">
          <id>markAsCustomStatus05Doc</id>
          <label>Custom Status 5</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus05DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,180">
          <id>markAsCustomStatus06Doc</id>
          <label>Custom Status 6</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus06DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,181">
          <id>markAsCustomStatus07Doc</id>
          <label>Custom Status 7</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus07DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,182">
          <id>markAsCustomStatus08Doc</id>
          <label>Custom Status 8</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus08DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,183">
          <id>markAsCustomStatus09Doc</id>
          <label>Custom Status 9</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus09DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,184">
          <id>markAsCustomStatus10Doc</id>
          <label>Custom Status 10</label>
          <type>MenuItem</type>
          <action>MarkAsCustomStatus10DocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="createGroup" label="Create" position="inspectReport_form.xlsx,form,187">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,194">
          <id>inspectReport</id>
          <label>New Inspection Report</label>
          <type>MenuItem</type>
          <action>NewDocAction</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
    <MenuGroup id="moreGroup" label="More" position="inspectReport_form.xlsx,form,197">
      <elements id="default">
        <element position="inspectReport_form.xlsx,form,204">
          <id>customDocAction01</id>
          <label>Custom Action 1</label>
          <type>MenuItem</type>
          <action>InspectReportCustom01Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,205">
          <id>customDocAction02</id>
          <label>Custom Action 2</label>
          <type>MenuItem</type>
          <action>InspectReportCustom02Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,206">
          <id>customDocAction03</id>
          <label>Custom Action 3</label>
          <type>MenuItem</type>
          <action>InspectReportCustom03Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,207">
          <id>customDocAction04</id>
          <label>Custom Action 4</label>
          <type>MenuItem</type>
          <action>InspectReportCustom04Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,208">
          <id>customDocAction05</id>
          <label>Custom Action 5</label>
          <type>MenuItem</type>
          <action>InspectReportCustom05Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,209">
          <id>customDocAction06</id>
          <label>Custom Action 6</label>
          <type>MenuItem</type>
          <action>InspectReportCustom06Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,210">
          <id>customDocAction07</id>
          <label>Custom Action 7</label>
          <type>MenuItem</type>
          <action>InspectReportCustom07Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,211">
          <id>customDocAction08</id>
          <label>Custom Action 8</label>
          <type>MenuItem</type>
          <action>InspectReportCustom08Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,212">
          <id>customDocAction09</id>
          <label>Custom Action 9</label>
          <type>MenuItem</type>
          <action>InspectReportCustom09Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,form,213">
          <id>customDocAction10</id>
          <label>Custom Action 10</label>
          <type>MenuItem</type>
          <action>InspectReportCustom10Action</action>
          <actionParams/>
          <cascade/>
          <cascadeBy/>
          <maxLength/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <data/>
          <dataFrom/>
          <size/>
          <mandatory/>
          <prefix/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <format/>
          <tabIndex/>
          <hideLabel/>
          <fileNum/>
          <fileSize/>
          <fileAllow/>
          <enableVersion/>
          <keptVersion/>
          <showHint/>
          <popupFormat/>
          <popupWinId/>
          <limit/>
          <labelRenderer/>
          <defaultValue/>
          <multiLine/>
          <viewName/>
          <single/>
          <filterBy/>
        </element>
      </elements>
    </MenuGroup>
  </sheet>
  <sheet id="tabHeader" position="inspectReport_form.xlsx,tabHeader">
    <Tab id="tabHeader" label="Header" position="inspectReport_form.xlsx,tabHeader,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabHeader,8">
          <id>generalInfoSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,9">
          <id>aqlInfoSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,10">
          <id>signatureSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,11">
          <id>inspectResultSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,12">
          <id>measSamplingRuleSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,13">
          <id>factorySignatureSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,14">
          <id>defectSummarySection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,15">
          <id>hierarchySection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,16">
          <id>measurementResultSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,17">
          <id>classificationSection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,18">
          <id>inspectReportChecklistSummarySection</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,19">
          <id>sysCustFields</id>
          <type>SysCustGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,20">
          <id>inspectReportChecklistSummary</id>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,21">
          <id>defectSummary01</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,22">
          <id>defectSummary02</id>
          <type>Section</type>
        </element>
      </elements>
    </Tab>
    <Section id="generalInfoSection" label="General Information" position="inspectReport_form.xlsx,tabHeader,25" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,35">
          <id>inspectReportNo</id>
          <label>Inspection Report No.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,36">
          <id>shortDescription</id>
          <label>Short Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,37">
          <id>instructions</id>
          <label>Notes / Instructions</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,38">
          <id>inspectBookingNo</id>
          <label>Inspection Booking No</label>
          <type>Hyperlink</type>
          <data/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=inspectBooking&amp;fieldId=inspectBooking&amp;closeAllPopup=false</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,39">
          <id>plannedInspectDate</id>
          <label>Planned Inspection Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,40">
          <id>inspectDate</id>
          <label>Inspection Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,41">
          <id>reportDate</id>
          <label>Report Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,42">
          <id>inspectType</id>
          <label>Inspection Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,43">
          <id>inspectFormat</id>
          <label>Inspection Format</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,44">
          <id>estimatedEffort</id>
          <label>Estimated Effort (Hours)</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,45">
          <id>actualEffort</id>
          <label>Actual Effort (Hours)</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,46">
          <id>inspectionParty</id>
          <label>Inspection Party</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,47">
          <id>inspectionOffice</id>
          <label>Inspection Office</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Section id="aqlInfoSection" label="Sampling Rule for Defect Details" position="inspectReport_form.xlsx,tabHeader,50" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,60">
          <id>reportTemplate</id>
          <label>Inspection Report Template</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <comboKey/>
          <viewName>popInspectReportTemplateView</viewName>
          <viewParams/>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <single>TRUE</single>
          <readonly/>
          <winTitle>Inspection Report Template Lookup</winTitle>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,61">
          <id>inspectionLevel</id>
          <label>Inspection Level</label>
          <type>Dropdown</type>
          <data>inspectionLevelStore</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custText1}</format>
          <comboKey>custText1</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,62">
          <id>aqlPendulum</id>
          <label>Inspection Method</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,63">
          <id>criticalLevel</id>
          <label>AQL (Critical)</label>
          <type>Dropdown</type>
          <data>criticalLevelStore</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,64">
          <id>majorLevel</id>
          <label>AQL (Major)</label>
          <type>Dropdown</type>
          <data>majorLevelStore</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,65">
          <id>minorLevel</id>
          <label>AQL (Minor)</label>
          <type>Dropdown</type>
          <data>minorLevelStore</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,66">
          <id>defQtyLevel</id>
          <label>Defective Quantity Level</label>
          <type>Dropdown</type>
          <data>defQtyLevelStore</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,67">
          <id>totalActualQty</id>
          <label>Total Actual Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly>TRUE</readonly>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,68">
          <id>totalSampleSize</id>
          <label>Calculated Sample Size</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly>TRUE</readonly>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,69">
          <id>actualSampleSize</id>
          <label>Actual Sample Size</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
      </elements>
    </Section>
    <Section id="measSamplingRuleSection" label="Sampling Rule for Measurement" position="inspectReport_form.xlsx,tabHeader,72" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,82">
          <id>measInspection</id>
          <label>Inspection Level</label>
          <type>Dropdown</type>
          <data>measInspection</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custText1}</format>
          <comboKey>custText1</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,83">
          <id>measCritical</id>
          <label>AQL (Critical)</label>
          <type>Dropdown</type>
          <data>measCritical</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,84">
          <id>measMajor</id>
          <label>AQL (Major)</label>
          <type>Dropdown</type>
          <data>measMajor</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,85">
          <id>measMinor</id>
          <label>AQL (Minor)</label>
          <type>Dropdown</type>
          <data>measMinor</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{custStr}</format>
          <comboKey>custStr</comboKey>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,86">
          <id>measCalculatedSampleSize</id>
          <label>Calculated Sample Size</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly>TRUE</readonly>
          <winTitle/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,87">
          <id>measActualSampleSize</id>
          <label>Actual Sample Size</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <readonly/>
          <winTitle/>
        </element>
      </elements>
    </Section>
    <Section id="inspectResultSection" label="Inspection Summary" position="inspectReport_form.xlsx,tabHeader,90" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,100">
          <id>inspectResult</id>
          <label>Overall Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,101">
          <id>inspectFailReason</id>
          <label>Fail Reason</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,102">
          <id>shipmentReleased</id>
          <label>Shipment Released</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,103">
          <id>inspectComments</id>
          <label>Overall Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
      </elements>
    </Section>
    <Section id="defectSummarySection" label="Defect Summary" position="inspectReport_form.xlsx,tabHeader,106" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,116">
          <id>defectResult</id>
          <label>Defect Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,117">
          <id>defectComments</id>
          <label>Defect Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,118">
          <id>column2</id>
          <label/>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel>TRUE</hideLabel>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,119">
          <id>criticalCount</id>
          <label>Critical</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,120">
          <id>majorCount</id>
          <label>Major</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,121">
          <id>minorCount</id>
          <label>Minor</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <hideLabel/>
        </element>
      </elements>
    </Section>
    <Section id="defectSummary01" label="Defect Summary 01" position="inspectReport_form.xlsx,tabHeader,124" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields"/>
    </Section>
    <Section id="defectSummary02" label="Defect Summary 02" position="inspectReport_form.xlsx,tabHeader,136" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields"/>
    </Section>
    <Section id="measurementResultSection" label="Measurement Summary" position="inspectReport_form.xlsx,tabHeader,148" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,158">
          <id>measResult</id>
          <label>Measurement Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,159">
          <id>comments</id>
          <label>Measurement Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,160">
          <id>totalMeasuredQty</id>
          <label>Total Measured Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,161">
          <id>totalMeasuredFailedQty</id>
          <label>Total Failed Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,162">
          <id>column3</id>
          <label/>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,163">
          <id>measurementCriticalCount</id>
          <label>Critical</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,164">
          <id>measurementMajorCount</id>
          <label>Major</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,165">
          <id>measurementMinorCount</id>
          <label>Minor</label>
          <type>Anchor</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Section id="hierarchySection" label="Hierarchy" position="inspectReport_form.xlsx,tabHeader,168" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,178">
          <id>hierarchy</id>
          <label>Hierarchy</label>
          <type>HclGroup</type>
          <data>PRODUCT_HIERARCHY</data>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <disableSSL>TRUE</disableSSL>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <Section id="classificationSection" label="Classification" position="inspectReport_form.xlsx,tabHeader,181" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,191">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <format>{name}</format>
          <readonly>TRUE</readonly>
          <viewName>popCodelistView</viewName>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{name}</popupFormat>
          <extraParams>Selection_preOpenPopupAction=ConstructViewParamsForClassificationAction</extraParams>
        </element>
      </elements>
    </Section>
    <Section id="signatureSection" label="Inspector Signature" position="inspectReport_form.xlsx,tabHeader,194" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,204">
          <id>inspectors</id>
          <label>Inspector(s)</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{userName}</format>
          <comboKey/>
          <viewName>lookupUserView</viewName>
          <viewParams>groupName='Inspectors'</viewParams>
          <allowDateFilter>FALSE</allowDateFilter>
          <popupFormat>{userName}</popupFormat>
          <single>FALSE</single>
          <winTitle>User Lookup</winTitle>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,205">
          <id>inspectorSignature</id>
          <label>Inspector Signature</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,206">
          <id>inspectorSignatureDate</id>
          <label>Inspector Sign Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,207">
          <id>inspectorSignatureLocation</id>
          <label>Sign Location</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <Section id="factorySignatureSection" label="Factory Signature" position="inspectReport_form.xlsx,tabHeader,210" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,220">
          <id>factorySignature</id>
          <label>Factory Signature</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,221">
          <id>factorySignatureDate</id>
          <label>Factory Sign Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,222">
          <id>factorySignatureLocation</id>
          <label>Sign Location</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <Section id="inspectReportChecklistSummarySection" label="Checklist Summary" position="inspectReport_form.xlsx,tabHeader,225" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabHeader,235">
          <id>checklistResult</id>
          <label>Checklist Result</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,236">
          <id>zeroToleranceFailure</id>
          <label>Zero Tolerance Failure</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,237">
          <id>checklistComments</id>
          <label>Checklist Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,238">
          <id>totalStatusScore</id>
          <label>Status Score (Total)</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,239">
          <id>totalMaximumScore</id>
          <label>Maximum Score (Total)</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,240">
          <id>overallScore</id>
          <label>Overall Score (%)</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,241">
          <id>overallRating</id>
          <label>Overall Rating</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <SysCustGroup id="sysCustFields" label="Additional Information" position="inspectReport_form.xlsx,tabHeader,244" type="SysCustGroup"/>
    <Grid entityName="InspectReportChecklistSummary" id="inspectReportChecklistSummary" label="Checklist Summary" position="inspectReport_form.xlsx,tabHeader,250" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabHeader,260">
          <id>result</id>
          <label>Label</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <winTitle/>
          <readonly>TRUE</readonly>
          <sorting>asc</sorting>
          <sortingIndex>1</sortingIndex>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,261">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <winTitle/>
          <readonly>TRUE</readonly>
          <sorting>asc</sorting>
          <sortingIndex>2</sortingIndex>
        </element>
        <element position="inspectReport_form.xlsx,tabHeader,262">
          <id>count</id>
          <label>Total Count</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <viewName/>
          <labelRenderer/>
          <viewParams/>
          <winTitle/>
          <readonly>TRUE</readonly>
          <sorting/>
          <sortingIndex/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabParties" position="inspectReport_form.xlsx,tabParties">
    <Tab id="tabParties" label="Parties" position="inspectReport_form.xlsx,tabParties,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabParties,8">
          <id>vendorInfoSection</id>
          <label>Vendor Information</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,9">
          <id>factoryInfoSection</id>
          <label>Site Information</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,10">
          <id>customerInfoSection</id>
          <label>Customer Information</label>
          <type>Section</type>
        </element>
      </elements>
    </Tab>
    <Section id="factoryInfoSection" label="Site Information" position="inspectReport_form.xlsx,tabParties,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabParties,23">
          <id>factory</id>
          <label>Factory Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{businessName}</format>
          <comboKey/>
          <viewName>popFactView</viewName>
          <viewParams>vendorId={vendor.id}</viewParams>
          <allowDateFilter/>
          <popupFormat>{businessName}</popupFormat>
          <single>TRUE</single>
          <winTitle>Factory Lookup</winTitle>
          <cascadeExpr>vendor</cascadeExpr>
          <cascadeLabelKey>lbl.message.common.noSelectVendor</cascadeLabelKey>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,24">
          <id>factoryCode</id>
          <label>Factory ID</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>factory.factCode</mapping>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,25">
          <id>factoryContactName</id>
          <label>Contact Person</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,26">
          <id>factoryContactEmail</id>
          <label>Contact Email(s)</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,27">
          <id>factoryContactTelNo</id>
          <label>Contact Phone</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,28">
          <id>factoryGpsLng</id>
          <label>GPS Coordinate Lng</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,29">
          <id>factoryGpsLat</id>
          <label>GPS Coordinate Lat</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,30">
          <id>address</id>
          <label>Address</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>address</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{address1}</format>
          <comboKey/>
          <viewName>popLookupFactAddressView</viewName>
          <viewParams>factRef={factId.refNo}&amp;factVersion={factId.version}&amp;domainId={factId.domainId}</viewParams>
          <allowDateFilter/>
          <popupFormat/>
          <single>TRUE</single>
          <winTitle>Inspection Report Address Lookup by Factory</winTitle>
          <cascadeExpr>factory</cascadeExpr>
          <cascadeLabelKey/>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,31">
          <id>factoryAddress2</id>
          <label/>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,32">
          <id>factoryAddress3</id>
          <label/>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,33">
          <id>factoryAddress4</id>
          <label/>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,34">
          <id>factoryCity</id>
          <label>Town / City</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,35">
          <id>factoryState</id>
          <label>State / Province</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,36">
          <id>factoryCountry</id>
          <label>Country</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
      </elements>
    </Section>
    <Section id="vendorInfoSection" label="Vendor Information" position="inspectReport_form.xlsx,tabParties,39" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabParties,49">
          <id>vendor</id>
          <label>Vendor Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{businessName}</format>
          <comboKey/>
          <viewName>popVendorView</viewName>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat>{businessName}</popupFormat>
          <single>TRUE</single>
          <winTitle>Vendor Lookup</winTitle>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,50">
          <id>vendorCode</id>
          <label>Vendor ID</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vendor.vendorCode</mapping>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,51">
          <id>vendorContactName</id>
          <label>Contact Person</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,52">
          <id>vendorContactEmail</id>
          <label>Contact Email(s)</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,53">
          <id>vendorContactTelNo</id>
          <label>Contact Phone</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <viewName/>
          <viewParams/>
          <allowDateFilter/>
          <popupFormat/>
          <single/>
          <winTitle/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
      </elements>
    </Section>
    <Section id="customerInfoSection" label="Customer Information" position="inspectReport_form.xlsx,tabParties,56" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabParties,66">
          <id>customer</id>
          <label>Customer Name</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{businessName}</format>
          <comboKey/>
          <popupFormat/>
          <single>TRUE</single>
          <viewName>popCustView</viewName>
          <winTitle>Customer Lookup</winTitle>
          <viewParams/>
          <popupFormat>{businessName}</popupFormat>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch>TRUE</disableAutoSearch>
        </element>
        <element position="inspectReport_form.xlsx,tabParties,67">
          <id>customerCode</id>
          <label>Customer ID</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>customer.custCode</mapping>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <comboKey/>
          <popupFormat/>
          <single/>
          <viewName/>
          <winTitle/>
          <viewParams/>
          <popupFormat/>
          <cascadeExpr/>
          <cascadeLabelKey/>
          <disableAutoSearch/>
        </element>
      </elements>
    </Section>
  </sheet>
  <sheet id="tabDetails" position="inspectReport_form.xlsx,tabDetails">
    <Tab id="tabDetails" label="Defect Details" position="inspectReport_form.xlsx,tabDetails,1" ratio="1">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabDetails,8">
          <id>inspectReportDetails</id>
          <label/>
          <type>GroupGrid</type>
        </element>
      </elements>
    </Tab>
    <GroupGrid allowAddDel="" entityName="InspectReportDetail" fieldSorting="asc" fieldSortingBy="seq" groupField="sectionSeq" groupSorting="asc" id="inspectReportDetails" label="Test Items" pageSize="100" position="inspectReport_form.xlsx,tabDetails,11" ratio="1" rowRenderer="com.core.cbx.ui.zk.cul.grid.renderer.ReadonlyRowRender" sectionAddAction="InspectRptDetailsGridHeaderAddAction" sectionDelAction="com.core.cbx.ui.zk.action.DelInspectReportDetailsItemAction" sectionLabel="sectionName" selectionMode="Multiple" showHint="">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabDetails,21">
          <id>seq</id>
          <label>Seq.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,22">
          <id>testItem</id>
          <label>Test Item</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,23">
          <id>critical</id>
          <label>Critical</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,24">
          <id>major</id>
          <label>Major</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,25">
          <id>minor</id>
          <label>Minor</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,26">
          <id>defectCode</id>
          <label>Defect Code</label>
          <type>Dropdown</type>
          <data>Defect_Code</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,27">
          <id>colorSize</id>
          <label>Color/Size</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,28">
          <id>comments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,29">
          <id>more</id>
          <label>Files</label>
          <type>Button</type>
          <data/>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupMoreDetails</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>130</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable>TRUE</alwaysEditable>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabDetails,30">
          <id>noOfAttachments</id>
          <label>No. of Attachments</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportNoOfAttachmentCellRenderer</rendererClass>
        </element>
      </elements>
    </GroupGrid>
  </sheet>
  <sheet id="tabInspectReportItems" position="inspectReport_form.xlsx,tabInspectReportItems">
    <Tab id="tabInspectReportItems" label="Items" position="inspectReport_form.xlsx,tabInspectReportItems,1" ratio="100%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabInspectReportItems,8">
          <id>inspectReportItems</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="InspectReportItem" id="inspectReportItems" label="Items" position="inspectReport_form.xlsx,tabInspectReportItems,11" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabInspectReportItems,18">
          <id>addShipmentItem</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,19">
          <id>selectFromItemIB</id>
          <label>Select from Booking by Item...</label>
          <action>OpenSelectIrShipmentItemPopWinAction</action>
          <actionParams>winId=popIrItemsFromIbByItem</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,20">
          <id>selectFromLotIB</id>
          <label>Select from Booking by Lot...</label>
          <action>OpenSelectIrShipmentItemPopWinAction</action>
          <actionParams>winId=popIrItemsFromIbByLot</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,21">
          <id>selectFromVPO</id>
          <label>Select from Vendor PO...</label>
          <action>OpenSelectIrShipmentItemPopWinAction</action>
          <actionParams>winId=popIrItemsFromVpo</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,22">
          <id>selectFromItem</id>
          <label>Select from Item...</label>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupSelectItem</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,23">
          <id>deleteShipmentItems</id>
          <label>Delete</label>
          <action>DelInspectReportShipItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabInspectReportItems,27">
          <id>vpoItemImage</id>
          <label>Image</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel>TRUE</hideLabel>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,28">
          <id>vpoItem</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom>VpoItem</dataFrom>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,29">
          <id>vpo</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom>Vpo</dataFrom>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,30">
          <id>vpoShipment</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom>VpoShip</dataFrom>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,31">
          <id>vpoNo</id>
          <label>Vendor PO No.</label>
          <type>Hyperlink</type>
          <data/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=vpo&amp;fieldId=vpo&amp;gridId=inspectReportItems</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpo.vpoNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,32">
          <id>vpoShipmentNo</id>
          <label>Shipment No.</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoShipment.shipmentNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass>com.core.cbx.vpo.form.VpoOrderItemLotNoCellRenderer</rendererClass>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,33">
          <id>lotNo</id>
          <label>Lot No.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,34">
          <id>vpoShipmentDate</id>
          <label>Shipment Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoShipment.shipmentDate</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,35">
          <id>vpoItemNo</id>
          <label>Item No.</label>
          <type>Hyperlink</type>
          <data/>
          <action>OpenModuleDocAction</action>
          <actionParams>moduleId=item&amp;fieldId=item&amp;gridId=inspectReportItems</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoItem.itemNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,36">
          <id>vpoItemnName</id>
          <label>Item Name</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoItem.itemName</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,37">
          <id>itemDesc</id>
          <label>Item Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoItem.itemDesc</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,38">
          <id>itemColors</id>
          <label>Colorway/Option</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{shortName}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName>popupItemColorView</viewName>
          <single/>
          <hideLabel/>
          <viewParams>itemId={item.id}</viewParams>
          <readonly/>
          <winTitle>Item Color Lookup</winTitle>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat>{shortName}</popupFormat>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,39">
          <id>itemSizes</id>
          <label>Size</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{sizeDisplayName}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName>popupItemSizeView</viewName>
          <single/>
          <hideLabel/>
          <viewParams>itemId={item.id}</viewParams>
          <readonly/>
          <winTitle>Item Size Lookup</winTitle>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat>{sizeDisplayName}</popupFormat>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,40">
          <id>vpoItemType</id>
          <label>Item Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,41">
          <id>hierarchy</id>
          <label>Product Hierarchy</label>
          <type>HclGroup</type>
          <data>PRODUCT_HIERARCHY</data>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL>TRUE</disableSSL>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,42">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName>popCodelistView</viewName>
          <single/>
          <hideLabel/>
          <viewParams>name=PRODUCT_CATEGORY</viewParams>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat>{name}</popupFormat>
          <allowDateFilter>FALSE</allowDateFilter>
          <extraParams>Selection_preOpenPopupAction=ConstructViewParamsForClassificationAction</extraParams>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,43">
          <id>vpoShipDetail</id>
          <label/>
          <type>Hidden</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom>VpoShipDtl</dataFrom>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,44">
          <id>vpoShipDetailOrderQty</id>
          <label>Order Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoShipDetail.qty</mapping>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,45">
          <id>actualQty</id>
          <label>Actual Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,46">
          <id>outerGtin</id>
          <label>Outer GTIN</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,47">
          <id>customerItemNo</id>
          <label>Customer Product No.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoItem.customerItemNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,48">
          <id>innerGtin</id>
          <label>Inner GTIN</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpoItem.innerGtin</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,49">
          <id>custName</id>
          <label>Customer</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpo.custName</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,50">
          <id>custPoNo</id>
          <label>Customer PO No.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>vpo.custPoNo</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,51">
          <id>inspectionResult</id>
          <label>Inspection Result</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,52">
          <id>failReason</id>
          <label>Fail Reason</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName>popCodelistView</viewName>
          <single/>
          <hideLabel/>
          <viewParams>name=INSPECTION_FAIL_REASON</viewParams>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat>{name}</popupFormat>
          <allowDateFilter>FALSE</allowDateFilter>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,53">
          <id>itemShipmentReleased</id>
          <label>Shipment Released</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabInspectReportItems,54">
          <id>inspectionComments</id>
          <label>Inspection Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabMeasurement" position="inspectReport_form.xlsx,tabMeasurement">
    <Tab id="tabMeasurement" label="Measurements" position="inspectReport_form.xlsx,tabMeasurement,1" ratio="60%,20%,20%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabMeasurement,8">
          <id>inspectReportPerSize</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,9">
          <id>[Blank]</id>
          <type>EmptyGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,10">
          <id>inspectReportMeasSums</id>
          <type>MatrixGrid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,11">
          <id>selectSizes</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,12">
          <id>[Blank]</id>
          <type>EmptyGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,13">
          <id>[Blank]</id>
          <type>EmptyGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,14">
          <id>inspectReportMeasDetails</id>
          <type>MatrixGrid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,15">
          <id>inspectReportMeasResults</id>
          <type>MatrixGrid</type>
        </element>
      </elements>
    </Tab>
    <Section id="inspectReportPerSize" label="Measurement by Size by Color" position="inspectReport_form.xlsx,tabMeasurement,18" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabMeasurement,28">
          <id>uom</id>
          <label>UOM</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <mapField/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <hideLabel/>
          <format>{name}</format>
          <comboKey/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <MatrixGrid colHideFlag="" colMapping="$doc=inspectReportMeasSizeResultId" colModel="inspectReportMeasSizeResults" colModelSorting="" columnHeaderProcessor="" dataModel="inspectReportMeasSumResults" dynamicColumnsStartIndex="1" entityName="InspectReportMeasSum" frozenColumns="1" id="inspectReportMeasSums" label="Measurement Result by Size by Color" matrixEntityName="InspectReportMeasSumResult" maxHeight="500px" pageSize="50" position="inspectReport_form.xlsx,tabMeasurement,32" ratio="100%" rowHideFlag="" rowMapping="$doc=inspectReportMeasSumId" rowModel="inspectReportMeasSums" selectionMode="Multiple">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabMeasurement,39">
          <id>refresh</id>
          <label>Refresh</label>
          <action>RefreshMensurementResultBySizeByColorAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,43">
          <id>inspectReportMeasQty</id>
          <label>Quantity by Size (Color)</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size/>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
        </element>
      </elements>
      <elements id="dynamic-columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,47">
          <id>sizeValue</id>
          <label/>
          <type>Number</type>
          <colHeaderFormat>{size.displayName}({color.shortName})</colHeaderFormat>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <rendererClass>com.core.cbx.inspectreport.form.MeasurementResultDynamicNumberCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </MatrixGrid>
    <MatrixGrid colMapping="$doc=inspectReportMeasSizeId" colModel="inspectReportMeasSizes" dataModel="inspectReportMeasDetailSizes" dynamicColumnsStartIndex="5" editable="FALSE" entityName="InspectReportMeasDetail" id="inspectReportMeasDetails" label="Point of Measurements" matrixEntityName="InspectReportMeasDetailSize" maxHeight="500px" pageSize="50" position="inspectReport_form.xlsx,tabMeasurement,50" ratio="100%" rowMapping="$doc=inspectReportMeasDetailId" rowModel="inspectReportMeasDetails" selectionMode="Single" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabMeasurement,57">
          <id>refreshLastItem</id>
          <label>Refresh from Latest Item</label>
          <action>RefreshPOMByLatestItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,61">
          <id>seq</id>
          <label>Ref.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,62">
          <id>image</id>
          <label>Image</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>specMeasurement.imageId</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,63">
          <id>itemNo</id>
          <label>Item No.</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,64">
          <id>code</id>
          <label>Code</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>specMeasurement.code</mapping>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,65">
          <id>position</id>
          <label>Position</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>specMeasurement.position</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,66">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>specMeasurement.description</mapping>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,67">
          <id>standardMeasurement</id>
          <label>Standard Meas.</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass>com.core.cbx.inspectreport.form.POMStandardMeasCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,68">
          <id>tolPositiveAndNegative</id>
          <label>Tol. (+/-)</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass>com.core.cbx.inspectreport.form.POMTolCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,69">
          <id>refKey</id>
          <label>Ref.Key</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
      <elements id="dynamic-columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,73">
          <id>sizeValue</id>
          <label/>
          <type>Decimal</type>
          <colHeaderFormat>{displayName}({sizeName})</colHeaderFormat>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <rendererClass>com.core.cbx.inspectreport.form.POMDynamicDecimalCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </MatrixGrid>
    <MatrixGrid colHideFlag="" colMapping="$doc=inspectReportMeasDetailId" colModel="inspectReportMeasDetails" colModelSorting="" dataModel="inspectReportMeasDetailResults" dynamicColumnsStartIndex="8" entityName="InspectReportMeasResult" frozenColumns="4" id="inspectReportMeasResults" label="Measurement Details" matrixEntityName="InspectReportMeasDetailResult" maxHeight="500px" pageSize="50" position="inspectReport_form.xlsx,tabMeasurement,76" ratio="100%" rowHideFlag="" rowMapping="$doc=inspectReportMeasResultId" rowModel="inspectReportMeasResults" selectionMode="Multiple">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabMeasurement,83">
          <id>addResultItem</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,84">
          <id>copyResultItem</id>
          <label>Copy</label>
          <action>CopyInspectReportMeasResultsAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,85">
          <id>deleteResultItem</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,86">
          <id>refreshColorSizePrices</id>
          <label>Refresh</label>
          <action>RefreshPOMDifferencesAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,90">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <hidden/>
          <rendererClass/>
          <readonly/>
          <comboKey/>
          <sorting>ASC</sorting>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,91">
          <id>size</id>
          <label>Size</label>
          <type>Dropdown</type>
          <data/>
          <action>GetInspectReportMeasSizeAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format>{displayName}</format>
          <hidden/>
          <rendererClass/>
          <readonly/>
          <comboKey>$entity</comboKey>
          <sorting/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,92">
          <id>color</id>
          <label>Color</label>
          <type>Dropdown</type>
          <data/>
          <action>GetInspectReportMeasColorAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format>{shortName}</format>
          <hidden/>
          <rendererClass/>
          <readonly/>
          <comboKey>$entity</comboKey>
          <sorting/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,93">
          <id>inspectResult</id>
          <label>Result</label>
          <type>Dropdown</type>
          <data>MEASUREMENT_SAMPLE_RESULT</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <hidden/>
          <rendererClass>com.core.cbx.inspectreport.form.MeasResultsCellRenderer</rendererClass>
          <readonly/>
          <comboKey/>
          <sorting/>
        </element>
      </elements>
      <elements id="dynamic-columns">
        <element position="inspectReport_form.xlsx,tabMeasurement,97">
          <id>sizeValue</id>
          <label/>
          <type>Decimal</type>
          <colHeaderFormat>{altLabel}({label})</colHeaderFormat>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <rendererClass>com.core.cbx.inspectreport.form.SizeValueDecimalCellRenderer</rendererClass>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabMeasurement,98">
          <id>deltaSizeValue</id>
          <label/>
          <type>Decimal</type>
          <colHeaderFormat>{altLabel}({label}) Delta</colHeaderFormat>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <rendererClass>com.core.cbx.inspectreport.form.DeltaSizeValueDecimalCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </MatrixGrid>
  </sheet>
  <sheet id="tabChecklist" position="inspectReport_form.xlsx,tabChecklist">
    <Tab id="tabChecklist" label="Checklist" position="inspectReport_form.xlsx,tabChecklist,1" ratio="1">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabChecklist,8">
          <id>inspectReportRequirements</id>
          <label/>
          <type>GroupGrid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,9">
          <id>inspectReportChecklists</id>
          <label/>
          <type>GroupGrid</type>
        </element>
      </elements>
    </Tab>
    <GroupGrid allowAddDel="" entityName="InspectReportRequirement" fieldSorting="asc" fieldSortingBy="seq" frozenColumns="2" groupField="sectionSeq" groupSorting="asc" id="inspectReportRequirements" label="Details" position="inspectReport_form.xlsx,tabChecklist,12" ratio="1" rowRenderer="com.core.cbx.ui.zk.cul.grid.renderer.InspectReportRequirementGroupRowRenderer" sectionAddAction="InspectReportGroupGridHeaderAddAction" sectionCopyAction="InspectReportRequirementsCopyAction" sectionLabel="sectionName" selectionMode="Multiple" showHint="" templDynamicColumnsStartIndex="2">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabChecklist,22">
          <id>seq</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,23">
          <id>sectionName</id>
          <label>Section</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,24">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,25">
          <id>importance</id>
          <label>Severity</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,26">
          <id>result1</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,27">
          <id>result2</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,28">
          <id>result3</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,29">
          <id>result4</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,30">
          <id>result5</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,31">
          <id>result6</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,32">
          <id>result7</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,33">
          <id>result8</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,34">
          <id>result9</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,35">
          <id>result10</id>
          <label/>
          <type>Radio</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams>templDynamicCol:Y</extraParams>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRadioCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,36">
          <id>observationsComments</id>
          <label>Observations</label>
          <type>Checklist</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportChecklistCellRender</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,37">
          <id>percentOfScore</id>
          <label>% of Score</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,38">
          <id>statusScore</id>
          <label>Status Score</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,39">
          <id>maximumScore</id>
          <label>Maximum Score</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,40">
          <id>reviewBy</id>
          <label>Review by</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=CHECKLIST_REVIEW_BY</viewParams>
          <winTitle/>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,41">
          <id>comments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,42">
          <id>correctiveAction</id>
          <label>Corrective Action</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,43">
          <id>dueDate</id>
          <label>Due Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,44">
          <id>responsiblePerson</id>
          <label>Responsible Person</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,45">
          <id>capStatus</id>
          <label>Status</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,46">
          <id>completeDate</id>
          <label>Complete Date</label>
          <type>Date</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,47">
          <id>more</id>
          <label>Files</label>
          <type>Button</type>
          <data/>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupMoreRequirements</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>130</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable>TRUE</alwaysEditable>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,48">
          <id>noOfAttachments</id>
          <label>No. of Attachments</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportNoOfAttachmentCellRenderer</rendererClass>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,49">
          <id>reference</id>
          <label>Reference</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,50">
          <id>isMandatory</id>
          <label>Mandatory</label>
          <type>CheckBox</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,51">
          <id>type</id>
          <label>Type</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <extraParams/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
      </elements>
    </GroupGrid>
    <GroupGrid entityName="InspectReportChecklist" fieldSorting="asc" fieldSortingBy="seqNo" frozenColumns="4" groupField="sectionSeqNo" groupSorting="asc" id="inspectReportChecklists" label="Checklists" position="inspectReport_form.xlsx,tabChecklist,54" ratio="100%" rowRenderer="" sectionAddAction="InspectReportChecklistHeaderAddAction" sectionCopyAction="InspectReportChecklistCopyAction" sectionLabel="sectionName" selectionMode="Multiple" showHint="">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabChecklist,64">
          <id>seqNo</id>
          <label>No.</label>
          <type>Number</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>XS</size>
          <alwaysEditable/>
          <rendererClass/>
          <readonly>TRUE</readonly>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,65">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>L</size>
          <alwaysEditable/>
          <rendererClass/>
          <readonly>TRUE</readonly>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,66">
          <id>observationsComments</id>
          <label>Observations / Comments</label>
          <type>Checklist</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>XXL</size>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportChecklistCellRender</rendererClass>
          <readonly/>
          <filterBy>inspectReportObservationsComments.description || inspectReportObservationsComments.textboxText</filterBy>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,67">
          <id>comments</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>545</size>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportRichTextCellRender</rendererClass>
          <readonly/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,68">
          <id>attachments</id>
          <label>Files</label>
          <type>Button</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupInspectReportMoreDetailsCL</actionParams>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>130</size>
          <alwaysEditable>TRUE</alwaysEditable>
          <rendererClass/>
          <readonly/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,69">
          <id>noOfAttachments</id>
          <label>No. of Attachments</label>
          <type>Number</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>n/a</size>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.factaudit.form.FactAuditNoOfAttachmentCellRenderer</rendererClass>
          <readonly>TRUE</readonly>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,70">
          <id>reference</id>
          <label>Reference</label>
          <type>Text</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>L</size>
          <alwaysEditable/>
          <rendererClass/>
          <readonly/>
          <filterBy/>
        </element>
        <element position="inspectReport_form.xlsx,tabChecklist,71">
          <id>isMandatory</id>
          <label>Mandatory</label>
          <type>CheckBox</type>
          <data/>
          <hideLabel/>
          <sorting/>
          <action/>
          <actionParams/>
          <defaultValue/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <mandatory/>
          <mapping/>
          <mapField/>
          <format/>
          <viewName/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <sortingIndex/>
          <size>L</size>
          <alwaysEditable/>
          <rendererClass/>
          <readonly/>
          <filterBy/>
        </element>
      </elements>
    </GroupGrid>
  </sheet>
  <sheet id="tabEanCodeReport" position="inspectReport_form.xlsx,tabEanCodeReport">
    <Tab id="tabEanCodeReport" label="EAN Code Report" position="inspectReport_form.xlsx,tabEanCodeReport,1" ratio="100%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabEanCodeReport,8">
          <id>inspectReportEanCodeReportList</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="InspectReportEanCodeReport" id="inspectReportEanCodeReportList" label="EAN Code Report" position="inspectReport_form.xlsx,tabEanCodeReport,11" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabEanCodeReport,21">
          <id>seq</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,22">
          <id>ean</id>
          <label>EAN</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,23">
          <id>result</id>
          <label>Result</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,24">
          <id>actual</id>
          <label>Actual</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,25">
          <id>comments</id>
          <label>Comments</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,26">
          <id>position</id>
          <label>Position</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,27">
          <id>styleAndSize</id>
          <label>Color/Size</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabEanCodeReport,28">
          <id>image</id>
          <label>Files</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabProductionStatus" position="inspectReport_form.xlsx,tabProductionStatus">
    <Tab id="tabProductionStatus" label="Production Status" position="inspectReport_form.xlsx,tabProductionStatus,1" ratio="100%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabProductionStatus,8">
          <id>inspectReportProductionStatusList</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="InspectReportProductionStatus" id="inspectReportProductionStatusList" label="Production Status" position="inspectReport_form.xlsx,tabProductionStatus,11" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons"/>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabProductionStatus,21">
          <id>description</id>
          <label>Item Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,22">
          <id>itemNo</id>
          <label>Item No.</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,23">
          <id>color</id>
          <label>Color</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,24">
          <id>checkedQty</id>
          <label>Checked Qty</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,25">
          <id>packedQty</id>
          <label>Packed Qty</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,26">
          <id>notPackedQty</id>
          <label>Not Packed Qty</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,27">
          <id>packedQtyInCarton</id>
          <label>Packed Qty in Carton</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,28">
          <id>noOfPackedCartons</id>
          <label>Number of Packed Cartons</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,29">
          <id>noOfNotPackedCartons</id>
          <label>Number of Not Packed Cartons</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,30">
          <id>producedQty</id>
          <label>Produced Qty</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabProductionStatus,31">
          <id>productionRatio</id>
          <label>Production Ratio (%)</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly/>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail/>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabCartonDetailList" position="inspectReport_form.xlsx,tabCartonDetailList">
    <Tab id="tabCartonDetailList" label="Carton Detail List" position="inspectReport_form.xlsx,tabCartonDetailList,1" ratio="1">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabCartonDetailList,8">
          <id>inspectReportCartonDetailList</id>
          <label>Carton Details</label>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,9">
          <id>inspectReportCartonDefectDtls</id>
          <label>Carton Defect Details</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid entityName="InspectReportCartonDetail" frozenColumns="" id="inspectReportCartonDetailList" label="Carton Details" position="inspectReport_form.xlsx,tabCartonDetailList,12" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabCartonDetailList,19">
          <id>addCartonDetailList</id>
          <label>Add</label>
          <action>AddCartonDetailList</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabCartonDetailList,23">
          <id>cartonDetailShipmentNo</id>
          <label>Shipment No.</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format>{name}</format>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,24">
          <id>cartonDetailColor</id>
          <label>Color</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format>{name}</format>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,25">
          <id>cartonDetailSize</id>
          <label>Size</label>
          <type>Dropdown</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format>{name}</format>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,26">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,27">
          <id>cartonNoFrom</id>
          <label>Carton No. From</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,28">
          <id>cartonNoTo</id>
          <label>Carton No. To</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,29">
          <id>noOfCartons</id>
          <label>No. of Cartons</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,30">
          <id>quantityPerCarton</id>
          <label>Quantity Per Carton</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,31">
          <id>deliveredQuantity</id>
          <label>Delivered Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,32">
          <id>inspectedCartonQuantity</id>
          <label>Inspected Carton Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,33">
          <id>inspectedCarton</id>
          <label>Inspected Carton</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,34">
          <id>inspectedQuantity</id>
          <label>Calculated Inspected Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,35">
          <id>actualInspectedQuantity</id>
          <label>Actual Inspected Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,36">
          <id>sortOutQuantity</id>
          <label>Sort Out Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,37">
          <id>releasedQuantity</id>
          <label>Released Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,38">
          <id>comments</id>
          <label>Comments</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="InspectReportCartonDefectDtl" id="inspectReportCartonDefectDtls" label="Carton Defect Details" position="inspectReport_form.xlsx,tabCartonDetailList,41" ratio="100%" selectionMode="Multiple">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabCartonDetailList,48">
          <id>addCartonDefectDetail</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams>entityName=InspectReportCartonDefectDtl</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,49">
          <id>delCartonDefectDetail</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabCartonDetailList,53">
          <id>seq</id>
          <label>Seq.</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,54">
          <id>cartonNos</id>
          <label>Carton No.</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,55">
          <id>defectCode</id>
          <label>Defect Code</label>
          <type>Dropdown</type>
          <data>CARTON_DEFECT_CODE</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,56">
          <id>status</id>
          <label>Status</label>
          <type>Dropdown</type>
          <data>CARTON_DEFECT_STATUS</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,57">
          <id>quantity</id>
          <label>Quantity</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,58">
          <id>comment</id>
          <label>Comments</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XXL</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,59">
          <id>more</id>
          <label>Attachments</label>
          <type>Button</type>
          <data/>
          <action>OpenPopupWinAction</action>
          <actionParams>winId=popupMoreDetails</actionParams>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>130</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable>TRUE</alwaysEditable>
          <rendererClass/>
        </element>
        <element position="inspectReport_form.xlsx,tabCartonDetailList,60">
          <id>noOfAttachments</id>
          <label>No. of Attachments</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>n/a</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <alwaysEditable/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportNoOfAttachmentCellRenderer</rendererClass>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabCosts" position="inspectReport_form.xlsx,tabCosts">
    <Tab id="tabCosts" label="Costs" position="inspectReport_form.xlsx,tabCosts,1" ratio="50%,50%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabCosts,8">
          <id>costsSummary</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,9">
          <id>costAdditionalInformation</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,10">
          <id>inspectReportCostDetailList</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="costsSummary" label="Cost Summary" position="inspectReport_form.xlsx,tabCosts,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabCosts,23">
          <id>currency</id>
          <label>Currency</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <mapping/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mandatory/>
          <scale/>
          <size>XS</size>
          <viewName/>
          <format>{name}</format>
          <single/>
          <viewParams/>
          <winTitle/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,24">
          <id>totalCost</id>
          <label>Total Cost</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <mapping/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mandatory/>
          <scale/>
          <size>M</size>
          <viewName/>
          <format/>
          <single/>
          <viewParams/>
          <winTitle/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,25">
          <id>notesAndInstructions</id>
          <label>Notes / Instructions</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <mapping/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mandatory/>
          <scale/>
          <size>L</size>
          <viewName/>
          <format/>
          <single/>
          <viewParams/>
          <winTitle/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Section id="costAdditionalInformation" label="Cost Additional Information" position="inspectReport_form.xlsx,tabCosts,28" ratio="100%">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabCosts,35">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
          <data/>
          <action/>
          <actionParams/>
          <mapping/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mandatory/>
          <scale/>
          <size/>
          <viewName/>
          <format/>
          <single/>
          <viewParams/>
          <winTitle/>
          <readonly/>
        </element>
      </elements>
    </Section>
    <Grid entityName="InspectReportCostDetail" frozenColumns="2" id="inspectReportCostDetailList" label="Costs" position="inspectReport_form.xlsx,tabCosts,38" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabCosts,45">
          <id>addCostDetail</id>
          <label>Add</label>
          <action>AddItemAction</action>
          <actionParams>entityName=InspectReportCostDetail</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,46">
          <id>copyCostDetail</id>
          <label>Copy</label>
          <action>CopyItemAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,47">
          <id>delCostDetail</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabCosts,51">
          <id>type</id>
          <label>Type</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,52">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,53">
          <id>cost</id>
          <label>Cost</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,54">
          <id>altCurrency</id>
          <label>Alt. Currency</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format>{name}</format>
          <readonly/>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,55">
          <id>calculatedCost</id>
          <label>Calculated Cost</label>
          <type>Decimal</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,56">
          <id>currency</id>
          <label>Currency</label>
          <type>Dropdown</type>
          <data/>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonly>TRUE</readonly>
        </element>
        <element position="inspectReport_form.xlsx,tabCosts,57">
          <id>notesAndInstructions</id>
          <label>Notes / Instructions</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabMobileActivities" position="inspectReport_form.xlsx,tabMobileActivities">
    <Tab id="tabMobileActivities" label="Mobile Activities" position="inspectReport_form.xlsx,tabMobileActivities,1" ratio="60%,20%,20%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabMobileActivities,8">
          <id>inspectReportTotalWorkTime</id>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,9">
          <id>[Blank]</id>
          <type>EmptyGroup</type>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,10">
          <id>inspectReportMobileActivities</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="inspectReportTotalWorkTime" label="" position="inspectReport_form.xlsx,tabMobileActivities,13" ratio="100%">
      <elements id="buttons"/>
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabMobileActivities,23">
          <id>totalWorkTimeText</id>
          <label>Total Work Time</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <mapField/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <hideLabel/>
          <format/>
          <comboKey/>
          <rendererClass/>
          <readonly>TRUE</readonly>
        </element>
      </elements>
    </Section>
    <Grid entityName="InspectReportMobileActivities" id="inspectReportMobileActivities" label="Mobile Activities" position="inspectReport_form.xlsx,tabMobileActivities,26" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabMobileActivities,33">
          <id>mobileUser</id>
          <label>User</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,34">
          <id>mobileAction</id>
          <label>Action</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,35">
          <id>startTime</id>
          <label>Start Time</label>
          <type>Datetime</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,36">
          <id>endTime</id>
          <label>End Time</label>
          <type>Datetime</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,37">
          <id>workTime</id>
          <label>Work Time(seconds)</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass>com.core.cbx.inspectreport.form.InspectReportMobileActivitiesNumberCellRenderer</rendererClass>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,38">
          <id>gpsLat</id>
          <label>GPS coordinate Lat</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
        <element position="inspectReport_form.xlsx,tabMobileActivities,39">
          <id>gpsLng</id>
          <label>GPS coordinate Lng</label>
          <type>Text</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeLabelKey/>
          <cascadeExpr/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <popupFormat/>
          <viewName/>
          <single/>
          <hideLabel/>
          <viewParams/>
          <readonly>TRUE</readonly>
          <winTitle/>
          <disableSSL/>
          <rendererClass/>
          <showInDetail>TRUE</showInDetail>
          <popupFormat/>
          <allowDateFilter/>
          <extraParams/>
          <alwaysEditable/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabImages" position="inspectReport_form.xlsx,tabImages">
    <Tab id="tabImages" label="Images &amp; Attachments" position="inspectReport_form.xlsx,tabImages,1" ratio="100%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabImages,8">
          <id>inspectReportImages</id>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,9">
          <id>inspectReportAttachments</id>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Grid enableDropUpload="Y" entityName="InspectReportImage" id="inspectReportImages" label="Images" position="inspectReport_form.xlsx,tabImages,12" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabImages,19">
          <id>addImage</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=InspectReportImage&amp;upload=true</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,20">
          <id>copyImage</id>
          <label>Copy</label>
          <action>InspectReportImagesCopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,21">
          <id>delImage</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabImages,25">
          <id>imageType</id>
          <label>Type</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>imageType</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=IMAGE_TYPE</viewParams>
          <winTitle>Image Type Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
          <extraParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,26">
          <id>defectLevel</id>
          <label>Defect Level</label>
          <type>Dropdown</type>
          <data>DEFECT_TYPE</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,27">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,28">
          <id>image</id>
          <label>File</label>
          <type>Image</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,29">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
          <extraParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,30">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
          <extraParams/>
        </element>
      </elements>
    </Grid>
    <Grid enableDropUpload="Y" entityName="InspectReportAttachment" id="inspectReportAttachments" label="Other Attachments &amp; Comments" position="inspectReport_form.xlsx,tabImages,33" ratio="1" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabImages,40">
          <id>addAttachment</id>
          <label>Select Files...</label>
          <action>AddItemAction</action>
          <actionParams>entityName=InspectReportAttachment&amp;upload=true</actionParams>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,41">
          <id>copyAttachment</id>
          <label>Copy</label>
          <action>InspectReportAttachmentsCopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,42">
          <id>delAttachment</id>
          <label>Delete</label>
          <action>DelItemAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabImages,46">
          <id>attachType</id>
          <label>Type</label>
          <type>Selection</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping>attachType</mapping>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName>popCodelistView</viewName>
          <viewParams>name=ATTACHMENT_TYPE</viewParams>
          <winTitle>Attachment Type Lookup</winTitle>
          <allowDateFilter>FALSE</allowDateFilter>
          <readonly/>
          <popupFormat>{name}</popupFormat>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,47">
          <id>defectLevel</id>
          <label>Defect Level</label>
          <type>Dropdown</type>
          <data>DEFECT_TYPE</data>
          <action>GetCodelistDataSourceAction</action>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format>{name}</format>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,48">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,49">
          <id>attachment</id>
          <label>File</label>
          <type>Attach</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory>TRUE</mandatory>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,50">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Label</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>M</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly/>
          <popupFormat/>
        </element>
        <element position="inspectReport_form.xlsx,tabImages,51">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>S</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonlyFormat/>
          <comboSorting/>
          <comboKey/>
          <labelRenderer/>
          <viewName/>
          <viewParams/>
          <winTitle/>
          <allowDateFilter/>
          <readonly>TRUE</readonly>
          <popupFormat/>
        </element>
      </elements>
    </Grid>
  </sheet>
  <sheet id="tabOther" position="inspectReport_form.xlsx,tabOther">
    <Tab id="tabOther" label="Others" position="inspectReport_form.xlsx,tabOther,1" ratio="33%,34%,33%">
      <elements id="default">
        <element position="inspectReport_form.xlsx,tabOther,8">
          <id>other1</id>
          <label>Other 1</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,9">
          <id>other2</id>
          <label>Other 2</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,10">
          <id>other3</id>
          <label>Other 3</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,11">
          <id>other4</id>
          <label>Other 4</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,12">
          <id>other5</id>
          <label>Other 5</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,13">
          <id>other6</id>
          <label>Other 6</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,14">
          <id>other7</id>
          <label>Other 7</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,15">
          <id>other8</id>
          <label>Other 8</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,16">
          <id>other9</id>
          <label>Other 9</label>
          <type>Section</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,17">
          <id>inspectReportOthers1</id>
          <label>Others Table 1</label>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,18">
          <id>inspectReportOthers2</id>
          <label>Others Table 2</label>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,19">
          <id>inspectReportOthers3</id>
          <label>Others Table 3</label>
          <type>Grid</type>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,20">
          <id>inspectReportOthers4</id>
          <label>Others Table 4</label>
          <type>Grid</type>
        </element>
      </elements>
    </Tab>
    <Section id="other1" label="Other 1" position="inspectReport_form.xlsx,tabOther,23">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,30">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other2" label="Other 2" position="inspectReport_form.xlsx,tabOther,33">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,40">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other3" label="Other 3" position="inspectReport_form.xlsx,tabOther,43">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,50">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other4" label="Other 4" position="inspectReport_form.xlsx,tabOther,53">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,60">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other5" label="Other 5" position="inspectReport_form.xlsx,tabOther,63">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,70">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other6" label="Other 6" position="inspectReport_form.xlsx,tabOther,73">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,80">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other7" label="Other 7" position="inspectReport_form.xlsx,tabOther,83">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,90">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other8" label="Other 8" position="inspectReport_form.xlsx,tabOther,93">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,100">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Section id="other9" label="Other 9" position="inspectReport_form.xlsx,tabOther,103">
      <elements id="fields">
        <element position="inspectReport_form.xlsx,tabOther,110">
          <id>[BLANK]</id>
          <label/>
          <type>Blank</type>
        </element>
      </elements>
    </Section>
    <Grid entityName="InspectReportOthers1" frozenColumns="" id="inspectReportOthers1" label="Others Table 1" position="inspectReport_form.xlsx,tabOther,113" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabOther,120">
          <id>addOthers1</id>
          <label>Add</label>
          <action>AddInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,121">
          <id>copyOthers1</id>
          <label>Copy</label>
          <action>InspectReportOther1CopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,122">
          <id>delOthers1</id>
          <label>Delete</label>
          <action>DelInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,123">
          <id>custOthers1</id>
          <label>Custom</label>
          <action>FirstOtherCustomAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabOther,127">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,128">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="InspectReportOthers2" frozenColumns="" id="inspectReportOthers2" label="Others Table 2" position="inspectReport_form.xlsx,tabOther,131" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabOther,138">
          <id>addOthers2</id>
          <label>Add</label>
          <action>AddInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,139">
          <id>copyOthers2</id>
          <label>Copy</label>
          <action>InspectReportOther2CopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,140">
          <id>delOthers2</id>
          <label>Delete</label>
          <action>DelInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,141">
          <id>custOthers2</id>
          <label>Custom</label>
          <action>SecondOtherCustomAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabOther,145">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,146">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="InspectReportOthers3" frozenColumns="" id="inspectReportOthers3" label="Others Table 3" position="inspectReport_form.xlsx,tabOther,149" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabOther,156">
          <id>addOthers3</id>
          <label>Add</label>
          <action>AddInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,157">
          <id>copyOthers3</id>
          <label>Copy</label>
          <action>InspectReportOther3CopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,158">
          <id>delOthers3</id>
          <label>Delete</label>
          <action>DelInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,159">
          <id>custOthers3</id>
          <label>Custom</label>
          <action>ThirdOtherCustomAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabOther,163">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,164">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
      </elements>
    </Grid>
    <Grid entityName="InspectReportOthers4" frozenColumns="" id="inspectReportOthers4" label="Others Table 4" position="inspectReport_form.xlsx,tabOther,167" ratio="100%" selectionMode="Multiple" showHint="">
      <elements id="buttons">
        <element position="inspectReport_form.xlsx,tabOther,174">
          <id>addOthers4</id>
          <label>Add</label>
          <action>AddInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,175">
          <id>copyOthers4</id>
          <label>Copy</label>
          <action>InspectReportOther4CopyAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,176">
          <id>delOthers4</id>
          <label>Delete</label>
          <action>DelInspectReportAction</action>
          <actionParams/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,177">
          <id>custOthers4</id>
          <label>Custom</label>
          <action>ThirdOtherCustomAction</action>
          <actionParams/>
        </element>
      </elements>
      <elements id="columns">
        <element position="inspectReport_form.xlsx,tabOther,181">
          <id>seqNo</id>
          <label>Seq.</label>
          <type>Number</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>XS</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
        <element position="inspectReport_form.xlsx,tabOther,182">
          <id>description</id>
          <label>Description</label>
          <type>TextArea</type>
          <data/>
          <action/>
          <actionParams/>
          <group/>
          <dataFrom/>
          <cascade/>
          <cascadeBy/>
          <filterBy/>
          <colspan/>
          <rowspan/>
          <mapping/>
          <scale/>
          <size>L</size>
          <prefix/>
          <mandatory/>
          <format/>
          <readonly/>
          <single/>
          <format/>
        </element>
      </elements>
    </Grid>
  </sheet>
</form>
