<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="compTempl" position="compTempl_view.xlsx">
  <sheet id="compTemplView" position="compTempl_view.xlsx,compTemplView">
    <ViewDefinition advancedSearchId="" description="" id="compTemplView" label="Comparison Templates" moduleId="compTempl" position="compTempl_view.xlsx,compTemplView,1" queryId="listCompTempls" searchCriterion="">
      <elements id="options">
        <element position="compTempl_view.xlsx,compTemplView,8">
          <id>LOAD_RESULTS_ON_LOAD</id>
          <label/>
          <value>FALSE</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,9">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,10">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,11">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,12">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,13">
          <id>LATEST_VERSION</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,14">
          <id>ROW_NUMBER_HINT</id>
          <label/>
          <value>No</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,15">
          <id>CPM_VIEW_LEVEL</id>
          <label/>
          <value>entity</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,16">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,17">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>CompTempl</value>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,18">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,refNo:refNo:string,businessRefNo:businessReference:string,isLatest:isLatest:boolean</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="compTempl_view.xlsx,compTemplView,22">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;entityName=CompTempl</actionParams>
          <buttonGroup/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,23">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,24">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,25">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,26">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,27">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,28">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;entityName=CompTempl</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,29">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;entityName=CompTempl</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,30">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,31">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;entityName=CompTempl</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,32">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=compTempl&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,33">
          <id>openBatchUpdateWin</id>
          <label>Batch Update</label>
          <type>button</type>
          <actionParams>entityName=CpmTask</actionParams>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="compTempl_view.xlsx,compTemplView,38">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>2</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,39">
          <id>applyModule</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CL.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>applyModule:applyModuleLabel:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,40">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CT.DESCRIPTION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,41">
          <id>isDefault</id>
          <label>Default</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>1</visibility>
          <mappedField>CT.IS_DEFAULT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isDefault:isDefault:boolean</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,42">
          <id>CompTempl</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,43">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,44">
          <id>status</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.status.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CT.STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>status:status:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,45">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,46">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,47">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,48">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,49">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,50">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CT.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>isCpmInitialized:isCpmInitialized:boolean</esMapping>
        </element>
        <element position="compTempl_view.xlsx,compTemplView,51">
          <id>cpmTask</id>
          <label/>
          <type>CpmTask</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width/>
          <visibility>1</visibility>
          <mappedField>CT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>cpmDocId:cpmDoc.cpmId:string,refDocRefNo:cpmDoc.refDocRefNo:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
