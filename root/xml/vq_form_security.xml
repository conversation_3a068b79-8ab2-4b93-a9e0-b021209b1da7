<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="vq" position="vq_form_security.xlsx">
  <sheet id="_system" position="vq_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="vq_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="vq_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="vq_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/14</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="vq_form_security.xlsx,generalInfo">
    <GeneralInfo position="vq_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="vq_form_security.xlsx,condition">
    <ConditionList position="vq_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="vq_form_security.xlsx,condition,4">
          <conditionId>statusNew</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,5">
          <conditionId>statusQuoted</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,6">
          <conditionId>statusDeclinedToResponse</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,7">
          <conditionId>statusConfirmedToBuy</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,8">
          <conditionId>statusRejectedToBuy</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,9">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,10">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,11">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,12">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,13">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,14">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,15">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,16">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,17">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,18">
          <conditionId>vqVendorNameNotEmpty</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,19">
          <conditionId>isShortlisted</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,20">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,21">
          <conditionId>isNotShortlisted</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,22">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,23">
          <conditionId>isExternalDomain</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,24">
          <conditionId>isHubDomain</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,25">
          <conditionId>isExternalDomain</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,26">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,27">
          <conditionId>isVqHasColorButNoSize</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,28">
          <conditionId>isVqHasSizeButNoColor</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,29">
          <conditionId>isVqHasNoColorAndNoSize</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,30">
          <conditionId>isItem</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,31">
          <conditionId>isMaterialItem</conditionId>
        </element>
        <element position="vq_form_security.xlsx,condition,32">
          <conditionId>isNotOpenCostingAndIsBuyer</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="vq_form_security.xlsx,default">
    <ActionConditionMatrix position="vq_form_security.xlsx,default,1">
      <elements id="default">
        <element position="vq_form_security.xlsx,default,4">
          <actionId>vqGenOffersheet</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,5">
          <actionId>quoteNewProjectDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,6">
          <actionId>vqNewRequestForSpecifications</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,7">
          <actionId>vqGenCpo</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,8">
          <actionId>vqGenMpo</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,9">
          <actionId>vqGenVpo</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,10">
          <actionId>copyFromExistingVq</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,11">
          <actionId>editDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,12">
          <actionId>amendDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,13">
          <actionId>saveDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,14">
          <actionId>baseSaveDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,15">
          <actionId>saveAndConfirm</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,16">
          <actionId>discardDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,17">
          <actionId>submitVq</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>disallowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,18">
          <actionId>sendToVendor</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,19">
          <actionId>customExport01</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,20">
          <actionId>customExport02</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,21">
          <actionId>customExport03</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,22">
          <actionId>customExport04</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,23">
          <actionId>customExport05</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,24">
          <actionId>customExport06</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,25">
          <actionId>customExport07</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,26">
          <actionId>customExport08</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,27">
          <actionId>customExport09</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,28">
          <actionId>customExport10</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,29">
          <actionId>uploadDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>disallowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,30">
          <actionId>loadCostSheet</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,31">
          <actionId>openCostSheet</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>disallowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,32">
          <actionId>declineVq</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>disallowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,33">
          <actionId>quoted</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,34">
          <actionId>markShortListed</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>disallowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,35">
          <actionId>removeFromShortlist</actionId>
          <isNotShortlisted>disallowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,36">
          <actionId>confirmToBuy</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>disallowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,37">
          <actionId>rejectToBuy</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>disallowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,38">
          <actionId>copyDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,39">
          <actionId>loadDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,40">
          <actionId>discardConfirmToBuy</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>disallowed</statusQuoted>
          <statusDeclinedToResponse>disallowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,41">
          <actionId>initializeCpm</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>disallowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,42">
          <actionId>activateDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,43">
          <actionId>deactivateDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,44">
          <actionId>cancelDoc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,45">
          <actionId>quotationCostBreakdownRecalculate</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,46">
          <actionId>quotationCostBreakdownCancel</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,47">
          <actionId>customPrint01</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,48">
          <actionId>customPrint02</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,49">
          <actionId>customPrint03</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,50">
          <actionId>customPrint04</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,51">
          <actionId>customPrint05</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,52">
          <actionId>customPrint06</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,53">
          <actionId>customPrint07</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,54">
          <actionId>customPrint08</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,55">
          <actionId>customPrint09</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,56">
          <actionId>customPrint10</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,57">
          <actionId>vqCustom01</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,58">
          <actionId>vqCustom02</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,59">
          <actionId>vqCustom03</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,60">
          <actionId>vqCustom04</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,61">
          <actionId>vqCustom05</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,62">
          <actionId>vqCustom06</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,63">
          <actionId>vqCustom07</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,64">
          <actionId>vqCustom08</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,65">
          <actionId>vqCustom09</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,66">
          <actionId>vqCustom10</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,67">
          <actionId>markAsCustomStatus01Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,68">
          <actionId>markAsCustomStatus02Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,69">
          <actionId>markAsCustomStatus03Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,70">
          <actionId>markAsCustomStatus04Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,71">
          <actionId>markAsCustomStatus05Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,72">
          <actionId>markAsCustomStatus06Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,73">
          <actionId>markAsCustomStatus07Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,74">
          <actionId>markAsCustomStatus08Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,75">
          <actionId>markAsCustomStatus09Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,76">
          <actionId>markAsCustomStatus10Doc</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,77">
          <actionId>reinitializeCpm</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,78">
          <actionId>refreshCpmTemplate</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,79">
          <actionId>refreshCpmPlanDate</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,80">
          <actionId>adoptSpecToItem</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>disallowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="vq_form_security.xlsx,default,81">
          <actionId>adoptAsNewItem</actionId>
          <isNotShortlisted>allowed</isNotShortlisted>
          <isShortlisted>allowed</isShortlisted>
          <statusNew>allowed</statusNew>
          <statusQuoted>allowed</statusQuoted>
          <statusDeclinedToResponse>allowed</statusDeclinedToResponse>
          <statusConfirmedToBuy>allowed</statusConfirmedToBuy>
          <statusRejectedToBuy>allowed</statusRejectedToBuy>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>disallowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="vq_form_security.xlsx,default,84">
      <elements id="default">
        <element position="vq_form_security.xlsx,default,87">
          <componentId>ui</componentId>
          <statusNew>editable</statusNew>
          <statusQuoted>editable</statusQuoted>
          <statusDeclinedToResponse>readonly</statusDeclinedToResponse>
          <statusConfirmedToBuy>editable</statusConfirmedToBuy>
          <statusRejectedToBuy>editable</statusRejectedToBuy>
          <docStatusActive>editable</docStatusActive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>editable</vqVendorNameNotEmpty>
          <isExternalDomain>editable</isExternalDomain>
          <isHubDomain>editable</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>editable</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>editable</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>editable</isVqHasNoColorAndNoSize>
          <isItem>editable</isItem>
          <isMaterialItem>editable</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,88">
          <componentId>ui.tabHeader.generalSection.quoteDate</componentId>
          <statusNew>readonly</statusNew>
          <statusQuoted>readonly</statusQuoted>
          <statusDeclinedToResponse>readonly</statusDeclinedToResponse>
          <statusConfirmedToBuy>readonly</statusConfirmedToBuy>
          <statusRejectedToBuy>readonly</statusRejectedToBuy>
          <docStatusActive>readonly</docStatusActive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>readonly</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,89">
          <componentId>ui.tabHeader.generalSection.vendorId</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>readonly</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,90">
          <componentId>ui.tabHeader.generalSection.shortListed</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>hidden</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,91">
          <componentId>ui.tabHeader.productInformationSection.setFlag</componentId>
          <statusNew>hidden</statusNew>
          <statusQuoted>hidden</statusQuoted>
          <statusDeclinedToResponse>hidden</statusDeclinedToResponse>
          <statusConfirmedToBuy>hidden</statusConfirmedToBuy>
          <statusRejectedToBuy>hidden</statusRejectedToBuy>
          <docStatusActive>hidden</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,92">
          <componentId>ui.tabHeader.summary</componentId>
          <statusNew>hidden</statusNew>
          <statusQuoted>hidden</statusQuoted>
          <statusDeclinedToResponse>hidden</statusDeclinedToResponse>
          <statusConfirmedToBuy>hidden</statusConfirmedToBuy>
          <statusRejectedToBuy>hidden</statusRejectedToBuy>
          <docStatusActive>hidden</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>hidden</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,93">
          <componentId>ui.tabHeader.rfqSection2.productCategory</componentId>
          <statusNew>readonly</statusNew>
          <statusQuoted>readonly</statusQuoted>
          <statusDeclinedToResponse>readonly</statusDeclinedToResponse>
          <statusConfirmedToBuy>readonly</statusConfirmedToBuy>
          <statusRejectedToBuy>readonly</statusRejectedToBuy>
          <docStatusActive>readonly</docStatusActive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>readonly</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,94">
          <componentId>ui.tabHeader.requestSection.sourcingRecordNo</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>hidden</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,95">
          <componentId>ui.tabHeader.colorSizeUnitCostSection</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>hidden</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,96">
          <componentId>ui.tabHeader.colorSizeUnitCostSection.unitCostBy</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>readonly</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>readonly</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>hidden</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,97">
          <componentId>ui.tabHeader.virtualUnitCostDetails</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>hidden</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,98">
          <componentId>ui.tabHeader.unitCostBySubItems</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>hidden</isItem>
          <isMaterialItem>hidden</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,99">
          <componentId>ui.vqLinkbar.approval</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,100">
          <componentId>ui.vqMenubar.viewVqCostingDetail</componentId>
          <statusNew>hidden</statusNew>
          <statusQuoted>hidden</statusQuoted>
          <statusDeclinedToResponse>hidden</statusDeclinedToResponse>
          <statusConfirmedToBuy>hidden</statusConfirmedToBuy>
          <statusRejectedToBuy>hidden</statusRejectedToBuy>
          <docStatusActive>hidden</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>hidden</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,101">
          <componentId>ui.vqLinkbar.duplicateWindow</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,102">
          <componentId>ui.vqLinkbar.openForum</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,103">
          <componentId>ui.vqLinkbar.addToFavorites</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,104">
          <componentId>ui.tabHeader.generalSection.vendorName</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>readonly</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,105">
          <componentId>ui.vqMenubar.printDoc</componentId>
          <statusNew>hidden</statusNew>
          <statusQuoted>hidden</statusQuoted>
          <statusDeclinedToResponse>hidden</statusDeclinedToResponse>
          <statusConfirmedToBuy>hidden</statusConfirmedToBuy>
          <statusRejectedToBuy>hidden</statusRejectedToBuy>
          <docStatusActive>hidden</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>hidden</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,106">
          <componentId>ui.vqMenubar.editDoc</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>editable</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,107">
          <componentId>ui.vqMenubar.amendDoc</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>editable</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>editable</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer/>
          <isNotOpenCostingAndNotAllowMaterials/>
          <isOpenCostingAndNotAllowMaterials/>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
        <element position="vq_form_security.xlsx,default,108">
          <componentId>ui.tabCostBreakdown</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer>inherit</isNotOpenCostingAndIsBuyer>
          <isNotOpenCostingAndNotAllowMaterials>inherit</isNotOpenCostingAndNotAllowMaterials>
          <isOpenCostingAndNotAllowMaterials>inherit</isOpenCostingAndNotAllowMaterials>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor>inherit</isNotOpenCostingAndAllowMaterialsAndIsVendor>
        </element>
        <element position="vq_form_security.xlsx,default,109">
          <componentId>ui.tabCostBreakdown.vqComponentCosts</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer>inherit</isNotOpenCostingAndIsBuyer>
          <isNotOpenCostingAndNotAllowMaterials>hidden</isNotOpenCostingAndNotAllowMaterials>
          <isOpenCostingAndNotAllowMaterials>inherit</isOpenCostingAndNotAllowMaterials>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor>hidden</isNotOpenCostingAndAllowMaterialsAndIsVendor>
        </element>
        <element position="vq_form_security.xlsx,default,110">
          <componentId>ui.tabCostBreakdown.vqComponentCosts.addComponentCost</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer>inherit</isNotOpenCostingAndIsBuyer>
          <isNotOpenCostingAndNotAllowMaterials>hidden</isNotOpenCostingAndNotAllowMaterials>
          <isOpenCostingAndNotAllowMaterials>hidden</isOpenCostingAndNotAllowMaterials>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor>hidden</isNotOpenCostingAndAllowMaterialsAndIsVendor>
        </element>
        <element position="vq_form_security.xlsx,default,111">
          <componentId>ui.tabCostBreakdown.vqComponentCosts.addGroup</componentId>
          <statusNew>inherit</statusNew>
          <statusQuoted>inherit</statusQuoted>
          <statusDeclinedToResponse>inherit</statusDeclinedToResponse>
          <statusConfirmedToBuy>inherit</statusConfirmedToBuy>
          <statusRejectedToBuy>inherit</statusRejectedToBuy>
          <docStatusActive>inherit</docStatusActive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>inherit</vqVendorNameNotEmpty>
          <isExternalDomain>inherit</isExternalDomain>
          <isHubDomain>inherit</isHubDomain>
          <isSingleSourcingRecord>inherit</isSingleSourcingRecord>
          <isVqHasColorButNoSize>inherit</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>inherit</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>inherit</isVqHasNoColorAndNoSize>
          <isItem>inherit</isItem>
          <isMaterialItem>inherit</isMaterialItem>
          <isNotOpenCostingAndIsBuyer>inherit</isNotOpenCostingAndIsBuyer>
          <isNotOpenCostingAndNotAllowMaterials>hidden</isNotOpenCostingAndNotAllowMaterials>
          <isOpenCostingAndNotAllowMaterials>hidden</isOpenCostingAndNotAllowMaterials>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor>hidden</isNotOpenCostingAndAllowMaterialsAndIsVendor>
        </element>
        <element position="vq_form_security.xlsx,default,112">
          <componentId>ui.tabContainer.vqCarton.cartonType</componentId>
          <statusNew>hidden</statusNew>
          <statusQuoted>hidden</statusQuoted>
          <statusDeclinedToResponse>hidden</statusDeclinedToResponse>
          <statusConfirmedToBuy>hidden</statusConfirmedToBuy>
          <statusRejectedToBuy>hidden</statusRejectedToBuy>
          <docStatusActive>hidden</docStatusActive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <vqVendorNameNotEmpty>hidden</vqVendorNameNotEmpty>
          <isExternalDomain>hidden</isExternalDomain>
          <isHubDomain>hidden</isHubDomain>
          <isSingleSourcingRecord>hidden</isSingleSourcingRecord>
          <isVqHasColorButNoSize>hidden</isVqHasColorButNoSize>
          <isVqHasSizeButNoColor>hidden</isVqHasSizeButNoColor>
          <isVqHasNoColorAndNoSize>hidden</isVqHasNoColorAndNoSize>
          <isItem>hidden</isItem>
          <isMaterialItem>hidden</isMaterialItem>
          <isNotOpenCostingAndIsBuyer>hidden</isNotOpenCostingAndIsBuyer>
          <isNotOpenCostingAndNotAllowMaterials>hidden</isNotOpenCostingAndNotAllowMaterials>
          <isOpenCostingAndNotAllowMaterials>hidden</isOpenCostingAndNotAllowMaterials>
          <isNotOpenCostingAndAllowMaterialsAndIsVendor/>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="vq_form_security.xlsx,acl">
    <ActionRule position="vq_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="vq_form_security.xlsx,acl,4">
          <actionId>vqGenOffersheet</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,5">
          <actionId>quoteNewProjectDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,6">
          <actionId>vqNewRequestForSpecifications</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,7">
          <actionId>vqGenMpo</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,8">
          <actionId>vqGenVpo</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,9">
          <actionId>copyFromExistingVq</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,10">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,11">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,12">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,13">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,14">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,15">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,16">
          <actionId>submitVq</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,17">
          <actionId>sendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,18">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,19">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,20">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,21">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,22">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,23">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,24">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,25">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,26">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,27">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,28">
          <actionId>uploadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,29">
          <actionId>loadCostSheet</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,30">
          <actionId>openCostSheet</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,31">
          <actionId>viewVqCostingDetail</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,32">
          <actionId>declineVq</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,33">
          <actionId>quoted</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,34">
          <actionId>markShortListed</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,35">
          <actionId>removeFromShortlist</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,36">
          <actionId>confirmToBuy</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,37">
          <actionId>rejectToBuy</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,38">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,39">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,40">
          <actionId>discardConfirmToBuy</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,41">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,42">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,43">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,44">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,45">
          <actionId>quotationCostBreakdownRecalculate</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,46">
          <actionId>quotationCostBreakdownCancel</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,47">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,48">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,49">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,50">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,51">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,52">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,53">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,54">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,55">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,56">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,57">
          <actionId>vqCustom01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,58">
          <actionId>vqCustom02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,59">
          <actionId>vqCustom03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,60">
          <actionId>vqCustom04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,61">
          <actionId>vqCustom05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,62">
          <actionId>vqCustom06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,63">
          <actionId>vqCustom07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,64">
          <actionId>vqCustom08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,65">
          <actionId>vqCustom09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,66">
          <actionId>vqCustom10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,67">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,68">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,69">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,70">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,71">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,72">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,73">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,74">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,75">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,76">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,77">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,78">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,79">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <vq.Author>not-has</vq.Author>
          <vq.Editor>not-has</vq.Editor>
          <vq.ReadOnly>not-has</vq.ReadOnly>
          <offersheet.Author>not-has</offersheet.Author>
          <project.Author>not-has</project.Author>
          <mpo.Author>not-has</mpo.Author>
          <vpo.Author>not-has</vpo.Author>
          <rfs.Author>not-has</rfs.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,80">
          <actionId>adoptSpecToItem</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>has</rfs.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="vq_form_security.xlsx,acl,81">
          <actionId>adoptAsNewItem</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <vq.Author>has</vq.Author>
          <vq.Editor>has</vq.Editor>
          <vq.ReadOnly>has</vq.ReadOnly>
          <offersheet.Author>has</offersheet.Author>
          <project.Author>has</project.Author>
          <mpo.Author>has</mpo.Author>
          <vpo.Author>has</vpo.Author>
          <rfs.Author>has</rfs.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="vq_form_security.xlsx,acl,84">
      <elements id="default">
        <element position="vq_form_security.xlsx,acl,87">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,88">
          <componentId>ui.tabCostBreakdown</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,89">
          <componentId>ui.tabContainer</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,90">
          <componentId>ui.tabContact</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,91">
          <componentId>ui.tabImage</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,92">
          <componentId>ui.tabCostSummary</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,93">
          <componentId>ui.tabHeader.costingSection.targetPrice</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,94">
          <componentId>ui.tabHeader.requestSection.sourcingRecordNo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,95">
          <componentId>ui.tabHeader.requestSection.projectNo</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,96">
          <componentId>ui.tabHeader.requestSection.projectName</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,97">
          <componentId>ui.tabCostSummary.vqCostSummarys.vat</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,98">
          <componentId>ui.tabCostSummary.vqCostSummarys.whatIfAnalysis</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,99">
          <componentId>ui.tabCostSummary.vqCostSummarys.updateUserName</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,100">
          <componentId>ui.tabCostSummary.vqCostSummarys.createdOn</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,101">
          <componentId>ui.tabCostSummary.vqCostSummarys.createUserName</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,102">
          <componentId>ui.tabHeader.virtualUnitCostDetails.addUnitCostDetail</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,103">
          <componentId>ui.tabHeader.virtualUnitCostDetails.copyUnitCostDetail</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,104">
          <componentId>ui.tabHeader.virtualUnitCostDetails.deleteUnitCostDetail</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,105">
          <componentId>ui.tabHeader.virtualUnitCostDetails.resetUnitCostDetail</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,106">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="vq_form_security.xlsx,acl,107">
          <componentId>ui.tabOther</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="vq_form_security.xlsx,acl,110">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
