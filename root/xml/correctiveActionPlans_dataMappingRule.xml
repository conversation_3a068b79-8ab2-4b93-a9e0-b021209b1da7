<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="correctiveActionPlans" position="correctiveActionPlans_dataMappingRule.xlsx">
  <sheet id="correctiveActionPlansCopyDoc" position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc">
    <DataMappingRule description="Mapping for CorrectiveActionPlans copy doc" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-03-15" id="correctiveActionPlansCopyDoc" position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,1" srcEntityName="CorrectiveActionPlans" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>correctiveActionPlansNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>issueTotalNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>openIssueNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>resolvedIssueNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>invalidIssueNo</dstFieldId>
          <dstFieldType>integer</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>overallCAPStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>shipmentAdvices</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>correctiveActionPlansImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,21">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>correctiveActionPlansAttachs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveActionPlansCopyDoc,22">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>referenceItem</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>null</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copyIssueAction" position="correctiveActionPlans_dataMappingRule.xlsx,copyIssueAction">
    <DataMappingRule description="Mapping Copy IssueAction" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-03-15" id="copyIssueAction" position="correctiveActionPlans_dataMappingRule.xlsx,copyIssueAction,1" srcEntityName="IssueAction" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyIssueAction,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyIssueAction,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copyCorrectiveAction" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveAction">
    <DataMappingRule description="Mapping Copy CorrectiveAction" domain="/" dstEntityName="IssueAction" dstEntityVersion="1" effectiveDate="2012-03-15" id="copyCorrectiveAction" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveAction,1" srcEntityName="CorrectiveAction" srcEntityVersion="1" status="1" updatedDate="2016-03-20">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveAction,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveAction,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>correctiveActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="issueActionSelectIssueType" position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType">
    <DataMappingRule description="Mapping from Issue type to IssueAction" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-02-20" id="issueActionSelectIssueType" position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType,1" srcEntityName="Codelist" srcEntityVersion="1" status="1" updatedDate="2019-03-05">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType,10">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>issueActions.issueType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,issueActionSelectIssueType,14">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.CapSelectIssueTypePostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="copyCorrectiveActionPlansImage" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansImage">
    <DataMappingRule description="Mapping from CorrectiveActionPlansImage to SampleTracker" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-03-15" id="copyCorrectiveActionPlansImage" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansImage,1" srcEntityName="CorrectiveActionPlansImage" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansImage,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansImage,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>correctiveActionPlansImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="copyCorrectiveActionPlansAttach" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansAttach">
    <DataMappingRule description="Mapping from CorrectiveActionPlansAttach to SampleTracker" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2012-03-15" id="copyCorrectiveActionPlansAttach" position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansAttach,1" srcEntityName="CorrectiveActionPlansAttach" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansAttach,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,copyCorrectiveActionPlansAttach,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>correctiveActionPlansAttachs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="correctiveactionplansDeliver" position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver">
    <DataMappingRule description="Mapping for CorrectiveActionPlans send to Vendor" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2016-12-03" id="correctiveactionplansDeliver" position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,1" srcEntityName="CorrectiveActionPlans" srcEntityVersion="1" status="1" updatedDate="2015-12-03">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>createdOn</srcFieldId>
          <dstFieldId>createdOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>entityVersion</srcFieldId>
          <dstFieldId>entityVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>issueTotalNo</srcFieldId>
          <dstFieldId>issueTotalNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>openIssueNo</srcFieldId>
          <dstFieldId>openIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>resolvedIssueNo</srcFieldId>
          <dstFieldId>resolvedIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>invalidIssueNo</srcFieldId>
          <dstFieldId>invalidIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>overallCAPStatus</srcFieldId>
          <dstFieldId>overallCAPStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>correctiveActionPlansNo</srcFieldId>
          <dstFieldId>correctiveActionPlansNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>reportDate</srcFieldId>
          <dstFieldId>reportDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>actualCompleteDate</srcFieldId>
          <dstFieldId>actualCompleteDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sourcingRecordNo</srcFieldId>
          <dstFieldId>sourcingRecordNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,38">
          <mappingType>Section</mappingType>
          <srcFieldId>auditor</srcFieldId>
          <dstFieldId>auditor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,39">
          <mappingType>Section</mappingType>
          <srcFieldId>reportChannel</srcFieldId>
          <dstFieldId>reportChannel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,40">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,41">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,42">
          <mappingType>Section</mappingType>
          <srcFieldId>referenceItem</srcFieldId>
          <dstFieldId>referenceItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,43">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,44">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,45">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord</srcFieldId>
          <dstFieldId>sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,46">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,47">
          <mappingType>Section</mappingType>
          <srcFieldId>vpo</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,48">
          <mappingType>Section</mappingType>
          <srcFieldId>capType</srcFieldId>
          <dstFieldId>capType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,49">
          <mappingType>Section</mappingType>
          <srcFieldId>factAudit</srcFieldId>
          <dstFieldId>factAudit</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,50">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdvices</srcFieldId>
          <dstFieldId>shipmentAdvices</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,51">
          <mappingType>Section</mappingType>
          <srcFieldId>issueActions</srcFieldId>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>issueNo</srcFieldId>
          <dstFieldId>issueNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>issueDesc</srcFieldId>
          <dstFieldId>issueDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>issueDetails</srcFieldId>
          <dstFieldId>issueDetails</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>totalAffectedQty</srcFieldId>
          <dstFieldId>totalAffectedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>overallActionsStatus</srcFieldId>
          <dstFieldId>overallActionsStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>isSample</srcFieldId>
          <dstFieldId>isSample</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>isPPMeeting</srcFieldId>
          <dstFieldId>isPPMeeting</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>isInlineInspection</srcFieldId>
          <dstFieldId>isInlineInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>isFinalInspection</srcFieldId>
          <dstFieldId>isFinalInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,67">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldNotUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>vendorComment,businessRefNo</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,68">
          <mappingType>Section</mappingType>
          <srcFieldId>severity</srcFieldId>
          <dstFieldId>issueActions.severity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,69">
          <mappingType>Section</mappingType>
          <srcFieldId>issueType</srcFieldId>
          <dstFieldId>issueActions.issueType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,70">
          <mappingType>Section</mappingType>
          <srcFieldId>issueStatus</srcFieldId>
          <dstFieldId>issueActions.issueStatus</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,71">
          <mappingType>Section</mappingType>
          <srcFieldId>image1</srcFieldId>
          <dstFieldId>issueActions.image1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,72">
          <mappingType>Section</mappingType>
          <srcFieldId>image2</srcFieldId>
          <dstFieldId>issueActions.image2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,73">
          <mappingType>Section</mappingType>
          <srcFieldId>image3</srcFieldId>
          <dstFieldId>issueActions.image3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,74">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment1</srcFieldId>
          <dstFieldId>issueActions.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,75">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment2</srcFieldId>
          <dstFieldId>issueActions.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,76">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment3</srcFieldId>
          <dstFieldId>issueActions.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,77">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActions</srcFieldId>
          <dstFieldId>issueActions.correctiveActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,78">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,79">
          <mappingType>Field</mappingType>
          <srcFieldId>proposedAction</srcFieldId>
          <dstFieldId>proposedAction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,80">
          <mappingType>Field</mappingType>
          <srcFieldId>expectedActionDate</srcFieldId>
          <dstFieldId>expectedActionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId>isVerified</srcFieldId>
          <dstFieldId>isVerified</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,84">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,85">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>$FieldToUpdate</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>proposedAction,expectedActionDate,isVerified,updateUserName,updatedOn,duid,capaStatus,round,preventiveAction,responsiblePerson,reviewComments</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,86">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansImages</srcFieldId>
          <dstFieldId>correctiveActionPlansImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansAttachs</srcFieldId>
          <dstFieldId>correctiveActionPlansAttachs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>sectionAttachmentList</srcFieldId>
          <dstFieldId>sectionAttachmentList</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,89">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther1</srcFieldId>
          <dstFieldId>correctiveActionPlansOther1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,90">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther2</srcFieldId>
          <dstFieldId>correctiveActionPlansOther2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,correctiveactionplansDeliver,91">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther3</srcFieldId>
          <dstFieldId>correctiveActionPlansOther3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="capVendorDeliver" position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver">
    <DataMappingRule description="Mapping for CorrectiveActionPlans send to Buyer" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2016-12-03" id="correctiveactionplansVendorDeliver" position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,1" srcEntityName="CorrectiveActionPlans" srcEntityVersion="1" status="1" updatedDate="2015-12-03">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,9">
          <mappingType>Field</mappingType>
          <srcFieldId>createUser</srcFieldId>
          <dstFieldId>createUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,10">
          <mappingType>Field</mappingType>
          <srcFieldId>createUserName</srcFieldId>
          <dstFieldId>createUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,11">
          <mappingType>Field</mappingType>
          <srcFieldId>createdOn</srcFieldId>
          <dstFieldId>createdOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,12">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUser</srcFieldId>
          <dstFieldId>updateUser</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,13">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,14">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,15">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,16">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,17">
          <mappingType>Field</mappingType>
          <srcFieldId>entityVersion</srcFieldId>
          <dstFieldId>entityVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,18">
          <mappingType>Field</mappingType>
          <srcFieldId>isCpmInitialized</srcFieldId>
          <dstFieldId>isCpmInitialized</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,19">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,20">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,21">
          <mappingType>Field</mappingType>
          <srcFieldId>refNo</srcFieldId>
          <dstFieldId>refNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,22">
          <mappingType>Field</mappingType>
          <srcFieldId>businessRefNo</srcFieldId>
          <dstFieldId>businessRefNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,23">
          <mappingType>Field</mappingType>
          <srcFieldId>issueTotalNo</srcFieldId>
          <dstFieldId>issueTotalNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,24">
          <mappingType>Field</mappingType>
          <srcFieldId>openIssueNo</srcFieldId>
          <dstFieldId>openIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,25">
          <mappingType>Field</mappingType>
          <srcFieldId>resolvedIssueNo</srcFieldId>
          <dstFieldId>resolvedIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,26">
          <mappingType>Field</mappingType>
          <srcFieldId>invalidIssueNo</srcFieldId>
          <dstFieldId>invalidIssueNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,27">
          <mappingType>Field</mappingType>
          <srcFieldId>overallCAPStatus</srcFieldId>
          <dstFieldId>overallCAPStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,28">
          <mappingType>Field</mappingType>
          <srcFieldId>correctiveActionPlansNo</srcFieldId>
          <dstFieldId>correctiveActionPlansNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,29">
          <mappingType>Field</mappingType>
          <srcFieldId>shortDesc</srcFieldId>
          <dstFieldId>shortDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,30">
          <mappingType>Field</mappingType>
          <srcFieldId>reportDate</srcFieldId>
          <dstFieldId>reportDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,31">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>dueDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,32">
          <mappingType>Field</mappingType>
          <srcFieldId>actualCompleteDate</srcFieldId>
          <dstFieldId>actualCompleteDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,33">
          <mappingType>Field</mappingType>
          <srcFieldId>instructions</srcFieldId>
          <dstFieldId>instructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,34">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorEmail</srcFieldId>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,35">
          <mappingType>Field</mappingType>
          <srcFieldId>itemNo</srcFieldId>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,36">
          <mappingType>Field</mappingType>
          <srcFieldId>sourcingRecordNo</srcFieldId>
          <dstFieldId>sourcingRecordNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,37">
          <mappingType>Field</mappingType>
          <srcFieldId>shipmentDate</srcFieldId>
          <dstFieldId>shipmentDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,38">
          <mappingType>Section</mappingType>
          <srcFieldId>auditor</srcFieldId>
          <dstFieldId>auditor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,39">
          <mappingType>Section</mappingType>
          <srcFieldId>reportChannel</srcFieldId>
          <dstFieldId>reportChannel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,40">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,41">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,42">
          <mappingType>Section</mappingType>
          <srcFieldId>referenceItem</srcFieldId>
          <dstFieldId>referenceItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,43">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,44">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>item</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,45">
          <mappingType>Section</mappingType>
          <srcFieldId>sourcingRecord</srcFieldId>
          <dstFieldId>sourcingRecord</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,46">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,47">
          <mappingType>Section</mappingType>
          <srcFieldId>vpo</srcFieldId>
          <dstFieldId>vpo</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,48">
          <mappingType>Section</mappingType>
          <srcFieldId>capType</srcFieldId>
          <dstFieldId>capType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,49">
          <mappingType>Section</mappingType>
          <srcFieldId>factAudit</srcFieldId>
          <dstFieldId>factAudit</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,50">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentAdvices</srcFieldId>
          <dstFieldId>shipmentAdvices</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,51">
          <mappingType>Section</mappingType>
          <srcFieldId>issueActions</srcFieldId>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,52">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,53">
          <mappingType>Field</mappingType>
          <srcFieldId>issueNo</srcFieldId>
          <dstFieldId>issueNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,54">
          <mappingType>Field</mappingType>
          <srcFieldId>issueDesc</srcFieldId>
          <dstFieldId>issueDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,55">
          <mappingType>Field</mappingType>
          <srcFieldId>issueDetails</srcFieldId>
          <dstFieldId>issueDetails</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,56">
          <mappingType>Field</mappingType>
          <srcFieldId>totalAffectedQty</srcFieldId>
          <dstFieldId>totalAffectedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,57">
          <mappingType>Field</mappingType>
          <srcFieldId>overallActionsStatus</srcFieldId>
          <dstFieldId>overallActionsStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,58">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComment</srcFieldId>
          <dstFieldId>vendorComment</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,59">
          <mappingType>Field</mappingType>
          <srcFieldId>remarks</srcFieldId>
          <dstFieldId>remarks</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,60">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,61">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,62">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,63">
          <mappingType>Field</mappingType>
          <srcFieldId>isSample</srcFieldId>
          <dstFieldId>isSample</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,64">
          <mappingType>Field</mappingType>
          <srcFieldId>isPPMeeting</srcFieldId>
          <dstFieldId>isPPMeeting</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,65">
          <mappingType>Field</mappingType>
          <srcFieldId>isInlineInspection</srcFieldId>
          <dstFieldId>isInlineInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,66">
          <mappingType>Field</mappingType>
          <srcFieldId>isFinalInspection</srcFieldId>
          <dstFieldId>isFinalInspection</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,67">
          <mappingType>Section</mappingType>
          <srcFieldId>severity</srcFieldId>
          <dstFieldId>issueActions.severity</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,68">
          <mappingType>Section</mappingType>
          <srcFieldId>issueType</srcFieldId>
          <dstFieldId>issueActions.issueType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,69">
          <mappingType>Section</mappingType>
          <srcFieldId>issueStatus</srcFieldId>
          <dstFieldId>issueActions.issueStatus</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,70">
          <mappingType>Section</mappingType>
          <srcFieldId>image1</srcFieldId>
          <dstFieldId>issueActions.image1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,71">
          <mappingType>Section</mappingType>
          <srcFieldId>image2</srcFieldId>
          <dstFieldId>issueActions.image2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,72">
          <mappingType>Section</mappingType>
          <srcFieldId>image3</srcFieldId>
          <dstFieldId>issueActions.image3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,73">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment1</srcFieldId>
          <dstFieldId>issueActions.attachment1</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,74">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment2</srcFieldId>
          <dstFieldId>issueActions.attachment2</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,75">
          <mappingType>Section</mappingType>
          <srcFieldId>attachment3</srcFieldId>
          <dstFieldId>issueActions.attachment3</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,76">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActions</srcFieldId>
          <dstFieldId>issueActions.correctiveActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,77">
          <mappingType>Field</mappingType>
          <srcFieldId>internalSeqNo</srcFieldId>
          <dstFieldId>internalSeqNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,78">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorAction</srcFieldId>
          <dstFieldId>vendorAction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,79">
          <mappingType>Field</mappingType>
          <srcFieldId>possibleCause</srcFieldId>
          <dstFieldId>possibleCause</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,80">
          <mappingType>Field</mappingType>
          <srcFieldId>actualActionDate</srcFieldId>
          <dstFieldId>actualActionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,81">
          <mappingType>Field</mappingType>
          <srcFieldId>updateUserName</srcFieldId>
          <dstFieldId>updateUserName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,82">
          <mappingType>Field</mappingType>
          <srcFieldId>updatedOn</srcFieldId>
          <dstFieldId>updatedOn</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,83">
          <mappingType>Field</mappingType>
          <srcFieldId>duid</srcFieldId>
          <dstFieldId>duid</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,84">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansImages</srcFieldId>
          <dstFieldId>correctiveActionPlansImages</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,85">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansAttachs</srcFieldId>
          <dstFieldId>correctiveActionPlansAttachs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,86">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther1</srcFieldId>
          <dstFieldId>correctiveActionPlansOther1</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,87">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther2</srcFieldId>
          <dstFieldId>correctiveActionPlansOther2</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,capVendorDeliver,88">
          <mappingType>Section</mappingType>
          <srcFieldId>correctiveActionPlansOther3</srcFieldId>
          <dstFieldId>correctiveActionPlansOther3</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="factAuditNewCorrectiveActions" position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions">
    <DataMappingRule description="Mapping from factAudit to CorrectiveActionPlans" domain="/" dstEntityName="CorrectiveActionPlans" dstEntityVersion="1" effectiveDate="2021-04-14" id="factAuditNewCorrectiveActions" position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,1" srcEntityName="FactAudit" srcEntityVersion="1" status="1" updatedDate="2021-04-14">
      <elements id="mappingRule">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>factAudit</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,10">
          <mappingType>Section</mappingType>
          <srcFieldId>vendorId</srcFieldId>
          <dstFieldId>vendor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,11">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorCode</srcFieldId>
          <dstFieldId>vendorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorName</srcFieldId>
          <dstFieldId>vendorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorEmail</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.email</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,14">
          <mappingType>Section</mappingType>
          <srcFieldId>factId</srcFieldId>
          <dstFieldId>factory</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,15">
          <mappingType>Section</mappingType>
          <srcFieldId>factoryCorrectiveActionsSummary</srcFieldId>
          <dstFieldId>issueActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,16">
          <mappingType>Field</mappingType>
          <srcFieldId>requirement</srcFieldId>
          <dstFieldId>issueDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,17">
          <mappingType>Field</mappingType>
          <srcFieldId>comments</srcFieldId>
          <dstFieldId>issueDetails</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,18">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>issueActions.correctiveActions</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,19">
          <mappingType>Field</mappingType>
          <srcFieldId>correctiveAction</srcFieldId>
          <dstFieldId>proposedAction</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,20">
          <mappingType>Field</mappingType>
          <srcFieldId>dueDate</srcFieldId>
          <dstFieldId>expectedActionDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,21">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,22">
          <mappingType>Section</mappingType>
          <srcFieldId>auditor</srcFieldId>
          <dstFieldId>auditor</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="correctiveActionPlans_dataMappingRule.xlsx,factAuditNewCorrectiveActions,26">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.FactAuditNewCorrectiveActionsPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
