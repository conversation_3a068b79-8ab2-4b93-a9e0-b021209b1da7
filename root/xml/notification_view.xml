<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="notification" position="notification_view.xlsx">
  <sheet id="inboxView" position="notification_view.xlsx,inboxView">
    <ViewDefinition advancedSearchId="" description="Home Dashboard - Inbox" id="inboxView" label="Inbox" moduleId="notification" position="notification_view.xlsx,inboxView,1" queryId="listInboxes" searchCriterion="">
      <elements id="options">
        <element position="notification_view.xlsx,inboxView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="notification_view.xlsx,inboxView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,inboxView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,inboxView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="notification_view.xlsx,inboxView,12">
          <id>DISABLE_ADD_REMOVE_COLUMNS</id>
          <label/>
          <value>disable</value>
        </element>
        <element position="notification_view.xlsx,inboxView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>rowRenderer=com.core.cbx.ui.zk.cul.grid.renderer.CustInboxRowRenderer</value>
        </element>
        <element position="notification_view.xlsx,inboxView,14">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>deleted=false=boolean</value>
        </element>
        <element position="notification_view.xlsx,inboxView,15">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>NotificationToUser</value>
        </element>
        <element position="notification_view.xlsx,inboxView,16">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>notificationToUserId:id:string,id:notification.id:string,isRead:isRead:boolean,refDocId:refDocId:string,isCpmInitialized:notification.isCpmInitialized:boolean,isLatest:notification.isLatest:boolean,fileNameStr:fileNameStr:string,from:notification.createdBy:string,content:notification.content:string,fileIdStr:fileIdStr:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="notification_view.xlsx,inboxView,20">
          <id>deleteInboxMessage</id>
          <label>Delete</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="notification_view.xlsx,inboxView,21">
          <id>inboxMarkAsRead</id>
          <label>Mark Selected as Read</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="notification_view.xlsx,inboxView,22">
          <id>inboxMarkAsUnread</id>
          <label>Mark Selected as Unread</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="notification_view.xlsx,inboxView,27">
          <id>hasAttachment</id>
          <label>Has Attachment?</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>cn.has_Attachment</mappedField>
          <esMapping>hasAttachment:hasAttachment:integer</esMapping>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,28">
          <id>attachmentFlag</id>
          <label>Attachment</label>
          <type>com.core.cbx.ui.renderer.AttachFlagCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>25px</width>
          <visibility>1</visibility>
          <mappedField>cn.has_Attachment</mappedField>
          <esMapping>attachmentFlag:hasAttachment:integer</esMapping>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,29">
          <id>date</id>
          <label>Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>76px</width>
          <visibility>1</visibility>
          <mappedField>cn.SENT_ON</mappedField>
          <esMapping>date:notification.sentOn:timestamp</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="notification_view.xlsx,inboxView,30">
          <id>refDoc</id>
          <label>Reference</label>
          <type>com.core.cbx.inbox.ui.InboxReferencesCellRenderer</type>
          <format/>
          <action>OpenReferenceDocAction</action>
          <actionParams>refFieldDocId=docId&amp;refFieldModule=moduleId</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>ref.REF_DOC_REF_NO</mappedField>
          <esMapping>refDoc:refDoc:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,31">
          <id>notificationType</id>
          <label>Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cn.NOTIFICATION_TYPE</mappedField>
          <esMapping>notificationType:notificationType:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,32">
          <id>subject</id>
          <label>Subject</label>
          <type>HyperLink</type>
          <format/>
          <action>PopupInboxMessageAction</action>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>450px</width>
          <visibility>1</visibility>
          <mappedField>cn.SUBJECT</mappedField>
          <esMapping>subject:notification.subject:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,33">
          <id>message</id>
          <label>Message</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>CN.CONTENT_LEFT1000</mappedField>
          <esMapping>message:notification.contentLeft1000:string</esMapping>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,inboxView,34">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>65px</width>
          <visibility>0</visibility>
          <mappedField>cn.UPDATED_ON</mappedField>
          <esMapping>updatedOn:notification.updatedOn:timestamp</esMapping>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="newInboxView" position="notification_view.xlsx,newInboxView">
    <ViewDefinition advancedSearchId="" description="Home Dashboard - Inbox" id="newInboxView" label="Inbox" moduleId="notification" position="notification_view.xlsx,newInboxView,1" queryId="listInboxes" searchCriterion="">
      <elements id="options">
        <element position="notification_view.xlsx,newInboxView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,12">
          <id>DISABLE_ADD_REMOVE_COLUMNS</id>
          <label/>
          <value>disable</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>rowRenderer=com.core.cbx.ui.zk.cul.grid.renderer.CustInboxRowRenderer</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,14">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>deleted=false=boolean</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,15">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>NotificationToUser</value>
        </element>
        <element position="notification_view.xlsx,newInboxView,16">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>notificationToUserId:id:string,id:notification.id:string,isRead:isRead:boolean,refDocId:refDocId:string,isCpmInitialized:notification.isCpmInitialized:boolean,isLatest:notification.isLatest:boolean,fileNameStr:fileNameStr:string,from:notification.createdBy:string,content:notification.content:string,fileIdStr:fileIdStr:string,refNo:notification.id:string</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="notification_view.xlsx,newInboxView,20">
          <id>markAs</id>
          <label>Mark as</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="notification_view.xlsx,newInboxView,21">
          <id>inboxMarkAsRead</id>
          <label>Read</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="notification_view.xlsx,newInboxView,22">
          <id>deleteInboxMessage</id>
          <label>Delete</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>markAs</buttonGroup>
        </element>
        <element position="notification_view.xlsx,newInboxView,23">
          <id>inboxMarkAllAsRead</id>
          <label>Mark All as Read</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="notification_view.xlsx,newInboxView,28">
          <id>date</id>
          <label>Sent Date</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>cn.SENT_ON</mappedField>
          <esMapping>date:notification.sentOn:timestamp</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="notification_view.xlsx,newInboxView,29">
          <id>notificationType</id>
          <label>From</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>cn.NOTIFICATION_TYPE</mappedField>
          <esMapping>notificationType:notificationType:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,newInboxView,30">
          <id>businessRefNo</id>
          <label>Reference</label>
          <type>NotificationReferencesLink</type>
          <format/>
          <action>OpenReferenceDocAction</action>
          <actionParams>refFieldDocId=docId&amp;refFieldModule=moduleId</actionParams>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>1</visibility>
          <mappedField>ref.REF_DOC_REF_NO</mappedField>
          <esMapping>businessRefNo:businessRefNo:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,newInboxView,31">
          <id>subject</id>
          <label>Subject</label>
          <type>NotificationSubjectLink</type>
          <format/>
          <action>PopupInboxMessageAction</action>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>450px</width>
          <visibility>1</visibility>
          <mappedField>cn.SUBJECT</mappedField>
          <esMapping>subject:notification.subject:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,newInboxView,32">
          <id>message</id>
          <label>Content</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CN.CONTENT_LEFT1000</mappedField>
          <esMapping>message:notification.content:string</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,newInboxView,33">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>cn.VERSION</mappedField>
          <esMapping>version:notification.version:integer</esMapping>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="dashboardInboxView" position="notification_view.xlsx,dashboardInboxView">
    <ViewDefinition advancedSearchId="" description="Home Dashboard - Inbox" id="dashboardInboxView" label="Alerts" moduleId="notification" position="notification_view.xlsx,dashboardInboxView,1" queryId="listInboxes" searchCriterion="">
      <elements id="options">
        <element position="notification_view.xlsx,dashboardInboxView,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,12">
          <id>DISABLE_ADD_REMOVE_COLUMNS</id>
          <label/>
          <value>disable</value>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,13">
          <id>EXTRA_PARAMS</id>
          <label/>
          <value>rowRenderer=com.core.cbx.ui.zk.cul.grid.renderer.CustInboxRowRenderer</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="notification_view.xlsx,dashboardInboxView,17">
          <id>viewAll</id>
          <label>View all</label>
          <type>Button</type>
          <actionParams/>
          <buttonGroup/>
        </element>
      </elements>
      <elements id="columns">
        <element position="notification_view.xlsx,dashboardInboxView,22">
          <id>hasAttachment</id>
          <label>Has Attachment?</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>cn.has_Attachment</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,23">
          <id>attachmentFlag</id>
          <label>Attachment</label>
          <type>com.core.cbx.ui.renderer.AttachFlagCellRenderer</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>cn.has_Attachment</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,24">
          <id>refDoc</id>
          <label>Reference</label>
          <type>com.core.cbx.inbox.ui.InboxReferencesCellRenderer</type>
          <format/>
          <action>OpenReferenceDocAction</action>
          <actionParams>refFieldDocId=docId&amp;refFieldModule=moduleId</actionParams>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>25px</width>
          <visibility>0</visibility>
          <mappedField>ref.REF_DOC_REF_NO</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,25">
          <id>subject</id>
          <label>Subject</label>
          <type>HyperLink</type>
          <format/>
          <action>PopupInboxMessageAction</action>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>454px</width>
          <visibility>1</visibility>
          <mappedField>cn.SUBJECT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,26">
          <id>from</id>
          <label>From</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>114px</width>
          <visibility>1</visibility>
          <mappedField>cn.FORM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,27">
          <id>date</id>
          <label>Date</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>74px</width>
          <visibility>1</visibility>
          <mappedField>cn.SENT_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,28">
          <id>message</id>
          <label>Message</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>CN.CONTENT_LEFT1000</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,29">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>cn.UPDATED_ON</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="notification_view.xlsx,dashboardInboxView,30">
          <id>notificationType</id>
          <label>Notification Type</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>0</sortable>
          <width>0px</width>
          <visibility>0</visibility>
          <mappedField>cn.NOTIFICATION_TYPE</mappedField>
          <allowSimpleSearch>0</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
