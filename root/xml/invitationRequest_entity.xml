<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity module="invitationRequest" position="invitationRequest_entity.xlsx">
  <sheet id="_system" position="invitationRequest_entity.xlsx,_system">
    <ProjectInfo client="Base" position="invitationRequest_entity.xlsx,_system,1" project="SCMS" release_no="1.00"/>
    <ProductVersion position="invitationRequest_entity.xlsx,_system,7">
      <elements id="default">
        <element position="invitationRequest_entity.xlsx,_system,10">
          <updated_on>09-Sep-2015</updated_on>
          <Summary>Creation</Summary>
          <release_no>1.00</release_no>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="invitationRequest_entity.xlsx,generalInfo">
    <GeneralInfo is_system_entity="N" main_entity="InvitationRequest" module="invitationRequest" position="invitationRequest_entity.xlsx,generalInfo,1" revision_tracking="" version="1" version_tracking="ALL"/>
  </sheet>
  <sheet id="entityDef" position="invitationRequest_entity.xlsx,entityDef">
    <Entity name="InvitationRequest" position="invitationRequest_entity.xlsx,entityDef,1" ref_pattern="${invitationId}" report_table_name="INVITATION_REQUEST" table_name="CNT_INVITATION_REQUEST">
      <elements id="reference">
        <element position="invitationRequest_entity.xlsx,entityDef,8">
          <entity_field_id>requester</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>User.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESTER_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,9">
          <entity_field_id>requesterName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>requester.userName</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>REQUESTER_NAME</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,10">
          <entity_field_id>resendInvitationRequest</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>InvitationRequest.id</entity_lookup>
          <entity_lookup_type>Snapshot</entity_lookup_type>
          <snapshot_field/>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RESEND_INVITATION_REQUEST_SID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,11">
          <entity_field_id>resentFrom</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <entity_lookup_type/>
          <snapshot_field>resendInvitationRequest.invitationId</snapshot_field>
          <dataType/>
          <data1/>
          <data2/>
          <report_column_name>RESENT_FROM</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
      </elements>
      <elements id="header">
        <element position="invitationRequest_entity.xlsx,entityDef,15">
          <entity_field_id>invitationId</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_INVITATION_REQUEST_ID","system.pattern.invitationId","IRQ-${domainId}-#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INVITATION_ID</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,16">
          <entity_field_id>relationshipType</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>RELATIONSHIP_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,17">
          <entity_field_id>requestType</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUEST_TYPE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,18">
          <entity_field_id>invitationDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INVITATION_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,19">
          <entity_field_id>expiryDate</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>EXPIRY_DATE</report_column_name>
          <tracking_level>2</tracking_level>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,20">
          <entity_field_id>optionalMessage</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>OPTIONAL_MESSAGE</report_column_name>
          <tracking_level>1</tracking_level>
        </element>
      </elements>
      <elements id="collection">
        <element position="invitationRequest_entity.xlsx,entityDef,24">
          <entity_field_id>invitedVendor</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InvitedVendor.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,25">
          <entity_field_id>InvitedFact</entity_field_id>
          <entity_field_type>collection</entity_field_type>
          <entity_lookup>InvitedFact.parentId</entity_lookup>
          <refer_table/>
          <key_column/>
          <lookup_column/>
          <generate_refer_table/>
          <description/>
          <tracking_level/>
        </element>
      </elements>
    </Entity>
    <Entity name="InvitedVendor" position="invitationRequest_entity.xlsx,entityDef,28" ref_pattern="${requestId}" report_table_name="INVITED_VENDOR" table_name="CNT_INVITED_VENDOR">
      <elements id="header">
        <element position="invitationRequest_entity.xlsx,entityDef,35">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PARENT_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,36">
          <entity_field_id>requestId</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_INVITED_VENDORS_ID","system.pattern.requestId","IRQIV-${domainId}-#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUEST_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,37">
          <entity_field_id>email</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>EMAIL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,38">
          <entity_field_id>contactName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CONTACT_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,39">
          <entity_field_id>companyName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>COMPANY_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,40">
          <entity_field_id>vendorId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Vendor.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,41">
          <entity_field_id>invitationStatus</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INVITATION_STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,42">
          <entity_field_id>isDefault</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_DEFAULT</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,43">
          <entity_field_id>isSubUser</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>IS_SUB_USER</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
    <Entity name="InvitedFact" position="invitationRequest_entity.xlsx,entityDef,46" ref_pattern="${requestId}" report_table_name="INVITED_VENDOR" table_name="CNT_INVITED_FACT">
      <elements id="header">
        <element position="invitationRequest_entity.xlsx,entityDef,53">
          <entity_field_id>parentId</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>PARENT_ID</report_column_name>
          <tracking_level>1</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,54">
          <entity_field_id>requestId</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.DomainPatternSeqGenerator("CBX_SEQ_INVITED_VENDORS_ID","system.pattern.requestId","IRQIV-${domainId}-#{Date:YYMM}-#{Seq:6}")</generator>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>REQUEST_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,55">
          <entity_field_id>email</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>EMAIL</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,56">
          <entity_field_id>contactName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>CONTACT_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,57">
          <entity_field_id>companyName</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>COMPANY_NAME</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,58">
          <entity_field_id>factId</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>Fact.id</entity_lookup>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>VENDOR_ID</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type>Dynamic</entity_lookup_type>
        </element>
        <element position="invitationRequest_entity.xlsx,entityDef,59">
          <entity_field_id>invitationStatus</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <data1/>
          <data2/>
          <transitive_fields/>
          <report_column_name>INVITATION_STATUS</report_column_name>
          <tracking_level>2</tracking_level>
          <entity_lookup_type/>
        </element>
      </elements>
    </Entity>
  </sheet>
  <sheet id="status" position="invitationRequest_entity.xlsx,status">
    <Status position="invitationRequest_entity.xlsx,status,1">
      <elements id="workflow">
        <element position="invitationRequest_entity.xlsx,status,4">
          <code>duplicated</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,5">
          <code>pending</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,6">
          <code>connected</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,7">
          <code>expired</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,8">
          <code>customStatus01</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,9">
          <code>customStatus02</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,10">
          <code>customStatus03</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,11">
          <code>customStatus04</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,12">
          <code>customStatus05</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,13">
          <code>customStatus06</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,14">
          <code>customStatus07</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,15">
          <code>customStatus08</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,16">
          <code>customStatus09</code>
        </element>
        <element position="invitationRequest_entity.xlsx,status,17">
          <code>customStatus10</code>
        </element>
      </elements>
      <elements id="document"/>
      <elements id="editing"/>
    </Status>
  </sheet>
</entity>
