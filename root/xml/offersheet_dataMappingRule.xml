<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<dataMappingRule module="offersheet" position="offersheet_dataMappingRule.xlsx">
  <sheet id="osCopyItem" position="offersheet_dataMappingRule.xlsx,osCopyItem">
    <DataMappingRule description="Mapping from offersheet item copy" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-14" id="osCopyItem" position="offersheet_dataMappingRule.xlsx,osCopyItem,1" srcEntityName="OsItem" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osCopyItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osCopyItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osCopyItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osCopyAttachment" position="offersheet_dataMappingRule.xlsx,osCopyAttachment">
    <DataMappingRule description="Mapping from offersheet attachment copy" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-14" id="osCopyAttachment" position="offersheet_dataMappingRule.xlsx,osCopyAttachment,1" srcEntityName="OsAttachment" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osCopyAttachment,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osCopyAttachment,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osAttachment</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osCopyDoc" position="offersheet_dataMappingRule.xlsx,osCopyDoc">
    <DataMappingRule description="Mapping from offersheet copy" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-14" id="osCopyDoc" position="offersheet_dataMappingRule.xlsx,osCopyDoc,1" srcEntityName="Offersheet" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osCopyDoc,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osGenCpo" position="offersheet_dataMappingRule.xlsx,osGenCpo">
    <DataMappingRule description="Mapping from offersheet to Cpo" domain="/" dstEntityName="Cpo" dstEntityVersion="1" effectiveDate="2012-03-14" id="osGenCpo" position="offersheet_dataMappingRule.xlsx,osGenCpo,1" srcEntityName="Offersheet" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId>status</srcFieldId>
          <dstFieldId>status</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,10">
          <mappingType>Field</mappingType>
          <srcFieldId>docStatus</srcFieldId>
          <dstFieldId>docStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>active</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>editingStatus</srcFieldId>
          <dstFieldId>editingStatus</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>draft</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId>version</srcFieldId>
          <dstFieldId>version</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>1</mappedValue>
          <mappedValueType>Constant</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId>projectReference</srcFieldId>
          <dstFieldId>projRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,14">
          <mappingType>Field</mappingType>
          <srcFieldId>termsConditions</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>customerContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.contactName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>phoneNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.mobileNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.faxNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.email</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.paymentInstruction</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,23">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,24">
          <mappingType>Section</mappingType>
          <srcFieldId>custCurrency</srcFieldId>
          <dstFieldId>currency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,25">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.businessName</srcFieldId>
          <dstFieldId>custName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId>custId.custCode</srcFieldId>
          <dstFieldId>custCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,28">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,29">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,30">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,31">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem</srcFieldId>
          <dstFieldId>cpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>itemName</srcFieldId>
          <dstFieldId>itemName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemRef</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemId.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>description</srcFieldId>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.specId.version</srcFieldId>
          <dstFieldId>specVersion</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>sellPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,38">
          <mappingType>Field</mappingType>
          <srcFieldId>buyingPrice</srcFieldId>
          <dstFieldId>buyPrice</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,39">
          <mappingType>Field</mappingType>
          <srcFieldId>buyingCost</srcFieldId>
          <dstFieldId>buyCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,40">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.vqId.vqNo</srcFieldId>
          <dstFieldId>quoteNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,41">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,42">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,43">
          <mappingType>Field</mappingType>
          <srcFieldId>innerQty</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,44">
          <mappingType>Field</mappingType>
          <srcFieldId>outerQty</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,45">
          <mappingType>Field</mappingType>
          <srcFieldId>outerLength</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,46">
          <mappingType>Field</mappingType>
          <srcFieldId>outerWidth</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,47">
          <mappingType>Field</mappingType>
          <srcFieldId>outerHeight</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,48">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCartonCFT</srcFieldId>
          <dstFieldId>outerCartonCFT</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,49">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,50">
          <mappingType>Field</mappingType>
          <srcFieldId>outerGrossWeight</srcFieldId>
          <dstFieldId>gw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,51">
          <mappingType>Field</mappingType>
          <srcFieldId>outerNetWeight</srcFieldId>
          <dstFieldId>nw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,52">
          <mappingType>Field</mappingType>
          <srcFieldId>outerCbm</srcFieldId>
          <dstFieldId>cbm</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,53">
          <mappingType>Field</mappingType>
          <srcFieldId>specVariance</srcFieldId>
          <dstFieldId>specInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,54">
          <mappingType>Field</mappingType>
          <srcFieldId>osId</srcFieldId>
          <dstFieldId>osId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,55">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>cpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,56">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.weightUOM</srcFieldId>
          <dstFieldId>cpoItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,57">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.dimensionUOM</srcFieldId>
          <dstFieldId>cpoItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,58">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.countryOfOrigin</srcFieldId>
          <dstFieldId>cpoItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,59">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.portOfLoading</srcFieldId>
          <dstFieldId>cpoItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,60">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.market</srcFieldId>
          <dstFieldId>cpoItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,61">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.channel</srcFieldId>
          <dstFieldId>cpoItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,62">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.uom</srcFieldId>
          <dstFieldId>cpoItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,63">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId</srcFieldId>
          <dstFieldId>cpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,64">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.specId</srcFieldId>
          <dstFieldId>cpoItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,65">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.vqId</srcFieldId>
          <dstFieldId>cpoItem.quoteId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,66">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.hierarchy</srcFieldId>
          <dstFieldId>cpoItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,67">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.productCategory</srcFieldId>
          <dstFieldId>cpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,68">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.fileId</srcFieldId>
          <dstFieldId>cpoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,69">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>cpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,70">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>cpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,71">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>cpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,72">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>cpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,73">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.osItemSize</srcFieldId>
          <dstFieldId>cpoItem.cpoItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,74">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,75">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,76">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,77">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,78">
          <mappingType>Field</mappingType>
          <srcFieldId>displayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,79">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemSize</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,80">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.osItemColor</srcFieldId>
          <dstFieldId>cpoItem.cpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,81">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,82">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,83">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,84">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,85">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,86">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,87">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.osItemCs</srcFieldId>
          <dstFieldId>cpoItem.cpoItemCs</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,88">
          <mappingType>Field</mappingType>
          <srcFieldId>osItemId</srcFieldId>
          <dstFieldId>itemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,89">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,90">
          <mappingType>Field</mappingType>
          <srcFieldId>itemSizeId</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,91">
          <mappingType>Field</mappingType>
          <srcFieldId>price</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,92">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSizeMoq</srcFieldId>
          <dstFieldId>colorSizeQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,96">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.ExtraChildEntityForCustIdProcessor</implementationClass>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osGenCpo,97">
          <type>PostProcessor</type>
          <templateName>osGenCpoTemplate</templateName>
          <templateFile>set_up_cpo_item_color_size_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.SetOSToCpoItemProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectContact" position="offersheet_dataMappingRule.xlsx,osSelectContact">
    <DataMappingRule description="Mapping for Customer Contact to OfferSheet Contact" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="osSelectContact" position="offersheet_dataMappingRule.xlsx,osSelectContact,1" srcEntityName="CustContact" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,10">
          <mappingType>Field</mappingType>
          <srcFieldId>firstName</srcFieldId>
          <dstFieldId>firstName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,11">
          <mappingType>Field</mappingType>
          <srcFieldId>lastName</srcFieldId>
          <dstFieldId>lastName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,12">
          <mappingType>Field</mappingType>
          <srcFieldId>position</srcFieldId>
          <dstFieldId>position</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,13">
          <mappingType>Field</mappingType>
          <srcFieldId>department</srcFieldId>
          <dstFieldId>department</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,14">
          <mappingType>Field</mappingType>
          <srcFieldId>telNo</srcFieldId>
          <dstFieldId>telNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,15">
          <mappingType>Field</mappingType>
          <srcFieldId>mobileNo</srcFieldId>
          <dstFieldId>mobileNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,16">
          <mappingType>Field</mappingType>
          <srcFieldId>faxNo</srcFieldId>
          <dstFieldId>faxNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,17">
          <mappingType>Field</mappingType>
          <srcFieldId>email</srcFieldId>
          <dstFieldId>email</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,18">
          <mappingType>Section</mappingType>
          <srcFieldId>title</srcFieldId>
          <dstFieldId>osContact.title</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectContact,19">
          <mappingType>Section</mappingType>
          <srcFieldId>contactTypeId</srcFieldId>
          <dstFieldId>osContact.contactTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectAddress" position="offersheet_dataMappingRule.xlsx,osSelectAddress">
    <DataMappingRule description="Mapping for Customer Address to OfferSheet Address" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="osSelectAddress" position="offersheet_dataMappingRule.xlsx,osSelectAddress,1" srcEntityName="CustAddress" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,10">
          <mappingType>Field</mappingType>
          <srcFieldId>address1</srcFieldId>
          <dstFieldId>address1</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,11">
          <mappingType>Field</mappingType>
          <srcFieldId>address2</srcFieldId>
          <dstFieldId>address2</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,12">
          <mappingType>Field</mappingType>
          <srcFieldId>address3</srcFieldId>
          <dstFieldId>address3</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,13">
          <mappingType>Field</mappingType>
          <srcFieldId>address4</srcFieldId>
          <dstFieldId>address4</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,14">
          <mappingType>Field</mappingType>
          <srcFieldId>companyName</srcFieldId>
          <dstFieldId>companyName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,15">
          <mappingType>Field</mappingType>
          <srcFieldId>city</srcFieldId>
          <dstFieldId>city</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,16">
          <mappingType>Field</mappingType>
          <srcFieldId>state</srcFieldId>
          <dstFieldId>state</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,17">
          <mappingType>Field</mappingType>
          <srcFieldId>postalCode</srcFieldId>
          <dstFieldId>postalCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,18">
          <mappingType>Section</mappingType>
          <srcFieldId>country</srcFieldId>
          <dstFieldId>osAddress.country</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,19">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfDischarge</srcFieldId>
          <dstFieldId>osAddress.port</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,20">
          <mappingType>Section</mappingType>
          <srcFieldId>language</srcFieldId>
          <dstFieldId>osAddress.language</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectAddress,21">
          <mappingType>Section</mappingType>
          <srcFieldId>addressTypeId</srcFieldId>
          <dstFieldId>osAddress.addressTypeId</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectItem" position="offersheet_dataMappingRule.xlsx,osSelectItem">
    <DataMappingRule description="Mapping from item to Offersheet Item" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="osSelectItem" position="offersheet_dataMappingRule.xlsx,osSelectItem,1" srcEntityName="Item" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,12">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultUom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,13">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,14">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,15">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,16">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,17">
          <mappingType>Section</mappingType>
          <srcFieldId>defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,18">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,19">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,20">
          <mappingType>Section</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>osItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,21">
          <mappingType>Section</mappingType>
          <srcFieldId>itemSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false &amp;&amp; entity.dimension.code = 'SIZE'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,23">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,24">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,26">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>itemColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isPrimary=true &amp;&amp; entity.isInactive=false</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,28">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,29">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,30">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,31">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectItem,32">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectCust" position="offersheet_dataMappingRule.xlsx,osSelectCust">
    <DataMappingRule description="Mapping from Customer" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-14" id="osSelectCust" position="offersheet_dataMappingRule.xlsx,osSelectCust,1" srcEntityName="Cust" srcEntityVersion="1" status="1" updatedDate="2016-03-16">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectCust,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectCust,9">
          <mappingType>Section</mappingType>
          <srcFieldId>currency</srcFieldId>
          <dstFieldId>custCurrency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osChargeCopy" position="offersheet_dataMappingRule.xlsx,osChargeCopy">
    <DataMappingRule description="Mapping for Customer Contact to OfferSheet Contact" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2011-01-01" id="osChargeCopy" position="offersheet_dataMappingRule.xlsx,osChargeCopy,1" srcEntityName="OsCharge" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osChargeCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osChargeCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osCharge</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osAddressCopy" position="offersheet_dataMappingRule.xlsx,osAddressCopy">
    <DataMappingRule description="Mapping for Customer Address to OfferSheet Address" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-15" id="osAddressCopy" position="offersheet_dataMappingRule.xlsx,osAddressCopy,1" srcEntityName="OsAddress" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osAddressCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osAddressCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osAddress</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osContactCopy" position="offersheet_dataMappingRule.xlsx,osContactCopy">
    <DataMappingRule description="Mapping for Customer Contact to OfferSheet Contact" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-03-15" id="osContactCopy" position="offersheet_dataMappingRule.xlsx,osContactCopy,1" srcEntityName="OsContact" srcEntityVersion="1" status="1" updatedDate="2012-03-20">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osContactCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osContactCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osContact</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectVqItem" position="offersheet_dataMappingRule.xlsx,osSelectVqItem">
    <DataMappingRule description="Mapping from vq to Offersheet Item" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-04-22" id="osSelectVqItem" position="offersheet_dataMappingRule.xlsx,osSelectVqItem,1" srcEntityName="Vq" srcEntityVersion="1" status="1" updatedDate="2012-04-22">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId>ft20Qty</srcFieldId>
          <dstFieldId>ft20</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40Qty</srcFieldId>
          <dstFieldId>ft40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40HcQty</srcFieldId>
          <dstFieldId>ftHc40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId>ft45Qty</srcFieldId>
          <dstFieldId>ft45</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>innerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>outerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>unitCost</srcFieldId>
          <dstFieldId>unitCost</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentMethod</srcFieldId>
          <dstFieldId>osItem.paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,25">
          <mappingType>Field</mappingType>
          <srcFieldId>fileId</srcFieldId>
          <dstFieldId>itemImage</dstFieldId>
          <dstFieldType>String</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>osItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>item.productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>factory</srcFieldId>
          <dstFieldId>osItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>vendor</srcFieldId>
          <dstFieldId>osItem.vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>uom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,36">
          <mappingType>Section</mappingType>
          <srcFieldId>incoterm</srcFieldId>
          <dstFieldId>osItem.incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,37">
          <mappingType>Section</mappingType>
          <srcFieldId>shipmentMethod</srcFieldId>
          <dstFieldId>osItem.shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,38">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfOrigin</srcFieldId>
          <dstFieldId>osItem.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,39">
          <mappingType>Section</mappingType>
          <srcFieldId>countryOfShipment</srcFieldId>
          <dstFieldId>osItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,40">
          <mappingType>Section</mappingType>
          <srcFieldId>portOfLoading</srcFieldId>
          <dstFieldId>osItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>paymentTerm</srcFieldId>
          <dstFieldId>osItem.paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>containerSize</srcFieldId>
          <dstFieldId>osItem.containerSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,43">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false &amp;&amp; entity.dimension.code = 'SIZE'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,44">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,45">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,46">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,47">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,48">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,49">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isPrimary=true &amp;&amp; entity.isInactive=false</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,50">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,51">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,52">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,53">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,54">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,55">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem.vqId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,59">
          <type>PreProcessor</type>
          <templateName>Offersheet Item select config</templateName>
          <templateFile>os_select_vq_item_cfg.txt</templateFile>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetSelectVQEntityProcessor</implementationClass>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectVqItem,60">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.InnerAndOuterCartonToOsItemsProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="osChargeOnDocCopy" position="offersheet_dataMappingRule.xlsx,osChargeOnDocCopy">
    <DataMappingRule description="Mapping for Copy Offersheet ChargeonDoc" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2011-01-01" id="osChargeOnDocCopy" position="offersheet_dataMappingRule.xlsx,osChargeOnDocCopy,1" srcEntityName="OsChargeOnDoc" srcEntityVersion="1" status="1" updatedDate="2099-12-31">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osChargeOnDocCopy,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osChargeOnDocCopy,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osChargeOnDoc</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="basketGenOfferSheet" position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet">
    <DataMappingRule description="Mapping from basket item to Offersheet" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="basketGenOfferSheet" position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,1" srcEntityName="Catalog" srcEntityVersion="1" status="1" updatedDate="2013-03-05">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>osDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,10">
          <mappingType>Section</mappingType>
          <srcFieldId>program</srcFieldId>
          <dstFieldId>program</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,11">
          <mappingType>Section</mappingType>
          <srcFieldId>customer</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,12">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,13">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,14">
          <mappingType>Section</mappingType>
          <srcFieldId>customer.currency</srcFieldId>
          <dstFieldId>custCurrency</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,15">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.itemId.season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,16">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogYear</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,17">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.vqType</srcFieldId>
          <dstFieldId>quoteType</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,18">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,19">
          <mappingType>Field</mappingType>
          <srcFieldId>itemDesc</srcFieldId>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,20">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,21">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>plannedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,22">
          <mappingType>Field</mappingType>
          <srcFieldId>cost</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,23">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorComments</srcFieldId>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,24">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemNo</srcFieldId>
          <dstFieldId>vendorItemRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,25">
          <mappingType>Field</mappingType>
          <srcFieldId>htsNo</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,26">
          <mappingType>Field</mappingType>
          <srcFieldId>productLeadTime</srcFieldId>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,27">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingInnerWidth</srcFieldId>
          <dstFieldId>innerWidth</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,28">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingInnerHeight</srcFieldId>
          <dstFieldId>innerHeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,29">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingInnerLength</srcFieldId>
          <dstFieldId>innerLength</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,30">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingInnerGrossWeight</srcFieldId>
          <dstFieldId>innerGrossWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,31">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingInnerNetWeight</srcFieldId>
          <dstFieldId>innerNetWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,32">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingOuterHeight</srcFieldId>
          <dstFieldId>outerHeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,33">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingOuterWidth</srcFieldId>
          <dstFieldId>outerWidth</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,34">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingOuterLength</srcFieldId>
          <dstFieldId>outerLength</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,35">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingOuterGrossWeight</srcFieldId>
          <dstFieldId>outerGrossWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,36">
          <mappingType>Field</mappingType>
          <srcFieldId>mappingOuterNetWeight</srcFieldId>
          <dstFieldId>outerNetWeight</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,37">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerOuter</srcFieldId>
          <dstFieldId>outerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,38">
          <mappingType>Field</mappingType>
          <srcFieldId>unitsPerInner</srcFieldId>
          <dstFieldId>innerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,39">
          <mappingType>Field</mappingType>
          <srcFieldId>ft20Qty</srcFieldId>
          <dstFieldId>ft20</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,40">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40Qty</srcFieldId>
          <dstFieldId>ft40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,41">
          <mappingType>Field</mappingType>
          <srcFieldId>ft40HcQty</srcFieldId>
          <dstFieldId>ftHc40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,42">
          <mappingType>Field</mappingType>
          <srcFieldId>ft45Qty</srcFieldId>
          <dstFieldId>ft45</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,43">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.containerSize</srcFieldId>
          <dstFieldId>osItem.containerSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,44">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.item</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,45">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.item.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,46">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.item.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,47">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.item.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,48">
          <mappingType>Section</mappingType>
          <srcFieldId>vq.item.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,49">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.vqId</srcFieldId>
          <dstFieldId>osItem.vqId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,50">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.itemId.hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,51">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.itemId.productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,52">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.itemId.specId</srcFieldId>
          <dstFieldId>osItem.specId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,53">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.uom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,54">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.dimensionUOM</srcFieldId>
          <dstFieldId>osItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,55">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.weightUOM</srcFieldId>
          <dstFieldId>osItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,56">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.factory</srcFieldId>
          <dstFieldId>osItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,57">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.vendor</srcFieldId>
          <dstFieldId>osItem.vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,58">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.market</srcFieldId>
          <dstFieldId>osItem.market</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,59">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.channel</srcFieldId>
          <dstFieldId>osItem.channel</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,60">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.paymentMethod</srcFieldId>
          <dstFieldId>osItem.paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,61">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.paymentTerm</srcFieldId>
          <dstFieldId>osItem.paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,62">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.incoterm</srcFieldId>
          <dstFieldId>osItem.incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,63">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.shipmentMethod</srcFieldId>
          <dstFieldId>osItem.shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,64">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.countryOfOrigin</srcFieldId>
          <dstFieldId>osItem.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,65">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.countryOfShipment</srcFieldId>
          <dstFieldId>osItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,66">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.portOfLoading</srcFieldId>
          <dstFieldId>osItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,67">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.item.itemSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false &amp;&amp; entity.dimension.code = 'SIZE'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,68">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,69">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>osItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,70">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,71">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,72">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,73">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,74">
          <mappingType>Section</mappingType>
          <srcFieldId>catalogItems.item.itemColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false&amp;&amp;entity.isPrimary=true</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,75">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,76">
          <mappingType>Field</mappingType>
          <srcFieldId>itemId</srcFieldId>
          <dstFieldId>osItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,77">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,78">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,79">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,basketGenOfferSheet,80">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor"/>
    </DataMappingRule>
  </sheet>
  <sheet id="offerSheetGenVpo" position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo">
    <DataMappingRule description="Mapping from Offersheet to Vpo" domain="/" dstEntityName="Vpo" dstEntityVersion="1" effectiveDate="2012-06-03" id="offerSheetGenVpo" position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,1" srcEntityName="Offersheet" srcEntityVersion="1" status="1" updatedDate="2012-06-03">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,9">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vpoDate</dstFieldId>
          <dstFieldType>date</dstFieldType>
          <mappedValue>SYS_DATE</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,10">
          <mappingType>Field</mappingType>
          <srcFieldId>projectReference</srcFieldId>
          <dstFieldId>projRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,11">
          <mappingType>Field</mappingType>
          <srcFieldId>termsConditions</srcFieldId>
          <dstFieldId>otherTerms</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>paymentInstructions</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.paymentInstruction</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>custContact</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.custId.contactName</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,14">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentMethod</srcFieldId>
          <dstFieldId>paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,15">
          <mappingType>Section</mappingType>
          <srcFieldId>year</srcFieldId>
          <dstFieldId>year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,16">
          <mappingType>Section</mappingType>
          <srcFieldId>season</srcFieldId>
          <dstFieldId>season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,17">
          <mappingType>Section</mappingType>
          <srcFieldId>hierarchy</srcFieldId>
          <dstFieldId>hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,18">
          <mappingType>Section</mappingType>
          <srcFieldId>productCategory</srcFieldId>
          <dstFieldId>productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,19">
          <mappingType>Section</mappingType>
          <srcFieldId>custId</srcFieldId>
          <dstFieldId>custId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,20">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.paymentTerm</srcFieldId>
          <dstFieldId>paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,21">
          <mappingType>Section</mappingType>
          <srcFieldId>custId.incoterm</srcFieldId>
          <dstFieldId>incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,22">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem</srcFieldId>
          <dstFieldId>vpoItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,23">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemId.itemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,24">
          <mappingType>Field</mappingType>
          <srcFieldId>lotNo</srcFieldId>
          <dstFieldId>lotNo</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,25">
          <mappingType>Field</mappingType>
          <srcFieldId>vendorItemRef</srcFieldId>
          <dstFieldId>vendorItemNo</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,26">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>itemDesc</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemId.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,27">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>isSet</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.itemId.isSet</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,28">
          <mappingType>Field</mappingType>
          <srcFieldId>htsCode</srcFieldId>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,29">
          <mappingType>Field</mappingType>
          <srcFieldId>plannedQty</srcFieldId>
          <dstFieldId>planedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,30">
          <mappingType>Field</mappingType>
          <srcFieldId>moq</srcFieldId>
          <dstFieldId>moq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,31">
          <mappingType>Field</mappingType>
          <srcFieldId>innerQty</srcFieldId>
          <dstFieldId>qtyPerInnerCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,32">
          <mappingType>Field</mappingType>
          <srcFieldId>outerQty</srcFieldId>
          <dstFieldId>qtyPerExportCarton</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,33">
          <mappingType>Field</mappingType>
          <srcFieldId>outerLength</srcFieldId>
          <dstFieldId>l</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,34">
          <mappingType>Field</mappingType>
          <srcFieldId>outerWidth</srcFieldId>
          <dstFieldId>w</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,35">
          <mappingType>Field</mappingType>
          <srcFieldId>outerHeight</srcFieldId>
          <dstFieldId>h</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,36">
          <mappingType>Field</mappingType>
          <srcFieldId>outerGrossWeight</srcFieldId>
          <dstFieldId>gw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,37">
          <mappingType>Field</mappingType>
          <srcFieldId>outerNetWeight</srcFieldId>
          <dstFieldId>nw</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,38">
          <mappingType>Field</mappingType>
          <srcFieldId>osId</srcFieldId>
          <dstFieldId>offerSheetId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,39">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.year</srcFieldId>
          <dstFieldId>vpoItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,40">
          <mappingType>Field</mappingType>
          <srcFieldId>osItem.season</srcFieldId>
          <dstFieldId>vpoItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,41">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.productCategory</srcFieldId>
          <dstFieldId>vpoItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,42">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.classification</srcFieldId>
          <dstFieldId>vpoItem.classification</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,43">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId</srcFieldId>
          <dstFieldId>vpoItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,44">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.itemId.fileId</srcFieldId>
          <dstFieldId>vpoItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,45">
          <mappingType>Section</mappingType>
          <srcFieldId>osItem.osItemColor</srcFieldId>
          <dstFieldId>vpoItem.vpoItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,46">
          <mappingType>Field</mappingType>
          <srcFieldId>osItemId</srcFieldId>
          <dstFieldId>vpoItemId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,47">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,48">
          <mappingType>Field</mappingType>
          <srcFieldId>itemColorId</srcFieldId>
          <dstFieldId>itemColorDoc</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue>ItemColor</mappedValue>
          <mappedValueType>IdToEntity</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,49">
          <mappingType>Field</mappingType>
          <srcFieldId>seq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,50">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,51">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,52">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,53">
          <mappingType>Field</mappingType>
          <srcFieldId>isSelected</srcFieldId>
          <dstFieldId>isSelected</dstFieldId>
          <dstFieldType>boolean</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="offersheet_dataMappingRule.xlsx,offerSheetGenVpo,57">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetGenVpoPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
  <sheet id="osSelectProjectItem" position="offersheet_dataMappingRule.xlsx,osSelectProjectItem">
    <DataMappingRule description="Mapping for Project Item to Vpo" domain="/" dstEntityName="Offersheet" dstEntityVersion="1" effectiveDate="2012-02-20" id="osSelectProjectItem" position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,1" srcEntityName="ProjectItem" srcEntityVersion="1" status="1" updatedDate="2013-08-08">
      <elements id="mappingRule">
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,8">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>$Root</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Aggregation</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,9">
          <mappingType>Section</mappingType>
          <srcFieldId>$Root</srcFieldId>
          <dstFieldId>osItem</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,10">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>description</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.item.itemDesc</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,11">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorComments</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.vendorComments</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,12">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>vendorItemRef</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.vendorItemNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,13">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>htsCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue>entity.quotation.htsNo</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,14">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>productLeadTime</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.productLeadTime</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,15">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>innerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerInner</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,16">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>outerQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.unitsPerOuter</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,17">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>osItem.ft20</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.ft20Qty</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,18">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>osItem.ft40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.ft40Qty</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,19">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>osItem.ftHc40</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.ft40HcQty</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,20">
          <mappingType>Field</mappingType>
          <srcFieldId/>
          <dstFieldId>osItem.ft45</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue>entity.quotation.ft45Qty</mappedValue>
          <mappedValueType>Formula</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,21">
          <mappingType>Field</mappingType>
          <srcFieldId>landedCost</srcFieldId>
          <dstFieldId>price</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,22">
          <mappingType>Field</mappingType>
          <srcFieldId>qty</srcFieldId>
          <dstFieldId>plannedQty</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,23">
          <mappingType>Section</mappingType>
          <srcFieldId>item</srcFieldId>
          <dstFieldId>osItem.itemId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,24">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation</srcFieldId>
          <dstFieldId>osItem.vqId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,25">
          <mappingType>Section</mappingType>
          <srcFieldId>item.hierarchy</srcFieldId>
          <dstFieldId>osItem.hierarchy</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isHclSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,26">
          <mappingType>Section</mappingType>
          <srcFieldId>item.productCategory</srcFieldId>
          <dstFieldId>osItem.productCategory</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition>isClassificationSecurityMode()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,27">
          <mappingType>Section</mappingType>
          <srcFieldId>item.fileId</srcFieldId>
          <dstFieldId>osItem.itemFileId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,28">
          <mappingType>Section</mappingType>
          <srcFieldId>item.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,29">
          <mappingType>Section</mappingType>
          <srcFieldId>item.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isSingleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,30">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.season</srcFieldId>
          <dstFieldId>osItem.season</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,31">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultSourcingRecord.year</srcFieldId>
          <dstFieldId>osItem.year</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>isMultipleSourcingRecord()</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,32">
          <mappingType>Section</mappingType>
          <srcFieldId>item.defaultUom</srcFieldId>
          <dstFieldId>osItem.uom</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,33">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.paymentMethod</srcFieldId>
          <dstFieldId>osItem.paymentMethod</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,34">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.paymentTerm</srcFieldId>
          <dstFieldId>osItem.paymentTerm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,35">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.incoterm</srcFieldId>
          <dstFieldId>osItem.incoterm</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,36">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.shipmentMethod</srcFieldId>
          <dstFieldId>osItem.shipMode</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,37">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.countryOfOrigin</srcFieldId>
          <dstFieldId>osItem.countryOfOrigin</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,38">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.countryOfShipment</srcFieldId>
          <dstFieldId>osItem.countryOfShipment</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,39">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.portOfLoading</srcFieldId>
          <dstFieldId>osItem.portOfLoading</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,40">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.weightUOM</srcFieldId>
          <dstFieldId>osItem.weightUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity.code = 'INNER'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,41">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.dimensionUOM</srcFieldId>
          <dstFieldId>osItem.dimensionUOM</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition>entity.code = 'INNER'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,42">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.containerSize</srcFieldId>
          <dstFieldId>osItem.containerSize</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Reference</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,43">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.vendor</srcFieldId>
          <dstFieldId>osItem.vendorId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,44">
          <mappingType>Section</mappingType>
          <srcFieldId>quotation.factory</srcFieldId>
          <dstFieldId>osItem.factId</dstFieldId>
          <dstFieldType>entity</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Copy</mappingStrategy>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,45">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemSize</srcFieldId>
          <dstFieldId>osItem.osItemSize</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isInactive=false &amp;&amp; entity.dimension.code = 'SIZE'</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,46">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemSizeId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,47">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,48">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeCode</srcFieldId>
          <dstFieldId>sizeCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,49">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeName</srcFieldId>
          <dstFieldId>sizeName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,50">
          <mappingType>Field</mappingType>
          <srcFieldId>sizeDisplayName</srcFieldId>
          <dstFieldId>displayName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,51">
          <mappingType>Section</mappingType>
          <srcFieldId>item.itemColor</srcFieldId>
          <dstFieldId>osItem.osItemColor</dstFieldId>
          <dstFieldType>collection</dstFieldType>
          <mappedValue/>
          <mappedValueType/>
          <mappingStrategy>Multiple</mappingStrategy>
          <aggregationType/>
          <condition>entity.isPrimary=true &amp;&amp; entity.isInactive=false</condition>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,52">
          <mappingType>Field</mappingType>
          <srcFieldId>id</srcFieldId>
          <dstFieldId>itemColorId</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,53">
          <mappingType>Field</mappingType>
          <srcFieldId>colorSeq</srcFieldId>
          <dstFieldId>seq</dstFieldId>
          <dstFieldType>number</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,54">
          <mappingType>Field</mappingType>
          <srcFieldId>colorCode</srcFieldId>
          <dstFieldId>colorCode</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,55">
          <mappingType>Field</mappingType>
          <srcFieldId>colorName</srcFieldId>
          <dstFieldId>colorName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,56">
          <mappingType>Field</mappingType>
          <srcFieldId>shortName</srcFieldId>
          <dstFieldId>shortName</dstFieldId>
          <dstFieldType>string</dstFieldType>
          <mappedValue/>
          <mappedValueType>Field</mappedValueType>
          <mappingStrategy/>
          <aggregationType/>
          <condition/>
        </element>
      </elements>
      <elements id="customizedProcessor">
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,60">
          <type>PreProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetSelectProjectItemPreProcessor</implementationClass>
        </element>
        <element position="offersheet_dataMappingRule.xlsx,osSelectProjectItem,61">
          <type>PostProcessor</type>
          <templateName/>
          <templateFile/>
          <implementationClass>com.core.cbx.inheritance.customizedprocessor.OfferSheetSelectProjectItemPostProcessor</implementationClass>
        </element>
      </elements>
    </DataMappingRule>
  </sheet>
</dataMappingRule>
