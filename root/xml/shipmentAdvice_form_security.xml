<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="shipmentAdvice" position="shipmentAdvice_form_security.xlsx">
  <sheet id="_system" position="shipmentAdvice_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="shipmentAdvice_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="shipmentAdvice_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,_system,10">
          <updatedOn>2012/Dec/6</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="shipmentAdvice_form_security.xlsx,generalInfo">
    <GeneralInfo position="shipmentAdvice_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="shipmentAdvice_form_security.xlsx,condition">
    <ConditionList position="shipmentAdvice_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,condition,4">
          <conditionId>statusDraft</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,5">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,6">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,7">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,8">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,9">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,10">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,11">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,12">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,13">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,14">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,15">
          <conditionId>isHeaderHclReadOnly</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,16">
          <conditionId>statusCustomsFilingAccepted</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,17">
          <conditionId>statusShipmentOnBoard</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,18">
          <conditionId>statusDocumentsUploaded</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,19">
          <conditionId>statusShipmentClosed</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,20">
          <conditionId>statusAllInvoiceIssued</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,21">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,22">
          <conditionId>isExternalDomain</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,23">
          <conditionId>isNotApprovalInPending</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,24">
          <conditionId>isItemOrder</conditionId>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,condition,25">
          <conditionId>isMaterialOrder</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="shipmentAdvice_form_security.xlsx,default">
    <ActionConditionMatrix position="shipmentAdvice_form_security.xlsx,default,1">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,default,4">
          <actionId>newDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,5">
          <actionId>newMaterialDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,6">
          <actionId>shipmentAdviceNewCustInv</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,7">
          <actionId>shipmentAdviceGenerateVendorInv</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,8">
          <actionId>shipmentAdviceCreateClaim</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,9">
          <actionId>editDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,10">
          <actionId>amendDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>disallowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,11">
          <actionId>saveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,12">
          <actionId>baseSaveDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,13">
          <actionId>saveAndConfirm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,14">
          <actionId>discardDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>disallowed</editingStatusConfirmed>
          <internalStatusUnlocked>disallowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>disallowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,15">
          <actionId>sendToVendor</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,16">
          <actionId>sendToBuyer</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,17">
          <actionId>customExport01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,18">
          <actionId>customExport02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,19">
          <actionId>customExport03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,20">
          <actionId>customExport04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,21">
          <actionId>customExport05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,22">
          <actionId>customExport06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,23">
          <actionId>customExport07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,24">
          <actionId>customExport08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,25">
          <actionId>customExport09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,26">
          <actionId>customExport10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,27">
          <actionId>draftStatus</actionId>
          <statusDraft>disallowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,28">
          <actionId>customsFilingAcceptedStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>disallowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,29">
          <actionId>shipmentOnBoardStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>disallowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,30">
          <actionId>documentsUploadedStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>disallowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,31">
          <actionId>shipmentClosedStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>disallowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,32">
          <actionId>allInvoiceIssuedStatus</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>disallowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,33">
          <actionId>copyDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,34">
          <actionId>activateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>disallowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,35">
          <actionId>deactivateDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,36">
          <actionId>cancelDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>disallowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,37">
          <actionId>loadDoc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,38">
          <actionId>initializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>disallowed</docStatusInactive>
          <docStatusCanceled>disallowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>disallowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>disallowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,39">
          <actionId>lookupShipmentBookingOrAdviceQty</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,40">
          <actionId>shipmentAdviceCustom01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,41">
          <actionId>shipmentAdviceCustom02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,42">
          <actionId>shipmentAdviceCustom03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,43">
          <actionId>shipmentAdviceCustom04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,44">
          <actionId>shipmentAdviceCustom05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,45">
          <actionId>shipmentAdviceCustom06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,46">
          <actionId>shipmentAdviceCustom07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,47">
          <actionId>shipmentAdviceCustom08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,48">
          <actionId>shipmentAdviceCustom09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,49">
          <actionId>shipmentAdviceCustom10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,50">
          <actionId>customPrint01</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,51">
          <actionId>customPrint02</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,52">
          <actionId>customPrint03</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,53">
          <actionId>customPrint04</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,54">
          <actionId>customPrint05</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,55">
          <actionId>customPrint06</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,56">
          <actionId>customPrint07</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,57">
          <actionId>customPrint08</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,58">
          <actionId>customPrint09</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,59">
          <actionId>customPrint10</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,60">
          <actionId>markAsCustomStatus01Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,61">
          <actionId>markAsCustomStatus02Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,62">
          <actionId>markAsCustomStatus03Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,63">
          <actionId>markAsCustomStatus04Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,64">
          <actionId>markAsCustomStatus05Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,65">
          <actionId>markAsCustomStatus06Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,66">
          <actionId>markAsCustomStatus07Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,67">
          <actionId>markAsCustomStatus08Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,68">
          <actionId>markAsCustomStatus09Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,69">
          <actionId>markAsCustomStatus10Doc</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>disallowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>disallowed</internalStatusNew>
          <isNotLatest>disallowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,70">
          <actionId>reinitializeCpm</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,71">
          <actionId>refreshCpmTemplate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,72">
          <actionId>refreshCpmPlanDate</actionId>
          <statusDraft>allowed</statusDraft>
          <statusCustomsFilingAccepted>allowed</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>allowed</statusShipmentOnBoard>
          <statusDocumentsUploaded>allowed</statusDocumentsUploaded>
          <statusShipmentClosed>allowed</statusShipmentClosed>
          <statusAllInvoiceIssued>allowed</statusAllInvoiceIssued>
          <docStatusActive>allowed</docStatusActive>
          <docStatusInactive>allowed</docStatusInactive>
          <docStatusCanceled>allowed</docStatusCanceled>
          <editingStatusDraft>allowed</editingStatusDraft>
          <editingStatusPending>allowed</editingStatusPending>
          <editingStatusConfirmed>allowed</editingStatusConfirmed>
          <internalStatusUnlocked>allowed</internalStatusUnlocked>
          <internalStatusLockedByOthers>allowed</internalStatusLockedByOthers>
          <internalStatusLocked>allowed</internalStatusLocked>
          <internalStatusNew>allowed</internalStatusNew>
          <isNotLatest>allowed</isNotLatest>
          <isExternalDomain>allowed</isExternalDomain>
          <isNotApprovalInPending>allowed</isNotApprovalInPending>
        </element>
      </elements>
    </ActionConditionMatrix>
    <UIConditionMatrix position="shipmentAdvice_form_security.xlsx,default,75">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,default,78">
          <componentId>ui</componentId>
          <statusDraft>editable</statusDraft>
          <statusCustomsFilingAccepted>editable</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>editable</statusShipmentOnBoard>
          <statusDocumentsUploaded>editable</statusDocumentsUploaded>
          <statusShipmentClosed>editable</statusShipmentClosed>
          <statusAllInvoiceIssued>editable</statusAllInvoiceIssued>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>editable</internalStatusLocked>
          <internalStatusNew>editable</internalStatusNew>
          <isHeaderHclReadOnly>editable</isHeaderHclReadOnly>
          <isItemOrder>editable</isItemOrder>
          <isMaterialOrder>editable</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,79">
          <componentId>ui.tabHeader.hierarchySection</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>readonly</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,80">
          <componentId>ui.shipmentAdviceLinkBar.duplicateWindow</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,81">
          <componentId>ui.shipmentAdviceLinkBar.approval</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,82">
          <componentId>ui.shipmentAdviceLinkBar.openForum</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,83">
          <componentId>ui.shipmentAdviceLinkBar.addToFavorites</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,84">
          <componentId>ui.shipmentAdviceLinkBar.followDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,85">
          <componentId>ui.shipmentAdviceLinkBar.unfollowDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,86">
          <componentId>ui.menubar.printDoc</componentId>
          <statusDraft>hidden</statusDraft>
          <statusCustomsFilingAccepted>hidden</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>hidden</statusShipmentOnBoard>
          <statusDocumentsUploaded>hidden</statusDocumentsUploaded>
          <statusShipmentClosed>hidden</statusShipmentClosed>
          <statusAllInvoiceIssued>hidden</statusAllInvoiceIssued>
          <docStatusActive>hidden</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,87">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.isSet</componentId>
          <statusDraft>hidden</statusDraft>
          <statusCustomsFilingAccepted>hidden</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>hidden</statusShipmentOnBoard>
          <statusDocumentsUploaded>hidden</statusDocumentsUploaded>
          <statusShipmentClosed>hidden</statusShipmentClosed>
          <statusAllInvoiceIssued>hidden</statusAllInvoiceIssued>
          <docStatusActive>hidden</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>hidden</internalStatusUnlocked>
          <internalStatusLockedByOthers>hidden</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>hidden</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,88">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.productCategory</componentId>
          <statusDraft>readonly</statusDraft>
          <statusCustomsFilingAccepted>readonly</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>readonly</statusShipmentOnBoard>
          <statusDocumentsUploaded>readonly</statusDocumentsUploaded>
          <statusShipmentClosed>readonly</statusShipmentClosed>
          <statusAllInvoiceIssued>readonly</statusAllInvoiceIssued>
          <docStatusActive>readonly</docStatusActive>
          <docStatusInactive>readonly</docStatusInactive>
          <docStatusCanceled>readonly</docStatusCanceled>
          <editingStatusDraft>readonly</editingStatusDraft>
          <editingStatusPending>readonly</editingStatusPending>
          <editingStatusConfirmed>readonly</editingStatusConfirmed>
          <internalStatusUnlocked>readonly</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>readonly</internalStatusLocked>
          <internalStatusNew>readonly</internalStatusNew>
          <isHeaderHclReadOnly>readonly</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,89">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.selectShipmentItemsFromVendorPO</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>editable</isItemOrder>
          <isMaterialOrder>hidden</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,90">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.selectShipmentItemsFromMaterialVendorPO</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>hidden</isItemOrder>
          <isMaterialOrder>editable</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,91">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.selectShipmentItemsFromShipmentBooking</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>editable</isItemOrder>
          <isMaterialOrder>hidden</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,92">
          <componentId>ui.tabShipmentItems.shipmentAdviceShipmentItems.selectShipmentItemsFromMaterialShipmentBooking</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>inherit</docStatusActive>
          <docStatusInactive>inherit</docStatusInactive>
          <docStatusCanceled>inherit</docStatusCanceled>
          <editingStatusDraft>inherit</editingStatusDraft>
          <editingStatusPending>inherit</editingStatusPending>
          <editingStatusConfirmed>inherit</editingStatusConfirmed>
          <internalStatusUnlocked>inherit</internalStatusUnlocked>
          <internalStatusLockedByOthers>inherit</internalStatusLockedByOthers>
          <internalStatusLocked>inherit</internalStatusLocked>
          <internalStatusNew>inherit</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>hidden</isItemOrder>
          <isMaterialOrder>editable</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,93">
          <componentId>ui.menuBar.editDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>editable</editingStatusDraft>
          <editingStatusPending>editable</editingStatusPending>
          <editingStatusConfirmed>hidden</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,default,94">
          <componentId>ui.menuBar.amendDoc</componentId>
          <statusDraft>inherit</statusDraft>
          <statusCustomsFilingAccepted>inherit</statusCustomsFilingAccepted>
          <statusShipmentOnBoard>inherit</statusShipmentOnBoard>
          <statusDocumentsUploaded>inherit</statusDocumentsUploaded>
          <statusShipmentClosed>inherit</statusShipmentClosed>
          <statusAllInvoiceIssued>inherit</statusAllInvoiceIssued>
          <docStatusActive>editable</docStatusActive>
          <docStatusInactive>hidden</docStatusInactive>
          <docStatusCanceled>hidden</docStatusCanceled>
          <editingStatusDraft>hidden</editingStatusDraft>
          <editingStatusPending>hidden</editingStatusPending>
          <editingStatusConfirmed>editable</editingStatusConfirmed>
          <internalStatusUnlocked>editable</internalStatusUnlocked>
          <internalStatusLockedByOthers>readonly</internalStatusLockedByOthers>
          <internalStatusLocked>hidden</internalStatusLocked>
          <internalStatusNew>hidden</internalStatusNew>
          <isHeaderHclReadOnly>inherit</isHeaderHclReadOnly>
          <isItemOrder>inherit</isItemOrder>
          <isMaterialOrder>inherit</isMaterialOrder>
        </element>
      </elements>
    </UIConditionMatrix>
  </sheet>
  <sheet id="acl" position="shipmentAdvice_form_security.xlsx,acl">
    <ActionRule position="shipmentAdvice_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,5">
          <actionId>newMaterialDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,6">
          <actionId>shipmentAdviceNewCustInv</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,7">
          <actionId>shipmentAdviceGenerateVendorInv</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,8">
          <actionId>shipmentAdviceCreateClaim</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,9">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,10">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,11">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,12">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,13">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,14">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,15">
          <actionId>sendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,16">
          <actionId>sendToBuyer</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,17">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,18">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,19">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,20">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,21">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,22">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,23">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,24">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,25">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,26">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,27">
          <actionId>draftStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,28">
          <actionId>customsFilingAcceptedStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,29">
          <actionId>shipmentOnBoardStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,30">
          <actionId>documentsUploadedStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,31">
          <actionId>shipmentClosedStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,32">
          <actionId>allInvoiceIssuedStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,33">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,34">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,35">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,36">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,37">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>has</shipmentAdvice.ReadOnly>
          <custInv.Author>has</custInv.Author>
          <vendorInvoice.Author>has</vendorInvoice.Author>
          <claim.Author>has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,38">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,39">
          <actionId>lookupShipmentBookingOrAdviceQty</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>has</shipmentAdvice.ReadOnly>
          <custInv.Author>has</custInv.Author>
          <vendorInvoice.Author>has</vendorInvoice.Author>
          <claim.Author>has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,40">
          <actionId>shipmentAdviceCustom01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,41">
          <actionId>shipmentAdviceCustom02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,42">
          <actionId>shipmentAdviceCustom03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,43">
          <actionId>shipmentAdviceCustom04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,44">
          <actionId>shipmentAdviceCustom05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,45">
          <actionId>shipmentAdviceCustom06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,46">
          <actionId>shipmentAdviceCustom07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,47">
          <actionId>shipmentAdviceCustom08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,48">
          <actionId>shipmentAdviceCustom09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,49">
          <actionId>shipmentAdviceCustom10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,50">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,51">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,52">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,53">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,54">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,55">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,56">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,57">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,58">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,59">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,60">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,61">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,62">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,63">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,64">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,65">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,66">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,67">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,68">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,69">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,70">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,71">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,72">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <READONLY_ROLE>not-has</READONLY_ROLE>
          <shipmentAdvice.Author>not-has</shipmentAdvice.Author>
          <shipmentAdvice.Editor>not-has</shipmentAdvice.Editor>
          <shipmentAdvice.ReadOnly>not-has</shipmentAdvice.ReadOnly>
          <custInv.Author>not-has</custInv.Author>
          <vendorInvoice.Author>not-has</vendorInvoice.Author>
          <claim.Author>not-has</claim.Author>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="shipmentAdvice_form_security.xlsx,acl,75">
      <elements id="default">
        <element position="shipmentAdvice_form_security.xlsx,acl,78">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,79">
          <componentId>ui.tabContainers</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,80">
          <componentId>ui.tabShipmentItems</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,81">
          <componentId>ui.tabAddressesAndContacts</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,82">
          <componentId>ui.tabAttachments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,83">
          <componentId>ui.tabHeader.sectionGeneral.isFromMultipleVendors</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,84">
          <componentId>ui.tabHeader.sectionSummary.currency</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,85">
          <componentId>ui.tabHeader.sectionSummary.totalCostOfContainers</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,86">
          <componentId>ui.tabContainers.shipmentAdviceSummary.currency</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,87">
          <componentId>ui.tabContainers.shipmentAdviceSummary.unitCost</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,88">
          <componentId>ui.tabContainers.shipmentAdviceSummary.additionalCost</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,89">
          <componentId>ui.tabContainers.shipmentAdviceSummary.calculatedCost</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
        <element position="shipmentAdvice_form_security.xlsx,acl,90">
          <componentId>ui.addRemoveColumn</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="shipmentAdvice_form_security.xlsx,acl,93">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
