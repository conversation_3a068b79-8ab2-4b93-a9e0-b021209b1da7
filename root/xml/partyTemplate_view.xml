<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view module="partyTemplate" position="partyTemplate_view.xlsx">
  <sheet id="partyTemplateView" position="partyTemplate_view.xlsx,partyTemplateView">
    <ViewDefinition advancedSearchId="" description="Party Template View" id="partyTemplateView" label="Responsible Parties" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateView,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateView,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>2</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>PartyTemplate</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,businessRefNo:businessReference:string,isLatest:isLatest:boolean</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateView,18">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,19">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,20">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,21">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,22">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,23">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,24">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,25">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,26">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,27">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,28">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,29">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateView,34">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>name:name:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,35">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>applyTo:applyTo:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,36">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,37">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,38">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:systemTag:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,39">
          <id>docStatus</id>
          <label>Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,40">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>editingStatus:editingStatus:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,41">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:integer</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,42">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationStatus:integrationStatus:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,43">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationNote:integrationNote:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,44">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updateUserName:updatedBy:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,45">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>updatedOn:updatedOn:timestamp</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,46">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdUserName:createdBy:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,47">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>createdOn:createdOn:timestamp</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,48">
          <id>isCpmInitialized</id>
          <label>CPM Initialized?</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.yesOrNo.all.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.IS_CPM_INITIALIZED</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,49">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docRef:refNo:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateView,50">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>integrationSource:integrationSource:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popPartyTemplateLookupView" position="partyTemplate_view.xlsx,popPartyTemplateLookupView">
    <ViewDefinition advancedSearchId="" description="Popup.Selection.ResponsibleParties" id="popPartyTemplateLookupView" label="Responsible Parties Lookup" moduleId="partyTemplate" position="partyTemplate_view.xlsx,popPartyTemplateLookupView,1" queryId="listPopupPartyTemplates" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>single</value>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,12">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,13">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,21">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,22">
          <id>applyToValue</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,23">
          <id>partyTemplateRuleValue</id>
          <label>Applies to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.PARTY_TEMPLATE_RULE_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,24">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,25">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,26">
          <id>docStatus</id>
          <label>Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,27">
          <id>version</id>
          <label>Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,28">
          <id>editingStatus</id>
          <label>Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,29">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,30">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,31">
          <id>updateUserName</id>
          <label>Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,32">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Datetime</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,33">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,popPartyTemplateLookupView,34">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popApplyModuleView" position="partyTemplate_view.xlsx,popApplyModuleView">
    <ViewDefinition advancedSearchId="" description="Apply to Modules" id="popApplyModuleView" label="Apply to Modules Lookup" moduleId="partyTemplate" position="partyTemplate_view.xlsx,popApplyModuleView,1" queryId="listMainModules" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,popApplyModuleView,8">
          <id>PAGE_SIZE</id>
          <label/>
          <value>20</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,9">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,10">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,11">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>ModuleLabel</value>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,popApplyModuleView,22">
          <id>label</id>
          <label>Module</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>main_cl.LABEL</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>label:label:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,23">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:systemTag:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,24">
          <id>labelId</id>
          <label>Label Id</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>main_cl.labelId</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>labelId:labelId:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popApplyModuleView,25">
          <id>version</id>
          <label>Version</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>main_cl.version</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>version:version:number</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="popDefaultContactUser" position="partyTemplate_view.xlsx,popDefaultContactUser">
    <ViewDefinition advancedSearchId="" description="User Lookup" id="popDefaultContactUser" label="User Lookup" moduleId="user" position="partyTemplate_view.xlsx,popDefaultContactUser,1" queryId="lookupUser" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,8">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,9">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>0</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,10">
          <id>BASE_CRITERION</id>
          <label/>
          <value>(docStatus =|STRING ''active'')</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,11">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,12">
          <id>ES_BASE_CRITERION_MUST</id>
          <label/>
          <value>docStatus=active=string</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,13">
          <id>ES_ENTITY_NAME</id>
          <label/>
          <value>User</value>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,14">
          <id>ES_RETURN_FIELD</id>
          <label/>
          <value>id:id:string,entityName:docType:string,refNo:refNo:string,version:version:integer,entityVersion:entityVersion:integer</value>
        </element>
      </elements>
      <elements id="actions"/>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,22">
          <id>loginId</id>
          <label>User ID</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>u.login_id</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>asc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
          <esMapping>loginId:loginId:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,23">
          <id>alias</id>
          <label>Alias</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>120px</width>
          <visibility>0</visibility>
          <mappedField>cu.alias</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>alias:alias:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,24">
          <id>userName</id>
          <label>User Name</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>u.user_name</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>userName:userName:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,25">
          <id>email</id>
          <label>Email</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>u.email</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>email:email:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,26">
          <id>shortCode</id>
          <label>Short Code</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>u.SHORT_CODE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>shortCode:shortCode:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,27">
          <id>description</id>
          <label>Description</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>u.description</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>description:description:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,28">
          <id>memberOfId</id>
          <label>User Group</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>u.member_of_id_value</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>memberOfId:memberOfIdValue:String</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,29">
          <id>productCategory</id>
          <label>Product Category</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>u.PRODUCT_CATEGORY_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>productCategory:productCategoryValue:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,30">
          <id>substitutes</id>
          <label>Substitute User</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>U.SUBSTITUTES_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>substitutes:substitutesValue:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,31">
          <id>effectiveFrom</id>
          <label>Effective From</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>U.EFFECTIVE_FROM</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>effectiveFrom:effectiveFrom:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,32">
          <id>effectiveTo</id>
          <label>Effective To</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>U.EFFECTIVE_TO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>effectiveTo:effectiveTo:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,33">
          <id>User</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>u</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping/>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,34">
          <id>updatedOn</id>
          <label>Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>95px</width>
          <visibility>1</visibility>
          <mappedField>U.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>updatedOn:updatedOn:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,35">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>systemTag:docTag.systemTag:string</esMapping>
        </element>
        <element position="partyTemplate_view.xlsx,popDefaultContactUser,36">
          <id>docStatus</id>
          <label>Document Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable/>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>u.DOC_STATUS</mappedField>
          <allowSimpleSearch/>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
          <esMapping>docStatus:docStatus:string</esMapping>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus01" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 01 View" id="partyTemplateForCustomStatus01View" label="Responsible Parties - Custom Status 01" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus01,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus02" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 02 View" id="partyTemplateForCustomStatus02View" label="Responsible Parties - Custom Status 02" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus02,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus03" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 03 View" id="partyTemplateForCustomStatus03View" label="Responsible Parties - Custom Status 03" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus03,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus04" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 04 View" id="partyTemplateForCustomStatus04View" label="Responsible Parties - Custom Status 04" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus04,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus05" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 05 View" id="partyTemplateForCustomStatus05View" label="Responsible Parties - Custom Status 05" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus05,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus06" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 06 View" id="partyTemplateForCustomStatus06View" label="Responsible Parties - Custom Status 06" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus06,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus07" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 07 View" id="partyTemplateForCustomStatus07View" label="Responsible Parties - Custom Status 07" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus07,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus08" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 08 View" id="partyTemplateForCustomStatus08View" label="Responsible Parties - Custom Status 08" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus08,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus09" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 09 View" id="partyTemplateForCustomStatus09View" label="Responsible Parties - Custom Status 09" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus09,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
  <sheet id="partyTemplateForCustomStatus10" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10">
    <ViewDefinition advancedSearchId="" description="Party Template Custom Status 10 View" id="partyTemplateForCustomStatus10View" label="Responsible Parties - Custom Status 10" moduleId="partyTemplate" position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,1" queryId="listPartyTemplate" searchCriterion="">
      <elements id="options">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,8">
          <id>SUPPORTED_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,9">
          <id>DEFAULT_MODE</id>
          <label/>
          <value>LIST</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,10">
          <id>SELECTION_MODE</id>
          <label/>
          <value>multiple</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,11">
          <id>FREEZE_COLUMN_INDEX</id>
          <label/>
          <value>1</value>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,12">
          <id>ADMIN_TEMPLATE_VIEW</id>
          <label/>
          <value>TRUE</value>
        </element>
      </elements>
      <elements id="actions">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,16">
          <id>searchNewDoc</id>
          <label>Create</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,17">
          <id>export</id>
          <label>Export</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,18">
          <id>searchViewAllExport</id>
          <label>All Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,19">
          <id>searchViewCurExport</id>
          <label>Current Fields</label>
          <type>button</type>
          <actionParams/>
          <buttonGroup>export</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,20">
          <id>importRawData</id>
          <label>Import</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.ImportRawDataListener</actionParams>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,21">
          <id>actions</id>
          <label>Actions</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,22">
          <id>searchActivateDoc</id>
          <label>Active</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,23">
          <id>searchDeactivateDoc</id>
          <label>Inactive</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,24">
          <id>searchCancelDoc</id>
          <label>Canceled</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>actions</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,25">
          <id>configuration</id>
          <label>Configuration</label>
          <type>buttonGroup</type>
          <actionParams/>
          <buttonGroup/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,26">
          <id>adminModuleDownload</id>
          <label>Download</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;entityName=PartyTemplate</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,27">
          <id>adminModuleUpload</id>
          <label>Upload</label>
          <type>button</type>
          <actionParams>moduleId=partyTemplate&amp;upload=true&amp;listener=com.core.cbx.ui.listener.AdminModuleUploadListener</actionParams>
          <buttonGroup>configuration</buttonGroup>
        </element>
      </elements>
      <elements id="columns">
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,32">
          <id>name</id>
          <label>Name</label>
          <type>RefNo</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,33">
          <id>applyTo</id>
          <label>Apply to</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>1</visibility>
          <mappedField>CPT.APPLY_TO_SELECTION_VALUE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,34">
          <id>PartyTemplate</id>
          <label/>
          <type>CustomFields</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,35">
          <id>PartyTemplate</id>
          <label/>
          <type>ResponsibleParty</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,36">
          <id>systemTag</id>
          <label>System Tag</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>TAG.SYSTEM_TAG</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,37">
          <id>docStatus</id>
          <label>Responsible Parties Doc. Status</label>
          <type>Label</type>
          <format>labelIdPrefix=lbl.docStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.DOC_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,38">
          <id>editingStatus</id>
          <label>Responsible Parties Editing Status</label>
          <type>Label</type>
          <format>labelPrefix=lbl.editingStatus.</format>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.EDITING_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,39">
          <id>version</id>
          <label>Responsible Parties Ver.</label>
          <type>Number</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>right</alignment>
          <sortable>1</sortable>
          <width>XS</width>
          <visibility>0</visibility>
          <mappedField>CPT.VERSION</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,40">
          <id>integrationStatus</id>
          <label>Integration Status</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_STATUS</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,41">
          <id>integrationNote</id>
          <label>Integration Notes</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>L</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_NOTE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,42">
          <id>updateUserName</id>
          <label>Responsible Parties Last Modified by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,43">
          <id>updatedOn</id>
          <label>Responsible Parties Last Modified on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>1</visibility>
          <mappedField>CPT.UPDATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting>desc</defaultSorting>
          <sortingIndex>1</sortingIndex>
          <sortingOption>blankLast</sortingOption>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,44">
          <id>createdUserName</id>
          <label>Created by</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATE_USER_NAME</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,45">
          <id>createdOn</id>
          <label>Created on</label>
          <type>Date</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.CREATED_ON</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,46">
          <id>docRef</id>
          <label>Document Ref.</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>M</width>
          <visibility>0</visibility>
          <mappedField>CPT.REF_NO</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
        <element position="partyTemplate_view.xlsx,partyTemplateForCustomStatus10,47">
          <id>integrationSource</id>
          <label>Document Source</label>
          <type>Text</type>
          <format/>
          <action/>
          <actionParams/>
          <alignment>left</alignment>
          <sortable>1</sortable>
          <width>S</width>
          <visibility>0</visibility>
          <mappedField>CPT.INTEGRATION_SOURCE</mappedField>
          <allowSimpleSearch>1</allowSimpleSearch>
          <defaultSorting/>
          <sortingIndex/>
          <sortingOption/>
        </element>
      </elements>
    </ViewDefinition>
  </sheet>
</view>
