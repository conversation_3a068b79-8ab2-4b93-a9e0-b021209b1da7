<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="fact" position="fact_form_security.xlsx">
  <sheet id="_system" position="fact_form_security.xlsx,_system">
    <ProjectInfo domain="cnt" position="fact_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="fact_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="fact_form_security.xlsx,_system,10">
          <updatedOn>2012/Feb/14</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="fact_form_security.xlsx,generalInfo">
    <GeneralInfo position="fact_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="fact_form_security.xlsx,condition">
    <ConditionList position="fact_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="fact_form_security.xlsx,condition,4">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,5">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,6">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,7">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,8">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,9">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,10">
          <conditionId>docStatusCanceled</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,11">
          <conditionId>editingStatusDraft</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,12">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,13">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,14">
          <conditionId>statusOfficial</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,15">
          <conditionId>isNotExternalUpdatedMode</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,16">
          <conditionId>isExternalUpdatedMode</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,17">
          <conditionId>isNotLatest</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,18">
          <conditionId>statusNew</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,19">
          <conditionId>statusActive</conditionId>
        </element>
        <element position="fact_form_security.xlsx,condition,20">
          <conditionId>statusInactive</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="acl" position="fact_form_security.xlsx,acl">
    <ActionRule position="fact_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="fact_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,5">
          <actionId>factAuditCreatedFromFact</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,6">
          <actionId>factNewShareFile</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,7">
          <actionId>editDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,8">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,9">
          <actionId>saveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,10">
          <actionId>baseSaveDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,11">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,12">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,13">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,14">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,15">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,16">
          <actionId>cancelDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,17">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,18">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,19">
          <actionId>applyUpdates</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,20">
          <actionId>cancelUpdates</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,21">
          <actionId>draftStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,22">
          <actionId>officialStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,23">
          <actionId>newStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,24">
          <actionId>activeStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,25">
          <actionId>inactiveStatus</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,26">
          <actionId>sendToVendor</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,27">
          <actionId>sendToBuyer</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,28">
          <actionId>factCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,29">
          <actionId>factCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,30">
          <actionId>factCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,31">
          <actionId>factCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,32">
          <actionId>factCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,33">
          <actionId>factCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,34">
          <actionId>factCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,35">
          <actionId>factCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,36">
          <actionId>factCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,37">
          <actionId>factCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,38">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,39">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,40">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,41">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,42">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,43">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,44">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,45">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,46">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,47">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,48">
          <actionId>factoryViewCompliance</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,49">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,50">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="fact_form_security.xlsx,acl,51">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <facts>not-has</facts>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="fact_form_security.xlsx,acl,54">
      <elements id="default">
        <element position="fact_form_security.xlsx,acl,57">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,58">
          <componentId>ui.tabHeader.generalSection.deactivationReason</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,59">
          <componentId>ui.tabHeader.hierarchyGrid</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,60">
          <componentId>ui.tabHeader.classificationSection</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,61">
          <componentId>ui.tabDetail</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,62">
          <componentId>ui.tabContact</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,63">
          <componentId>ui.tabVendor</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,64">
          <componentId>ui.tabVendor.vendorFact.selectVendor</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,65">
          <componentId>ui.tabVendor.vendorFact.delVendor</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,66">
          <componentId>ui.tabVendor.factories</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>hidden</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,67">
          <componentId>ui.tabProduction</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,68">
          <componentId>ui.tabQa</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,69">
          <componentId>ui.tabOther</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>hidden</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,70">
          <componentId>ui.tabImage</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,71">
          <componentId>ui.factMenubar.saveAndConfirm</componentId>
          <SuperAdministratorRole>hidden</SuperAdministratorRole>
          <GeneralAdministratorRole>hidden</GeneralAdministratorRole>
          <ClientAdministratorRole>hidden</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>hidden</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,72">
          <componentId>ui.factMenubar.newGroup.newDoc</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,73">
          <componentId>ui.tabHeader.generalSection.syncByVendor</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,74">
          <componentId>ui.tabBSCI</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,75">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.amforiFactoryName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,76">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.otherName</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,77">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.amforiFactoryID</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,78">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.partnerAmforiID</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,79">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.amforiSiteOverallStatus</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,80">
          <componentId>ui.tabBSCI.amforiGeneralInformationSection.amforiUpdatedOn</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,81">
          <componentId>ui.tabBSCI.baciAddressSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,82">
          <componentId>ui.tabBSCI.monitoringInformationSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,83">
          <componentId>ui.tabBSCI.baciContactSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,84">
          <componentId>ui.tabBSCI.monitoringResultSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,85">
          <componentId>ui.tabBSCI.gicsActivityClassificationSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,86">
          <componentId>ui.tabBSCI.productProcessClassificationSection</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,87">
          <componentId>ui.tabBSCI.factBsciSectionRatings</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,88">
          <componentId>ui.tabBSCI.factBsciMetrics</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,89">
          <componentId>ui.tabBSCI.factBsciGs1ProductClassification</componentId>
          <SuperAdministratorRole>readonly</SuperAdministratorRole>
          <GeneralAdministratorRole>readonly</GeneralAdministratorRole>
          <ClientAdministratorRole>readonly</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>readonly</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,90">
          <componentId>ui.tabAgreement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,91">
          <componentId>ui.tabAgreement.template.agreementTemplate</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,92">
          <componentId>ui.tabAgreement.template.agreementStatus</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,93">
          <componentId>ui.tabAgreement.factAgreements</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,94">
          <componentId>ui.tabAgreement.factAgreements.selectAgreement</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,95">
          <componentId>ui.tabAgreement.factAgreements.markAsConfirmed</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,96">
          <componentId>ui.tabAgreement.factAgreements.markAsCanceled</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,97">
          <componentId>ui.tabAgreement.factAgreements.contractDetailsIcon</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>editable</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,98">
          <componentId>ui.tabAgreement.factAgreements.internalComments</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>hidden</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
        <element position="fact_form_security.xlsx,acl,99">
          <componentId>ui.tabDetail.certification.status</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>readonly</vendors>
          <CommonFunctionsRole>editable</CommonFunctionsRole>
          <facts>editable</facts>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="fact_form_security.xlsx,acl,102">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
