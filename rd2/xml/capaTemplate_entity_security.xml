<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity_security module="capaTemplate" position="capaTemplate_entity_security.xlsx">
  <sheet id="_system" position="capaTemplate_entity_security.xlsx,_system">
    <ProjectInfo domain="$root" position="capaTemplate_entity_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="capaTemplate_entity_security.xlsx,_system,7">
      <elements id="default"/>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="capaTemplate_entity_security.xlsx,generalInfo">
    <GeneralInfo position="capaTemplate_entity_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="capaTemplate_entity_security.xlsx,condition">
    <ConditionList position="capaTemplate_entity_security.xlsx,condition,1">
      <elements id="default">
        <element position="capaTemplate_entity_security.xlsx,condition,4">
          <conditionId>$ANY</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="default" position="capaTemplate_entity_security.xlsx,default">
    <DataConditionMatrix position="capaTemplate_entity_security.xlsx,default,1">
      <elements id="default">
        <element position="capaTemplate_entity_security.xlsx,default,4">
          <ANY>editable</ANY>
        </element>
      </elements>
    </DataConditionMatrix>
  </sheet>
  <sheet id="acl" position="capaTemplate_entity_security.xlsx,acl">
    <DataRule position="capaTemplate_entity_security.xlsx,acl,1">
      <elements id="default">
        <element position="capaTemplate_entity_security.xlsx,acl,4">
          <conditionId>$ANY</conditionId>
          <SuperAdministratorGroup>editable</SuperAdministratorGroup>
          <GeneralAdministratorGroup>editable</GeneralAdministratorGroup>
          <ClientAdministratorGroup>editable</ClientAdministratorGroup>
          <vendors>readonly</vendors>
          <facts>editable</facts>
          <custs>editable</custs>
          <forwarders>editable</forwarders>
          <CommonFunctionsGroup>readonly</CommonFunctionsGroup>
          <Manager>editable</Manager>
          <Buyer>editable</Buyer>
          <SeniorMerchandiser>editable</SeniorMerchandiser>
          <EDITOR>editable</EDITOR>
          <READER>readonly</READER>
        </element>
      </elements>
    </DataRule>
  </sheet>
</entity_security>
