<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="domain" position="domain_view_security.xlsx">
  <sheet id="generalInfo" position="domain_view_security.xlsx,generalInfo">
    <GeneralInfo position="domain_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="domain_view_security.xlsx,condition">
    <ConditionList position="domain_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="acl" position="domain_view_security.xlsx,acl">
    <ActionRule position="domain_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="domain_view_security.xlsx,acl,4">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,5">
          <actionId>searchNewDomainFromRd1</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,6">
          <actionId>searchNewDomainFromRd2</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,7">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,8">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,9">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,10">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,11">
          <actionId>adminModuleDownload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,12">
          <actionId>adminModuleUpload</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>has</custs>
          <forwarders>has</forwarders>
          <facts>has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,13">
          <actionId>batchUpdateField</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="domain_view_security.xlsx,acl,14">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>has</cpmAdmin>
          <CommonFunctionsRole>has</CommonFunctionsRole>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="domain_view_security.xlsx,acl,17">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="domain_view_security.xlsx,acl,22">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
