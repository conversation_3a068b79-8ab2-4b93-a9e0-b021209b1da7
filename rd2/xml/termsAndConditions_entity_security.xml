<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<entity_security module="termsAndConditions" position="termsAndConditions_entity_security.xlsx">
  <sheet id="_system" position="termsAndConditions_entity_security.xlsx,_system">
    <ProjectInfo domain="$root" position="termsAndConditions_entity_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="termsAndConditions_entity_security.xlsx,_system,7">
      <elements id="default">
        <element position="termsAndConditions_entity_security.xlsx,_system,10">
          <updatedOn>2013/Oct/16</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="termsAndConditions_entity_security.xlsx,generalInfo">
    <GeneralInfo position="termsAndConditions_entity_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="termsAndConditions_entity_security.xlsx,condition">
    <ConditionList position="termsAndConditions_entity_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="default" position="termsAndConditions_entity_security.xlsx,default">
    <DataConditionMatrix position="termsAndConditions_entity_security.xlsx,default,1">
      <elements id="default">
        <element position="termsAndConditions_entity_security.xlsx,default,4">
          <ANY>editable</ANY>
        </element>
      </elements>
    </DataConditionMatrix>
  </sheet>
  <sheet id="acl" position="termsAndConditions_entity_security.xlsx,acl">
    <DataRule position="termsAndConditions_entity_security.xlsx,acl,1">
      <elements id="default">
        <element position="termsAndConditions_entity_security.xlsx,acl,4">
          <conditionId>$ANY</conditionId>
          <SuperAdministratorGroup>editable</SuperAdministratorGroup>
          <GeneralAdministratorGroup>editable</GeneralAdministratorGroup>
          <ClientAdministratorGroup>editable</ClientAdministratorGroup>
          <Buyer>editable</Buyer>
          <Merchandiser>editable</Merchandiser>
          <Planner>readonly</Planner>
          <SeniorMerchandiser>editable</SeniorMerchandiser>
          <ProductDeveloper>editable</ProductDeveloper>
          <QA>readonly</QA>
          <Inspector>readonly</Inspector>
          <Finance>readonly</Finance>
          <Logistics>readonly</Logistics>
          <Manager>editable</Manager>
          <EDITOR>editable</EDITOR>
          <READER>readonly</READER>
          <vendors>readonly</vendors>
        </element>
      </elements>
    </DataRule>
  </sheet>
</entity_security>
