<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<form_security module="termsAndConditions" position="termsAndConditions_form_security.xlsx">
  <sheet id="_system" position="termsAndConditions_form_security.xlsx,_system">
    <ProjectInfo domain="$root" position="termsAndConditions_form_security.xlsx,_system,1" release_no="1.00"/>
    <ProductVersion position="termsAndConditions_form_security.xlsx,_system,7">
      <elements id="default">
        <element position="termsAndConditions_form_security.xlsx,_system,10">
          <updatedOn>2013/Dec/04</updatedOn>
          <summary>Creation</summary>
          <releaseNo>1.00</releaseNo>
        </element>
      </elements>
    </ProductVersion>
  </sheet>
  <sheet id="generalInfo" position="termsAndConditions_form_security.xlsx,generalInfo">
    <GeneralInfo position="termsAndConditions_form_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="termsAndConditions_form_security.xlsx,condition">
    <ConditionList position="termsAndConditions_form_security.xlsx,condition,1">
      <elements id="default">
        <element position="termsAndConditions_form_security.xlsx,condition,4">
          <conditionId>docStatusActive</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,5">
          <conditionId>docStatusInactive</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,6">
          <conditionId>editingStatusConfirmed</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,7">
          <conditionId>editingStatusPending</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,8">
          <conditionId>internalStatusUnlocked</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,9">
          <conditionId>internalStatusLockedByOthers</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,10">
          <conditionId>internalStatusLocked</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,11">
          <conditionId>internalStatusNew</conditionId>
        </element>
        <element position="termsAndConditions_form_security.xlsx,condition,12">
          <conditionId>isNotLatest</conditionId>
        </element>
      </elements>
    </ConditionList>
  </sheet>
  <sheet id="acl" position="termsAndConditions_form_security.xlsx,acl">
    <ActionRule position="termsAndConditions_form_security.xlsx,acl,1">
      <elements id="default">
        <element position="termsAndConditions_form_security.xlsx,acl,4">
          <actionId>newDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,5">
          <actionId>amendDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,6">
          <actionId>saveAndConfirm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,7">
          <actionId>discardDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,8">
          <actionId>copyDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,9">
          <actionId>activateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,10">
          <actionId>deactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,11">
          <actionId>loadDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,12">
          <actionId>initializeCpm</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,13">
          <actionId>customPrint01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,14">
          <actionId>customPrint02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,15">
          <actionId>customPrint03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,16">
          <actionId>customPrint04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,17">
          <actionId>customPrint05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,18">
          <actionId>customPrint06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,19">
          <actionId>customPrint07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,20">
          <actionId>customPrint08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,21">
          <actionId>customPrint09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,22">
          <actionId>customPrint10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,23">
          <actionId>customExport01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,24">
          <actionId>customExport02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,25">
          <actionId>customExport03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,26">
          <actionId>customExport04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,27">
          <actionId>customExport05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,28">
          <actionId>customExport06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,29">
          <actionId>customExport07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,30">
          <actionId>customExport08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,31">
          <actionId>customExport09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,32">
          <actionId>customExport10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,33">
          <actionId>termsAndConditionsCustom01</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,34">
          <actionId>termsAndConditionsCustom02</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,35">
          <actionId>termsAndConditionsCustom03</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,36">
          <actionId>termsAndConditionsCustom04</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,37">
          <actionId>termsAndConditionsCustom05</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,38">
          <actionId>termsAndConditionsCustom06</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,39">
          <actionId>termsAndConditionsCustom07</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,40">
          <actionId>termsAndConditionsCustom08</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,41">
          <actionId>termsAndConditionsCustom09</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,42">
          <actionId>termsAndConditionsCustom10</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,43">
          <actionId>markAsCustomStatus01Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,44">
          <actionId>markAsCustomStatus02Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,45">
          <actionId>markAsCustomStatus03Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,46">
          <actionId>markAsCustomStatus04Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,47">
          <actionId>markAsCustomStatus05Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,48">
          <actionId>markAsCustomStatus06Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,49">
          <actionId>markAsCustomStatus07Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,50">
          <actionId>markAsCustomStatus08Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,51">
          <actionId>markAsCustomStatus09Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,52">
          <actionId>markAsCustomStatus10Doc</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>not-has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,53">
          <actionId>reinitializeCpm</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,54">
          <actionId>refreshCpmTemplate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>has</cpmAdmin>
        </element>
        <element position="termsAndConditions_form_security.xlsx,acl,55">
          <actionId>refreshCpmPlanDate</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
          <cpmAdmin>has</cpmAdmin>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="termsAndConditions_form_security.xlsx,acl,58">
      <elements id="default">
        <element position="termsAndConditions_form_security.xlsx,acl,61">
          <componentId>ui.tabHeader</componentId>
          <SuperAdministratorRole>editable</SuperAdministratorRole>
          <GeneralAdministratorRole>editable</GeneralAdministratorRole>
          <ClientAdministratorRole>editable</ClientAdministratorRole>
          <vendors>not-has</vendors>
          <custs>not-has</custs>
          <forwarders>not-has</forwarders>
          <facts>not-has</facts>
        </element>
      </elements>
    </UIRule>
    <FieldDenyRule position="termsAndConditions_form_security.xlsx,acl,64">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</form_security>
