<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<view_security module="termsAndConditions" position="termsAndConditions_view_security.xlsx">
  <sheet id="generalInfo" position="termsAndConditions_view_security.xlsx,generalInfo">
    <GeneralInfo position="termsAndConditions_view_security.xlsx,generalInfo,1" version="1"/>
  </sheet>
  <sheet id="condition" position="termsAndConditions_view_security.xlsx,condition">
    <ConditionList position="termsAndConditions_view_security.xlsx,condition,1">
      <elements id="default"/>
    </ConditionList>
  </sheet>
  <sheet id="acl" position="termsAndConditions_view_security.xlsx,acl">
    <ActionRule position="termsAndConditions_view_security.xlsx,acl,1">
      <elements id="default">
        <element position="termsAndConditions_view_security.xlsx,acl,4">
          <actionId>searchActivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,5">
          <actionId>searchDeactivateDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,6">
          <actionId>searchNewDoc</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,7">
          <actionId>searchViewAllExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,8">
          <actionId>searchViewCurExport</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,9">
          <actionId>batchUpdateField</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,10">
          <actionId>openBatchUpdateWin</actionId>
          <SuperAdministratorRole>has</SuperAdministratorRole>
          <GeneralAdministratorRole>has</GeneralAdministratorRole>
          <ClientAdministratorRole>has</ClientAdministratorRole>
          <cpmAdmin>not-has</cpmAdmin>
          <CommonFunctionsRole>not-has</CommonFunctionsRole>
        </element>
        <element position="termsAndConditions_view_security.xlsx,acl,11">
          <actionId>openCpmMode</actionId>
          <SuperAdministratorRole>not-has</SuperAdministratorRole>
          <GeneralAdministratorRole>not-has</GeneralAdministratorRole>
          <ClientAdministratorRole>not-has</ClientAdministratorRole>
          <cpmAdmin>has</cpmAdmin>
          <CommonFunctionsRole>has</CommonFunctionsRole>
        </element>
      </elements>
    </ActionRule>
    <UIRule position="termsAndConditions_view_security.xlsx,acl,14">
      <elements id="default"/>
    </UIRule>
    <FieldDenyRule position="termsAndConditions_view_security.xlsx,acl,19">
      <elements id="default"/>
    </FieldDenyRule>
  </sheet>
</view_security>
