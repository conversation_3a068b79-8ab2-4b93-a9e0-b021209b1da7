<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<system position="system.xlsx">
  <sheet id="_system" position="system.xlsx,_system">
    <ProjectInfo domain="RD2" position="system.xlsx,_system,1" release_no="5.01"/>
  </sheet>
  <sheet id="condition" position="system.xlsx,condition">
    <Conditions position="system.xlsx,condition,1">
      <elements id="default">
        <element position="system.xlsx,condition,4">
          <conditionId>$ANY</conditionId>
          <expression>$ANY</expression>
          <customClass/>
          <description>Default condition, menas can match any condition.</description>
        </element>
        <element position="system.xlsx,condition,5">
          <conditionId>statusNew</conditionId>
          <expression>entity.status = 'new'</expression>
          <customClass/>
          <description>True means document is in New status. False means document is not in New status.</description>
        </element>
        <element position="system.xlsx,condition,6">
          <conditionId>statusDraft</conditionId>
          <expression>entity.status = 'draft'</expression>
          <customClass/>
          <description>True means document is in Draft status. False means document is not in Draft status.</description>
        </element>
        <element position="system.xlsx,condition,7">
          <conditionId>statusConfirm</conditionId>
          <expression>entity.status = 'confirm'</expression>
          <customClass/>
          <description>True means document is in Confirm status. False means document is not in Confirm status.</description>
        </element>
        <element position="system.xlsx,condition,8">
          <conditionId>statusPending</conditionId>
          <expression>entity.status = 'pending'</expression>
          <customClass/>
          <description>True means document is in Pending status. False means document is not in Pending status.</description>
        </element>
        <element position="system.xlsx,condition,9">
          <conditionId>statusClosed</conditionId>
          <expression>entity.status = 'closed'</expression>
          <customClass/>
          <description>True means document is in Closed status. False means document is not in Closed status.</description>
        </element>
        <element position="system.xlsx,condition,10">
          <conditionId>statusSubmitted</conditionId>
          <expression>entity.status = 'submitted'</expression>
          <customClass/>
          <description>True means document is in Submitted status. False means document is not in Submitted status.</description>
        </element>
        <element position="system.xlsx,condition,11">
          <conditionId>statusCancelled</conditionId>
          <expression>entity.status = 'cancelled'</expression>
          <customClass/>
          <description>True means document is in Cancelled status. False means document is not in Cancelled status.</description>
        </element>
        <element position="system.xlsx,condition,12">
          <conditionId>statusCanceled</conditionId>
          <expression>entity.status = 'canceled'</expression>
          <customClass/>
          <description>True means document is in Canceled status. False means document is not in Canceled status.</description>
        </element>
        <element position="system.xlsx,condition,13">
          <conditionId>statusAgreed</conditionId>
          <expression>entity.status = 'agreed'</expression>
          <customClass/>
          <description>True means document is in Agreed status. False means document is not in Agreed status.</description>
        </element>
        <element position="system.xlsx,condition,14">
          <conditionId>statusWaive</conditionId>
          <expression>entity.status = 'waive'</expression>
          <customClass/>
          <description>True means document is in Waive status. False means document is not in Waive status.</description>
        </element>
        <element position="system.xlsx,condition,15">
          <conditionId>statusApproved</conditionId>
          <expression>entity.status = 'approved'</expression>
          <customClass/>
          <description>True means document is in Approved status. False means document is not in Approved status.</description>
        </element>
        <element position="system.xlsx,condition,16">
          <conditionId>statusConcept</conditionId>
          <expression>entity.status = 'concept'</expression>
          <customClass/>
          <description>True means document is in Concept status. False means document is not in Concept status.</description>
        </element>
        <element position="system.xlsx,condition,17">
          <conditionId>statusCosting</conditionId>
          <expression>entity.status = 'costing'</expression>
          <customClass/>
          <description>True means document is in Costing status. False means document is not in Costing status.</description>
        </element>
        <element position="system.xlsx,condition,18">
          <conditionId>statusAdopted</conditionId>
          <expression>entity.status = 'adopted'</expression>
          <customClass/>
          <description>True means document is in Adopted status. False means document is not in Adopted status.</description>
        </element>
        <element position="system.xlsx,condition,19">
          <conditionId>statusOfficial</conditionId>
          <expression>entity.status = 'official'</expression>
          <customClass/>
          <description>True means document is in Offical status. False means document is not in Offical status.</description>
        </element>
        <element position="system.xlsx,condition,20">
          <conditionId>statusCompleted</conditionId>
          <expression>entity.status = 'completed'</expression>
          <customClass/>
          <description>True means document is in Completed status. False means document is not in Completed status.</description>
        </element>
        <element position="system.xlsx,condition,21">
          <conditionId>statusPublished</conditionId>
          <expression>entity.status = 'published'</expression>
          <customClass/>
          <description>True means document is in Published status. False means document is not in Published status.</description>
        </element>
        <element position="system.xlsx,condition,22">
          <conditionId>statusOpen</conditionId>
          <expression>entity.status = 'open'</expression>
          <customClass/>
          <description>True means document is in Open status. False means document is not in Open status.</description>
        </element>
        <element position="system.xlsx,condition,23">
          <conditionId>statusShortListed</conditionId>
          <expression>entity.status = 'shortListed'</expression>
          <customClass/>
          <description>True means document is in ShortListed status. False means document is not in ShortListed status.</description>
        </element>
        <element position="system.xlsx,condition,24">
          <conditionId>statusConfirmedToBuy</conditionId>
          <expression>entity.status = 'confirmedToBuy'</expression>
          <customClass/>
          <description>True means document is in ConfirmedToBuy status. False means document is not in ConfirmedToBuy status.</description>
        </element>
        <element position="system.xlsx,condition,25">
          <conditionId>statusIntentToBuy</conditionId>
          <expression>entity.status = 'intentToBuy'</expression>
          <customClass/>
          <description>True means document is in IntentToBuy status. False means document is not in IntentToBuy status.</description>
        </element>
        <element position="system.xlsx,condition,26">
          <conditionId>statusOrdered</conditionId>
          <expression>entity.status = 'ordered'</expression>
          <customClass/>
          <description>True means document is in Orfered status. False means document is not in Orfered status.</description>
        </element>
        <element position="system.xlsx,condition,27">
          <conditionId>statusQuoted</conditionId>
          <expression>entity.status = 'quoted'</expression>
          <customClass/>
          <description>True means document is in Quoted status. False means document is not in Quoted status.</description>
        </element>
        <element position="system.xlsx,condition,28">
          <conditionId>statusRevisionRequest</conditionId>
          <expression>entity.status = 'revisionRequest'</expression>
          <customClass/>
          <description>True means document is in revisionRequest status. False means document is not in revisionRequest status.</description>
        </element>
        <element position="system.xlsx,condition,29">
          <conditionId>statusDeclinedToResponse</conditionId>
          <expression>entity.status = 'declinedToResponse'</expression>
          <customClass/>
          <description>True means document is in DeclinedToResponse status. False means document is not in DeclinedToResponse status.</description>
        </element>
        <element position="system.xlsx,condition,30">
          <conditionId>statusRejectedToBuy</conditionId>
          <expression>entity.status = 'rejectedToBuy'</expression>
          <customClass/>
          <description>True means document is in RejectedToBuy status. False means document is not in RejectedToBuy status.</description>
        </element>
        <element position="system.xlsx,condition,31">
          <conditionId>statusRequested</conditionId>
          <expression>entity.status = 'requested'</expression>
          <customClass/>
          <description>True means document is in RequestedToBuy status. False means document is not in RequestedToBuy status.</description>
        </element>
        <element position="system.xlsx,condition,32">
          <conditionId>statusInProgress</conditionId>
          <expression>entity.status = 'inProgress'</expression>
          <customClass/>
          <description>True means document is in InProgress status. False means document is not in InProgress status.</description>
        </element>
        <element position="system.xlsx,condition,33">
          <conditionId>statusFinal</conditionId>
          <expression>entity.status = 'final'</expression>
          <customClass/>
          <description>True means document is in Final status. False means document is not in Final status.</description>
        </element>
        <element position="system.xlsx,condition,34">
          <conditionId>statusRejected</conditionId>
          <expression>entity.status = 'rejected'</expression>
          <customClass/>
          <description>True means document is in Rejected status. False means document is not in Rejected status.</description>
        </element>
        <element position="system.xlsx,condition,35">
          <conditionId>statusReleasedToVendor</conditionId>
          <expression>entity.status = 'releasedToVendor'</expression>
          <customClass/>
          <description>True means document is in Released To Vendor status. False means document is not in Released To Vendor status.</description>
        </element>
        <element position="system.xlsx,condition,36">
          <conditionId>statusVendorConfirmed</conditionId>
          <expression>entity.status = 'vendorConfirmed'</expression>
          <customClass/>
          <description>True means document is in Vendor Confirmed status. False means document is not in Vendor Confirmed status.</description>
        </element>
        <element position="system.xlsx,condition,37">
          <conditionId>statusVendorRejected</conditionId>
          <expression>entity.status = 'vendorRejected'</expression>
          <customClass/>
          <description>True means document is in Vendor Rejected status. False means document is not in Vendor Rejected status.</description>
        </element>
        <element position="system.xlsx,condition,38">
          <conditionId>costOver5M</conditionId>
          <expression>entity.cost &gt; 5000000</expression>
          <customClass/>
          <description>The value of COST field in document larger than 5000000</description>
        </element>
        <element position="system.xlsx,condition,39">
          <conditionId>costLess3M</conditionId>
          <expression>entity.cost &lt; 3000000</expression>
          <customClass/>
          <description>The value of COST field smaller than 3000000</description>
        </element>
        <element position="system.xlsx,condition,40">
          <conditionId>statusSaved</conditionId>
          <expression>entity.status = 'saved'</expression>
          <customClass/>
          <description>True means document is in Saved status. False means document is not in Saved status.</description>
        </element>
        <element position="system.xlsx,condition,41">
          <conditionId>statusEnabled</conditionId>
          <expression>entity.status = 'enabled'</expression>
          <customClass/>
          <description>True means document is in Enabled status. False means document is not in Enabled status.</description>
        </element>
        <element position="system.xlsx,condition,42">
          <conditionId>statusDisabled</conditionId>
          <expression>entity.status = 'disabled'</expression>
          <customClass/>
          <description>True means document is in Disabled status. False means document is not in Disabled status.</description>
        </element>
        <element position="system.xlsx,condition,43">
          <conditionId>statusActive</conditionId>
          <expression>entity.status = 'active'</expression>
          <customClass/>
          <description>True means document is in Active status. False means document is not in Active status.</description>
        </element>
        <element position="system.xlsx,condition,44">
          <conditionId>statusInactive</conditionId>
          <expression>entity.status = 'inactive'</expression>
          <customClass/>
          <description>True means document is in Inactive status. False means document is not in Inactive status.</description>
        </element>
        <element position="system.xlsx,condition,45">
          <conditionId>statusPoAccepted</conditionId>
          <expression>entity.status = 'poAccepted'</expression>
          <customClass/>
          <description>True means document is in poAccepted status. False means document is not in poAccepted status.</description>
        </element>
        <element position="system.xlsx,condition,46">
          <conditionId>statusPoRejected</conditionId>
          <expression>entity.status = 'poRejected'</expression>
          <customClass/>
          <description>True means document is in poRejected status. False means document is not in poRejected status.</description>
        </element>
        <element position="system.xlsx,condition,47">
          <conditionId>statusPoChangeProposed</conditionId>
          <expression>entity.status = 'poChangeProposed'</expression>
          <customClass/>
          <description>True means document is in poChangeProposed status. False means document is not in poChangeProposed status.</description>
        </element>
        <element position="system.xlsx,condition,48">
          <conditionId>statusChangAccepted</conditionId>
          <expression>entity.status = 'changeAccepted'</expression>
          <customClass/>
          <description>True means document is in changeAccepted status. False means document is not in changeAccepted status.</description>
        </element>
        <element position="system.xlsx,condition,49">
          <conditionId>statusChangRejected</conditionId>
          <expression>entity.status = 'changeRejected'</expression>
          <customClass/>
          <description>True means document is in changeRejected status. False means document is not in changeRejected status.</description>
        </element>
        <element position="system.xlsx,condition,50">
          <conditionId>docStatusActive</conditionId>
          <expression>entity.docStatus = 'active'</expression>
          <customClass/>
          <description>True means document is Active.</description>
        </element>
        <element position="system.xlsx,condition,51">
          <conditionId>docStatusInactive</conditionId>
          <expression>entity.docStatus = 'inactive'</expression>
          <customClass/>
          <description>True means document is Inactive.</description>
        </element>
        <element position="system.xlsx,condition,52">
          <conditionId>docStatusCanceled</conditionId>
          <expression>entity.docStatus = 'canceled'</expression>
          <customClass/>
          <description>True means document is Canceled.</description>
        </element>
        <element position="system.xlsx,condition,53">
          <conditionId>editingStatusDraft</conditionId>
          <expression>entity.editingStatus = 'draft'</expression>
          <customClass/>
          <description>Indicate document Editing status is Draft.</description>
        </element>
        <element position="system.xlsx,condition,54">
          <conditionId>editingStatusPending</conditionId>
          <expression>entity.editingStatus = 'pending'</expression>
          <customClass/>
          <description>True means document Editing status is pending.</description>
        </element>
        <element position="system.xlsx,condition,55">
          <conditionId>editingStatusConfirmed</conditionId>
          <expression>entity.editingStatus = 'confirmed'</expression>
          <customClass/>
          <description>True means document Editing status is Confirmed.</description>
        </element>
        <element position="system.xlsx,condition,56">
          <conditionId>editingStatusDeploying</conditionId>
          <expression>entity.editingStatus = 'deploying'</expression>
          <customClass/>
          <description>True means document Editing status is hot deploying.</description>
        </element>
        <element position="system.xlsx,condition,57">
          <conditionId>editingStatusDeploySucceed</conditionId>
          <expression>entity.editingStatus = 'deploySucceed'</expression>
          <customClass/>
          <description>True means document Editing status is hot deploy succeed.</description>
        </element>
        <element position="system.xlsx,condition,58">
          <conditionId>editingStatusDeployFailed</conditionId>
          <expression>entity.editingStatus = 'deployFailed'</expression>
          <customClass/>
          <description>True means document Editing status is hot deploy failed.</description>
        </element>
        <element position="system.xlsx,condition,59">
          <conditionId>internalStatusNew</conditionId>
          <expression>null != entity.?internalStatus</expression>
          <customClass/>
          <description>True means document internalStatus is not null. That means the document is a new created document but not saved into database.</description>
        </element>
        <element position="system.xlsx,condition,60">
          <conditionId>internalStatusUnlocked</conditionId>
          <expression>null = entity.?lockingStatus</expression>
          <customClass/>
          <description>True means document lockingStatus null. It means document is not locked by anyone.</description>
        </element>
        <element position="system.xlsx,condition,61">
          <conditionId>internalStatusLocked</conditionId>
          <expression>entity.lockingStatus = '$locked'</expression>
          <customClass/>
          <description>lockingStatus is a runtime status. User locked the document, system will set this field to $locked, so if the condition is true, means document is locked by user.</description>
        </element>
        <element position="system.xlsx,condition,62">
          <conditionId>internalStatusLockedByOthers</conditionId>
          <expression>entity.lockingStatus = '$lockedByOthers'</expression>
          <customClass/>
          <description>lockingStatus is a runtime status. This condition means there are other users locked the document.</description>
        </element>
        <element position="system.xlsx,condition,63">
          <conditionId>isSet</conditionId>
          <expression>entity.itemType.code = 'SET'</expression>
          <customClass/>
          <description>Use to indicate item document is solo item or a set of item. True: means item document is a set of item. False: means item document is solo item.</description>
        </element>
        <element position="system.xlsx,condition,64">
          <conditionId>isItem</conditionId>
          <expression>entity.itemType.code = 'ITEM'</expression>
          <customClass/>
          <description>Use to indicate item document is solo item or a set of item. True: means item document is solo item. False: means item document is a set of item.</description>
        </element>
        <element position="system.xlsx,condition,65">
          <conditionId>isOrderIndividual</conditionId>
          <expression>entity.isOrderIndividual = false</expression>
          <customClass/>
          <description>True means document is Order as Individual. False means document is not Order as Individual.</description>
        </element>
        <element position="system.xlsx,condition,66">
          <conditionId>notChildItem</conditionId>
          <expression>entity.itemSubItem!=null &amp;&amp; entity.itemSubItem.size = 0 &amp;&amp; entity.itemType.code = 'SET'</expression>
          <customClass/>
          <description>it meas the item document with set type has no child item.</description>
        </element>
        <element position="system.xlsx,condition,67">
          <conditionId>statusSourcing</conditionId>
          <expression>entity.status = 'sourcing'</expression>
          <customClass/>
          <description>True means document is in Sourcing status. False means document is not in Sourcing status.</description>
        </element>
        <element position="system.xlsx,condition,68">
          <conditionId>rootDocument</conditionId>
          <expression>entity.domainId = '/'</expression>
          <customClass/>
          <description>The domain is in root domain</description>
        </element>
        <element position="system.xlsx,condition,69">
          <conditionId>notRootDocument</conditionId>
          <expression>entity.domainId != '/'</expression>
          <customClass/>
          <description>The domain is not in root domain</description>
        </element>
        <element position="system.xlsx,condition,70">
          <conditionId>domainNotMatch</conditionId>
          <expression>entity.domainId != user.domainId</expression>
          <customClass/>
          <description>The domain is not match</description>
        </element>
        <element position="system.xlsx,condition,71">
          <conditionId>domainAndHclMatch</conditionId>
          <expression>isOrChildOfUserHclNodes(entity.hierarchy,entity.hierarchyHcl)</expression>
          <customClass/>
          <description>Means user owned hierarchy nodes contains also the node defined in entity.hierarchy, entity.hierarchyHcl.</description>
        </element>
        <element position="system.xlsx,condition,72">
          <conditionId>domainAndLinkObjHclMatch</conditionId>
          <expression>isOrChildOfUserHclNodes(entity.itemId.hierarchy,entity.itemId.hierarchyHcl)</expression>
          <customClass/>
          <description>Means user owned hierarchy nodes contains also the node defined in entity.itemId.hierarchy, entity.itemId.hierarchyHcl.</description>
        </element>
        <element position="system.xlsx,condition,73">
          <conditionId>validateCpmTaskStatus</conditionId>
          <expression>entity.updateStatus.name = 'Rejected'</expression>
          <customClass/>
          <description>The CPM updateStatus is Rejected</description>
        </element>
        <element position="system.xlsx,condition,74">
          <conditionId>cpmTemplStaticType</conditionId>
          <expression>entity.cpmTemplType=1</expression>
          <customClass/>
          <description>The CPM template type is static type(DEPRECATED)</description>
        </element>
        <element position="system.xlsx,condition,75">
          <conditionId>cpmTemplDynamicType</conditionId>
          <expression>entity.cpmTemplType=2</expression>
          <customClass/>
          <description>The CPM template type is dynamic type(DEPRECATED)</description>
        </element>
        <element position="system.xlsx,condition,76">
          <conditionId>cpmTemplTaskAnchorDateTypeIsDocField</conditionId>
          <expression>entity.milestoneAnchorDateType=1</expression>
          <customClass/>
          <description>The CPM Templ Task Milestone AnchorDateType is Document Field</description>
        </element>
        <element position="system.xlsx,condition,77">
          <conditionId>cpmTemplTaskAnchorDateTypeIsFixedDate</conditionId>
          <expression>entity.milestoneAnchorDateType=2</expression>
          <customClass/>
          <description>The CPM Templ Task Milestone AnchorDateType is Fixed Date</description>
        </element>
        <element position="system.xlsx,condition,78">
          <conditionId>cpmTemplTaskAnchorDateTypeIsCustomClass</conditionId>
          <expression>entity.milestoneAnchorDateType=3</expression>
          <customClass/>
          <description>The CPM Templ Task Milestone AnchorDateType is Custom Class</description>
        </element>
        <element position="system.xlsx,condition,79">
          <conditionId>notCurrentUser</conditionId>
          <expression>entity.loginId != user.loginId</expression>
          <customClass/>
          <description>User is not current login user</description>
        </element>
        <element position="system.xlsx,condition,80">
          <conditionId>hclNotEmpty</conditionId>
          <expression>entity.hierarchy != null</expression>
          <customClass/>
          <description>HCL is not empty</description>
        </element>
        <element position="system.xlsx,condition,81">
          <conditionId>vqVendorNameNotEmpty</conditionId>
          <expression>entity.vendorId != null</expression>
          <customClass/>
          <description>Quatation vender name is not empty</description>
        </element>
        <element position="system.xlsx,condition,82">
          <conditionId>costGridNotAllowEdit</conditionId>
          <expression>entity.allowAddDeleteElements = false</expression>
          <customClass/>
          <description>The cost grid in costing document form does NOT allow to be editted</description>
        </element>
        <element position="system.xlsx,condition,83">
          <conditionId>costSheetNoCalError</conditionId>
          <expression>entity.calError = false</expression>
          <customClass/>
          <description>Indicate the calculation process of costing document is success or not. True means no error happened in calculation process. False means the calculation failed and get some error.</description>
        </element>
        <element position="system.xlsx,condition,84">
          <conditionId>isShortlisted</conditionId>
          <expression>entity.shortListed = true</expression>
          <customClass/>
          <description>To indicate whether the quotation document is marked as "Shortlisted". True means the Quotation document is in "Shortlisted"</description>
        </element>
        <element position="system.xlsx,condition,85">
          <conditionId>isNotShortlisted</conditionId>
          <expression>entity.shortListed = false||entity.shortListed = null</expression>
          <customClass/>
          <description>To indicate whether the quotation document is marked as "Shortlisted". True means the quotation is not in "Shortlisted"</description>
        </element>
        <element position="system.xlsx,condition,86">
          <conditionId>isDefaultItem</conditionId>
          <expression>entity.isDefaultItem=true</expression>
          <customClass/>
          <description>True means item document is default item of some brief documents. False means item document is not default item of some brief documents</description>
        </element>
        <element position="system.xlsx,condition,87">
          <conditionId>basisMatchToFixed</conditionId>
          <expression>entity.basis='FIXED'</expression>
          <customClass/>
          <description>Document field BASIS value is  "FIXED"</description>
        </element>
        <element position="system.xlsx,condition,88">
          <conditionId>statusCustomsFilingAccepted</conditionId>
          <expression>entity.status = 'customsFilingAccepted'</expression>
          <customClass/>
          <description>True means document is in CustomsFilingAccepted status. False means document is not in CustomsFilingAccepted status.</description>
        </element>
        <element position="system.xlsx,condition,89">
          <conditionId>statusShipmentOnBoard</conditionId>
          <expression>entity.status = 'shipmentOnBoard'</expression>
          <customClass/>
          <description>True means document is in ShipmentOnBoard status. False means document is not in ShipmentOnBoard status.</description>
        </element>
        <element position="system.xlsx,condition,90">
          <conditionId>statusDocumentsUploaded</conditionId>
          <expression>entity.status = 'documentsUploaded'</expression>
          <customClass/>
          <description>True means document is in DocumentsUploaded status. False means document is not in DocumentsUploaded status.</description>
        </element>
        <element position="system.xlsx,condition,91">
          <conditionId>statusShipmentClosed</conditionId>
          <expression>entity.status = 'shipmentClosed'</expression>
          <customClass/>
          <description>True means document is in ShipmentClosed status. False means document is not in ShipmentClosed status.</description>
        </element>
        <element position="system.xlsx,condition,92">
          <conditionId>statusAllInvoiceIssued</conditionId>
          <expression>entity.status = 'allInvoiceIssued'</expression>
          <customClass/>
          <description>True means document is in AllInvoiceIssued status. False means document is not in AllInvoiceIssued status.</description>
        </element>
        <element position="system.xlsx,condition,93">
          <conditionId>isDisabled</conditionId>
          <expression>entity.isDisabled=false</expression>
          <customClass/>
          <description>Document field isDisabled value is "false". It means document is not disabled.</description>
        </element>
        <element position="system.xlsx,condition,94">
          <conditionId>validateHexCode</conditionId>
          <expression>entity.codeType.refNo='HEX'</expression>
          <customClass/>
          <description>Use to validate color code type of color document is HEX</description>
        </element>
        <element position="system.xlsx,condition,95">
          <conditionId>validateRgbCode</conditionId>
          <expression>entity.codeType.refNo='RGB'</expression>
          <customClass/>
          <description>Use to validate color code type of color document is RGB</description>
        </element>
        <element position="system.xlsx,condition,96">
          <conditionId>validateCmykCode</conditionId>
          <expression>entity.codeType.refNo='CMYK'</expression>
          <customClass/>
          <description>Use to validate color code type of color document is CMYK</description>
        </element>
        <element position="system.xlsx,condition,97">
          <conditionId>validateHsvCode</conditionId>
          <expression>entity.codeType.refNo='HSV'</expression>
          <customClass/>
          <description>Use to validate color code type of color document is HSV</description>
        </element>
        <element position="system.xlsx,condition,98">
          <conditionId>hasItem</conditionId>
          <expression>entity.itemId != null</expression>
          <customClass/>
          <description>The document has Item document.</description>
        </element>
        <element position="system.xlsx,condition,99">
          <conditionId>isNotExternalUpdatedMode</conditionId>
          <expression>null = entity.?isExternalUpdatedMode</expression>
          <customClass/>
          <description>True means the document updated by hub domain user.</description>
        </element>
        <element position="system.xlsx,condition,100">
          <conditionId>isExternalUpdatedMode</conditionId>
          <expression>entity.isExternalUpdatedMode = true</expression>
          <customClass/>
          <description>True means the document updated by external domain user.</description>
        </element>
        <element position="system.xlsx,condition,101">
          <conditionId>isExternalDomain</conditionId>
          <expression>entity.domainId != null &amp;&amp; entity.hubDomainId != null &amp;&amp; entity.domainId != entity.hubDomainId</expression>
          <customClass/>
          <description>True means the domain is external domain.False means the domain is not external domain.</description>
        </element>
        <element position="system.xlsx,condition,102">
          <conditionId>isHubDomain</conditionId>
          <expression>entity.domainId = entity.hubDomainId</expression>
          <customClass/>
          <description>True means the domain is a hub domain which contains at least one external domain.</description>
        </element>
        <element position="system.xlsx,condition,103">
          <conditionId>isNewEntity</conditionId>
          <expression>entity.isNewEntity() = true &amp;&amp; entity.status = null</expression>
          <customClass/>
          <description>The document is new created document and not set status yet.</description>
        </element>
        <element position="system.xlsx,condition,104">
          <conditionId>isNotLatest</conditionId>
          <expression>entity.isLatest != null &amp;&amp; entity.isLatest = false</expression>
          <customClass/>
          <description>The document is not latest version document.</description>
        </element>
        <element position="system.xlsx,condition,105">
          <conditionId>isNewDoc</conditionId>
          <expression>entity.newEntity = true</expression>
          <customClass/>
          <description>The entity is a new created entity.</description>
        </element>
        <element position="system.xlsx,condition,106">
          <conditionId>cpoItemWithColorSize</conditionId>
          <expression>(entity.cpoItemId.cpoItemColor instanceof java.util.Collection &amp;&amp; entity.cpoItemId.cpoItemColor.size&gt;0) &amp;&amp; (entity.cpoItemId.cpoItemSize instanceof java.util.Collection &amp;&amp; entity.cpoItemId.cpoItemSize.size&gt;0)</expression>
          <customClass/>
          <description>The Customer PO item has color and size.</description>
        </element>
        <element position="system.xlsx,condition,107">
          <conditionId>cpoItemWithoutColorSize</conditionId>
          <expression>entity.cpoItemId=null||entity.cpoItemId.cpoItemColor=null||entity.cpoItemId.cpoItemSize =null|| (entity.cpoItemId.cpoItemColor instanceof java.util.Collection &amp;&amp; entity.cpoItemId.cpoItemColor.size=0) || (entity.cpoItemId.cpoItemSize instanceof java.util.Collection &amp;&amp; entity.cpoItemId.cpoItemSize.size=0)</expression>
          <customClass/>
          <description>The Customer PO item does not have color and size.</description>
        </element>
        <element position="system.xlsx,condition,108">
          <conditionId>vpoItemWithoutColorSize</conditionId>
          <expression>entity.vpoItemId=null||entity.vpoItemId.vpoItemColor=null||entity.vpoItemId.vpoItemSize =null||(entity.vpoItemId.vpoItemColor instanceof java.util.Collection &amp;&amp; entity.vpoItemId.vpoItemColor.size=0)||(entity.vpoItemId.vpoItemSize instanceof java.util.Collection &amp;&amp; entity.vpoItemId.vpoItemSize.size=0)</expression>
          <customClass/>
          <description>The Vendor PO item does not have color and size.</description>
        </element>
        <element position="system.xlsx,condition,109">
          <conditionId>isAvailable</conditionId>
          <expression>entity.enabled = true</expression>
          <customClass/>
          <description>Use to validate Custom Field module. If Available field is checked, the Field Label is mandatory in Custom Fields grid.</description>
        </element>
        <element position="system.xlsx,condition,110">
          <conditionId>isMpoVendorAndMpoItemsNotEmpty</conditionId>
          <expression>entity.mpoItems!=null &amp;&amp; (entity.mpoItems instanceof java.util.Collection &amp;&amp; entity.mpoItems.size &gt; 0) &amp;&amp; entity.vendor !=null</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,111">
          <conditionId>isMpoCustomerAndMpoItemsNotEmpty</conditionId>
          <expression>entity.mpoItems!=null &amp;&amp; entity.mpoItems instanceof java.util.Collection &amp;&amp; entity.mpoItems.size &gt; 0 &amp;&amp; entity.customer !=null</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,112">
          <conditionId>nameDefaultRole</conditionId>
          <expression>entity.name = '$DEFAULT_ROLE'</expression>
          <customClass/>
          <description>While matched, mark the name field of the doc to readonly.</description>
        </element>
        <element position="system.xlsx,condition,113">
          <conditionId>nameDefaultGroup</conditionId>
          <expression>entity.name = '$DEFAULT_GROUP'</expression>
          <customClass/>
          <description>While matched, mark the name field of the doc to readonly.</description>
        </element>
        <element position="system.xlsx,condition,114">
          <conditionId>isNotFromMultipleVendors</conditionId>
          <expression>entity.isFromMultipleVendors = null || entity.isFromMultipleVendors = false</expression>
          <customClass/>
          <description>Use to validate Vendor Field in shipment booking / shipment advice header level. False means the field is mandatory.</description>
        </element>
        <element position="system.xlsx,condition,115">
          <conditionId>isListingView</conditionId>
          <expression>entity.baseViewCategory = 'listing'</expression>
          <customClass/>
          <description>While matched, mark targetModule and targetElement  fields of the doc to readonly.</description>
        </element>
        <element position="system.xlsx,condition,116">
          <conditionId>aprvsRequiredMatchToAny</conditionId>
          <expression>entity.aprvsRequired.name='Any'</expression>
          <customClass/>
          <description>Document field aprvsRequired value is  "Any"</description>
        </element>
        <element position="system.xlsx,condition,117">
          <conditionId>statusConfirmed</conditionId>
          <expression>entity.status = 'confirmed'</expression>
          <customClass/>
          <description>True means document is in Confirmed status. False means document is not in Confirmed status.</description>
        </element>
        <element position="system.xlsx,condition,118">
          <conditionId>statusAccepted</conditionId>
          <expression>entity.status = 'accepted'</expression>
          <customClass/>
          <description>True means document is in Accepted status. False means document is not in Accepted status.</description>
        </element>
        <element position="system.xlsx,condition,119">
          <conditionId>statusReceived</conditionId>
          <expression>entity.status = 'received'</expression>
          <customClass/>
          <description>True means document is in Received status. False means document is not in Received status.</description>
        </element>
        <element position="system.xlsx,condition,120">
          <conditionId>statusRejected</conditionId>
          <expression>entity.status = 'rejected'</expression>
          <customClass/>
          <description>True means document is in Rejected status. False means document is not in Rejected status.</description>
        </element>
        <element position="system.xlsx,condition,121">
          <conditionId>isAttrKeyEqualsBookName</conditionId>
          <expression>entity.attrKey='bookName'</expression>
          <customClass/>
          <description>Check is AttrKey equals bookName for dataListTypeItemAttr.</description>
        </element>
        <element position="system.xlsx,condition,122">
          <conditionId>notCreatorAndNotPrivate</conditionId>
          <expression>entity.createUser != user.loginUser &amp;&amp; entity.isPrivate = false</expression>
          <customClass/>
          <description>Use to validate note module. edit need check creator and not is private.</description>
        </element>
        <element position="system.xlsx,condition,123">
          <conditionId>notCreatorAndIsPrivate</conditionId>
          <expression>entity.createUser != user.loginUser &amp;&amp; entity.isPrivate = true</expression>
          <customClass/>
          <description>Use to validate note module. read data need check creator and is private.</description>
        </element>
        <element position="system.xlsx,condition,124">
          <conditionId>isCreator</conditionId>
          <expression>entity.createUser = user.loginUser</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,125">
          <conditionId>notCreator</conditionId>
          <expression>entity.createUser != user.loginUser</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,126">
          <conditionId>isCheckedContacts</conditionId>
          <expression>entity.toContacts=true</expression>
          <customClass/>
          <description>Use to validate Notification profile. if document contacts field is checked, the type selection is mandatory</description>
        </element>
        <element position="system.xlsx,condition,127">
          <conditionId>isCheckedResponsibleParties</conditionId>
          <expression>entity.toResponsibleParties=true</expression>
          <customClass/>
          <description>Use to validate Notification profile. If responsible parties field is checked, the type selection is mandatory</description>
        </element>
        <element position="system.xlsx,condition,128">
          <conditionId>isHeaderHclReadOnly</conditionId>
          <expression>isHeaderHCLReadOnly(entity)</expression>
          <customClass/>
          <description>According to reference item to decide Header's hcl section whether display read only. If no any reference item exists will display editable, else display reads only.</description>
        </element>
        <element position="system.xlsx,condition,129">
          <conditionId>isHeaderClassificationReadOnly</conditionId>
          <expression>isHeaderClassificationReadOnly(entity)</expression>
          <customClass/>
          <description>According to reference item to decide Header's classification section whether display read only. If no any reference item exists will display editable, else display reads only.</description>
        </element>
        <element position="system.xlsx,condition,130">
          <conditionId>isFromSampleTracker</conditionId>
          <expression>entity.isFromSampleTracker=true</expression>
          <customClass/>
          <description>Use to open do sample request. If sample request create in sample tracker doc, then it will open as Popup.SampleRequestST.</description>
        </element>
        <element position="system.xlsx,condition,131">
          <conditionId>notFromSampleTracker</conditionId>
          <expression>entity.isFromSampleTracker =null||entity.isFromSampleTracker=false</expression>
          <customClass/>
          <description>Use to open do sample request. If sample request create in sample tracker doc, then it will open as Popup.SampleRequestST.</description>
        </element>
        <element position="system.xlsx,condition,132">
          <conditionId>matchAllClassification</conditionId>
          <expression>matchAllClassification(entity.productCategory)</expression>
          <customClass/>
          <description>For each common field, user's header classification contain ALL values of the document's header classification.</description>
        </element>
        <element position="system.xlsx,condition,133">
          <conditionId>matchAnyClassification</conditionId>
          <expression>matchAnyClassification(entity.productCategory)</expression>
          <customClass/>
          <description>For each common field, ANY values match between user's header classification and document's header classification</description>
        </element>
        <element position="system.xlsx,condition,134">
          <conditionId>isSTMatchAnyClassification</conditionId>
          <expression>entity.sampleTracker=null || matchAnyClassification(entity.sampleTracker.productCategory)</expression>
          <customClass/>
          <description>For each common field, ANY values match between user's header classification and document's referecenced sampleTracker's header classification</description>
        </element>
        <element position="system.xlsx,condition,135">
          <conditionId>isClassificationSecurityMode</conditionId>
          <expression>isClassificationSecurityMode()</expression>
          <customClass/>
          <description>The security mode is classification mode.</description>
        </element>
        <element position="system.xlsx,condition,136">
          <conditionId>isHclSecurityMode</conditionId>
          <expression>isHclSecurityMode()</expression>
          <customClass/>
          <description>The security mode is HCL mode.</description>
        </element>
        <element position="system.xlsx,condition,137">
          <conditionId>isNotEvaluateForItemSample</conditionId>
          <expression>entity.evaluationType!='EF01'</expression>
          <customClass/>
          <description>Use to hanlde the security part for sample evaluation 's fit assessment tab.Current sampl evaluation's Evaluation for != Item Sample</description>
        </element>
        <element position="system.xlsx,condition,138">
          <conditionId>isNotNewDoc</conditionId>
          <expression>entity.newEntity = false</expression>
          <customClass/>
          <description>False means document has been saved into DB</description>
        </element>
        <element position="system.xlsx,condition,139">
          <conditionId>isDataListTypeItemAvailable</conditionId>
          <expression>entity.enabled=true</expression>
          <customClass/>
          <description>Use to validate Data List Type module. If Available field is checked, the fieldLabel is mandatory</description>
        </element>
        <element position="system.xlsx,condition,140">
          <conditionId>isSpecRequirementMandatory</conditionId>
          <expression>entity.mandatory=true</expression>
          <customClass/>
          <description>If Mandatory field is checked, the Detail field should do the mandatory validation in Spec - Requirements tab - Requirements grid.</description>
        </element>
        <element position="system.xlsx,condition,141">
          <conditionId>isDefault</conditionId>
          <expression>entity.isDefault=true</expression>
          <customClass/>
          <description>Use to validate Agreement Template is default.</description>
        </element>
        <element position="system.xlsx,condition,142">
          <conditionId>isDocumentFieldsChecked</conditionId>
          <expression>entity.isDocumentFields = true</expression>
          <customClass/>
          <description>Use to validate Notification Profile. If Document Fields is checked, the Emails Fields selection is mandatory.</description>
        </element>
        <element position="system.xlsx,condition,143">
          <conditionId>isSampleEvaluationReadOnlyFromST</conditionId>
          <expression>isSampleEvaluationReadOnlyFromST(entity)</expression>
          <customClass/>
          <description>According to sample version in sampletracker whether sample evaluation display read only. If sample version is latest, sample evaluation will display editable, else display reads only.</description>
        </element>
        <element position="system.xlsx,condition,144">
          <conditionId>isWithVendorAccess</conditionId>
          <expression>entity.isVendorAccess=true</expression>
          <customClass/>
          <description>True means Vendor doc with vendor access</description>
        </element>
        <element position="system.xlsx,condition,145">
          <conditionId>isWithoutVendorAccess</conditionId>
          <expression>entity.isVendorAccess=false</expression>
          <customClass/>
          <description>True means Vendor doc without vendor access</description>
        </element>
        <element position="system.xlsx,condition,146">
          <conditionId>matchWorkingAndUserDomain</conditionId>
          <expression>entity.hubDomainId = user.workingDomainId &amp;&amp; entity.domainId = user.domainId</expression>
          <customClass/>
          <description>validate whether the current user is internal.</description>
        </element>
        <element position="system.xlsx,condition,147">
          <conditionId>isForumExternalUser</conditionId>
          <expression>entity.hubDomainId = user.workingDomainId &amp;&amp; existInForumExternalParty(entity)</expression>
          <customClass/>
          <description>validate whether the current user is external.</description>
        </element>
        <element position="system.xlsx,condition,148">
          <conditionId>notMatchWorkingDomain</conditionId>
          <expression>entity.hubDomainId != user.workingDomainId</expression>
          <customClass/>
          <description>Neither forum internal user nor forum external user.</description>
        </element>
        <element position="system.xlsx,condition,149">
          <conditionId>isHubDomainAndClassificationMode</conditionId>
          <expression>isClassificationSecurityMode() &amp;&amp; entity.domainId = entity.hubDomainId</expression>
          <customClass/>
          <description>Use to validation field in hub domain and classification security mode . Such as if domain is hub domain and the security mode is classification, the classification field should be mandatory.</description>
        </element>
        <element position="system.xlsx,condition,150">
          <conditionId>isPlannedInpectDateCondition</conditionId>
          <expression>entity.plannedInspectDate &lt; daysAfterToday(2) &amp;&amp; entity.status = 'scheduled'</expression>
          <customClass/>
          <description>Use to inspectBooking check daysAfterToday</description>
        </element>
        <element position="system.xlsx,condition,151">
          <conditionId>statusScheduled</conditionId>
          <expression>entity.status = 'scheduled'</expression>
          <customClass/>
          <description>Use to inspectBooking check workflow status equals 'scheduled'</description>
        </element>
        <element position="system.xlsx,condition,152">
          <conditionId>isInspectionFormatByLotWithPOM</conditionId>
          <expression>!(entity.isContainMeasurement=true&amp;&amp; entity.inspectFormat='ITEM')</expression>
          <customClass/>
          <description>Use to inspectReport Only display the measurement summary in header when the format is item and the item has POM</description>
        </element>
        <element position="system.xlsx,condition,153">
          <conditionId>isContainMeasurement</conditionId>
          <expression>!(entity.isContainMeasurement=true)</expression>
          <customClass/>
          <description>Use to inspectReport check inspectFormat  whether to display measurement tab</description>
        </element>
        <element position="system.xlsx,condition,154">
          <conditionId>isNotCpmForVendor</conditionId>
          <expression>!isCpmForVendor() &amp;&amp; entity.domainId != null &amp;&amp; entity.hubDomainId != null &amp;&amp; entity.domainId != entity.hubDomainId</expression>
          <customClass/>
          <description>Use to validate if aLL connected Vendor can see/edit the initialized CPM or not.if true,CPM function will be not granted to ALL connected Vendors.</description>
        </element>
        <element position="system.xlsx,condition,155">
          <conditionId>isSpectReportFail</conditionId>
          <expression>entity.inspectResult = 'FA'</expression>
          <customClass/>
          <description>Use inspec report result, if it's Fail, then fail reason is manditory.</description>
        </element>
        <element position="system.xlsx,condition,156">
          <conditionId>statusRevisionRequired</conditionId>
          <expression>entity.status = 'revisionRequired'</expression>
          <customClass/>
          <description>True means document is in RevisionRequired status. False means document is not in RevisionRequired status.</description>
        </element>
        <element position="system.xlsx,condition,157">
          <conditionId>isVq2Version</conditionId>
          <expression>isVq2Version()</expression>
          <customClass/>
          <description>Check the VQ Version, True means the current VQ version is 2</description>
        </element>
        <element position="system.xlsx,condition,158">
          <conditionId>hasShortlistedTag</conditionId>
          <expression>hasSystemTag(entity, 'Shortlisted')</expression>
          <customClass/>
          <description>Use to hide [Shortilisted] and show  [Remove from Shortlisted]  according to  this condition in VQ2 form</description>
        </element>
        <element position="system.xlsx,condition,159">
          <conditionId>notHasShortlistedTag</conditionId>
          <expression>!(hasSystemTag(entity, 'Shortlisted'))</expression>
          <customClass/>
          <description>Use to show [Shortilisted] and hide  [Remove from Shortlisted]  according to  this condition in VQ2 form</description>
        </element>
        <element position="system.xlsx,condition,160">
          <conditionId>isVq2RequirementMandatory</conditionId>
          <expression>entity.mandatory=true&amp;&amp;entity.dataType!=null&amp;&amp;entity.dataType!='Checkbox'</expression>
          <customClass/>
          <description>If Mandatory field is checked and Detail is not checkBox type,the Detail field should do the mandatory validation in Vq2 - Requirements tab - Requirements grid.</description>
        </element>
        <element position="system.xlsx,condition,161">
          <conditionId>isMultipleSourcingRecord</conditionId>
          <expression>isMultipleSourcingRecord()</expression>
          <customClass/>
          <description>use to control what should be hidden in different Sourcing Record type</description>
        </element>
        <element position="system.xlsx,condition,162">
          <conditionId>isSingleSourcingRecord</conditionId>
          <expression>isSingleSourcingRecord()</expression>
          <customClass/>
          <description>use to control what should be hidden in different Sourcing Record type</description>
        </element>
        <element position="system.xlsx,condition,163">
          <conditionId>isCpmTaskCustFieldsEnabled</conditionId>
          <expression>!(isCpmTaskCustFieldsEnabled(entity))</expression>
          <customClass/>
          <description>Check if the current cpmtask's custfields is disable, then hide the buttons [close] and [cancel]</description>
        </element>
        <element position="system.xlsx,condition,164">
          <conditionId>isVq1Version</conditionId>
          <expression>isVq1Version()</expression>
          <customClass/>
          <description>Check the VQ Version, True means the current VQ version is 1</description>
        </element>
        <element position="system.xlsx,condition,165">
          <conditionId>isVendorDeclinedNotChecked</conditionId>
          <expression>entity.vendorDeclined!=true</expression>
          <customClass/>
          <description>Check the QqItem's vendorDeclined is checked or not</description>
        </element>
        <element position="system.xlsx,condition,166">
          <conditionId>rfqRequiresOpenCosting</conditionId>
          <expression>entity.openCosting=true</expression>
          <customClass/>
          <description>Check the rfq's Costing is selected or not</description>
        </element>
        <element position="system.xlsx,condition,167">
          <conditionId>isContainChecklistTemplate</conditionId>
          <expression>entity.factoryAuditTemplate!=null</expression>
          <customClass/>
          <description>Use to inspectReport check inspectReportTemplate whether contain checlistTemplate</description>
        </element>
        <element position="system.xlsx,condition,168">
          <conditionId>isActive</conditionId>
          <expression>entity.isInactive!=true</expression>
          <customClass/>
          <description>Use to check is itemSize record active</description>
        </element>
        <element position="system.xlsx,condition,169">
          <conditionId>isVendorDomain</conditionId>
          <expression>isVendorDomain()</expression>
          <customClass/>
          <description>Use to validate current workingDomain is VendorDomain. If true, the current working domain is vendor Domain.</description>
        </element>
        <element position="system.xlsx,condition,170">
          <conditionId>isAllowManuallyOverwriteMR</conditionId>
          <expression>entity.isShowMeasurementTab=true &amp;&amp; entity.isContainMeasurement=true &amp;&amp; entity.isCountMeasIntoDefect=false &amp;&amp; entity.isAllowManuallyOverwriteMR=true</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,171">
          <conditionId>isNotApprovalInPending</conditionId>
          <expression>isNotApprovalInPending(entity)</expression>
          <customClass/>
          <description>if approval stage is not allow update, or user is not current stage approver, then will not allow to edit doc.</description>
        </element>
        <element position="system.xlsx,condition,172">
          <conditionId>isImportEntity</conditionId>
          <expression>isImport(entity)=true</expression>
          <customClass/>
          <description>if isImportEntity=true mean the entity is conveter from import raw data excel</description>
        </element>
        <element position="system.xlsx,condition,173">
          <conditionId>existsNewerSample</conditionId>
          <expression>existsNewerSample(entity)</expression>
          <customClass/>
          <description>if the current document is not a sample evaluation then return false, else if exist another sample evaluation that has the same sample ID and sample version = current sample version + 1, then return true</description>
        </element>
        <element position="system.xlsx,condition,174">
          <conditionId>isItemOrder</conditionId>
          <expression>entity.orderType.code = 'ITEM_ORDER'</expression>
          <customClass/>
          <description>Use to indicate VPO/MPO document is solo itemOrder or a materialOrder of VPO/MPO. True: means VPO/MPO document is a itemOrder of VPO/MPO. False: means VPO/MPO document is solo materialOrder.</description>
        </element>
        <element position="system.xlsx,condition,175">
          <conditionId>isMaterialOrder</conditionId>
          <expression>entity.orderType.code = 'MATERIAL_ORDER'</expression>
          <customClass/>
          <description>Use to indicate VPO/MPO document is solo materialOrder or a materialOrder of VPO/MPO. True: means VPO/MPO document is a materialOrder of VPO/MPO. False: means VPO/MPO document is solo itemOrder.</description>
        </element>
        <element position="system.xlsx,condition,176">
          <conditionId>isImportOrApiMode</conditionId>
          <expression>isApiMode() || isImport(entity)=true</expression>
          <customClass/>
          <description>Check if document is create or save from rest api Or document is import from raw data excel.</description>
        </element>
        <element position="system.xlsx,condition,177">
          <conditionId>isSelectPomTemplate</conditionId>
          <expression>entity.templateName !=null &amp;&amp; entity.templateName !=''</expression>
          <customClass/>
          <description>when template is selected in POM tab (POM tab is enable), Measurement Unit will change from optional fields to Mandatory fields, otherwise keep as optional </description>
        </element>
        <element position="system.xlsx,condition,178">
          <conditionId>isFirstChildLevel</conditionId>
          <expression>entity!=null&amp;&amp;entity.levelType='firstChildLevel'</expression>
          <customClass/>
          <description>Check whether comparison temple section level is first child level.</description>
        </element>
        <element position="system.xlsx,condition,179">
          <conditionId>isActiveContractFileOfVendorAgreement</conditionId>
          <expression>entity.contractFile!=null &amp;&amp; entity.contractFile.docStatus != 'inactive'</expression>
          <customClass/>
          <description>Check the vendor agreement entity whether it is active.</description>
        </element>
        <element position="system.xlsx,condition,180">
          <conditionId>sourcingRecordForItemDelivery</conditionId>
          <expression>isMultipleSourcingRecord() &amp;&amp; entity.isDefaultItem=false</expression>
          <customClass/>
          <description>In multiple sourcing mode, for some project, may specify other conditions such as workflow status of the sourcing record matching specific value.</description>
        </element>
        <element position="system.xlsx,condition,181">
          <conditionId>isComplianceHasFactory</conditionId>
          <expression>entity.fact!=null</expression>
          <customClass/>
          <description>is Compliance has factory</description>
        </element>
        <element position="system.xlsx,condition,182">
          <conditionId>isComplianceRespondedByAuditor</conditionId>
          <expression>entity.respondedBy='AUDITOR'</expression>
          <customClass/>
          <description>Is compliance responded by is AUDITOR</description>
        </element>
        <element position="system.xlsx,condition,183">
          <conditionId>isComplianceSubTypeBookNameNotEmpty</conditionId>
          <expression>entity.subTypeBookName != null</expression>
          <customClass/>
          <description>is Compliance subTypeBookName is not empty</description>
        </element>
        <element position="system.xlsx,condition,184">
          <conditionId>isComplianceActivityStatusResponded</conditionId>
          <expression>entity.status='Responded'</expression>
          <customClass/>
          <description>is Compliance Activity status is Responsed</description>
        </element>
        <element position="system.xlsx,condition,185">
          <conditionId>isComplianceExpiryDateMandatory</conditionId>
          <expression>entity.ed=true &amp;&amp; entity.status='Requested'</expression>
          <customClass/>
          <description>is Compliance Activity  expiryDate mandatory : ed is ticked and status is Requested</description>
        </element>
        <element position="system.xlsx,condition,186">
          <conditionId>isComplianceRejectReasonMandatory</conditionId>
          <expression>entity.approvedRejected='REJECTED' &amp;&amp; entity.status='Responded'</expression>
          <customClass/>
          <description>is Compliance Activity  reject reason mandatory : approvedRejected is REJECTED and status is Responded</description>
        </element>
        <element position="system.xlsx,condition,187">
          <conditionId>isComplianceReviewRemarksMandatory</conditionId>
          <expression>entity.remarksMandatory='YES' &amp;&amp; entity.status='Responded'</expression>
          <customClass/>
          <description>is Compliance Activity  reviewRemarks mandatory : remarksMandatory is YES and status is Responded</description>
        </element>
        <element position="system.xlsx,condition,188">
          <conditionId>isComplianceNotificationActived</conditionId>
          <expression>entity.active=true</expression>
          <customClass/>
          <description>is Compliance Notification active is ticked</description>
        </element>
        <element position="system.xlsx,condition,189">
          <conditionId>isSampleEvaluationEnabled</conditionId>
          <expression>isSampleEvaluationEnabled()</expression>
          <customClass/>
          <description>use to control if want to use SampleEvaluation module</description>
        </element>
        <element position="system.xlsx,condition,190">
          <conditionId>isSampleEvaluationDisabled</conditionId>
          <expression>!isSampleEvaluationEnabled()</expression>
          <customClass/>
          <description>use to control if want to use SampleEvaluation module</description>
        </element>
        <element position="system.xlsx,condition,191">
          <conditionId>defaultValueTypeIsNotAdd</conditionId>
          <expression>null = entity.?valueType || !('add' = entity.?valueType)</expression>
          <customClass/>
          <description>use to control if type is Add then change to Field column as optional mandatory under default profile template</description>
        </element>
        <element position="system.xlsx,condition,192">
          <conditionId>isFactSyncByVendor</conditionId>
          <expression>entity.syncByVendor=true</expression>
          <customClass/>
          <description>is Factory Sync By Vendor</description>
        </element>
        <element position="system.xlsx,condition,193">
          <conditionId>isNeedToValidateVqUnitCost</conditionId>
          <expression>entity.openCosting=false &amp;&amp; entity.itemType.code = 'SET'</expression>
          <customClass/>
          <description>use to validate quotation 1.0 unit cost</description>
        </element>
        <element position="system.xlsx,condition,194">
          <conditionId>isVqUnitCostNeedMandatory</conditionId>
          <expression>!(entity.openCosting=false &amp;&amp; entity.itemType.code = 'SET')</expression>
          <customClass/>
          <description>use to validate quotation 1.0 unit cost Mandatory Field</description>
        </element>
        <element position="system.xlsx,condition,195">
          <conditionId>statusProspect</conditionId>
          <expression>entity.status = 'prospect'</expression>
          <customClass/>
          <description>True means document is in Prospect status. False means document is not in Prospect status.</description>
        </element>
        <element position="system.xlsx,condition,196">
          <conditionId>isWithoutVendorAccessForQq</conditionId>
          <expression>entity.vendor.isVendorAccess=false</expression>
          <customClass/>
          <description>True means Vendor doc without vendor access</description>
        </element>
        <element position="system.xlsx,condition,197">
          <conditionId>isMaterialItem</conditionId>
          <expression>entity.itemType.code = 'MATERIAL'</expression>
          <customClass/>
          <description>Use to indicate item document is solo item or a set of item. True: means item document is a material of item. False: means item document is solo item.</description>
        </element>
        <element position="system.xlsx,condition,198">
          <conditionId>isNotVendorDomain</conditionId>
          <expression>isNotVendorDomain()</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,199">
          <conditionId>isNotOpenCosting</conditionId>
          <expression>entity.openCosting=false</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,200">
          <conditionId>isVendorRelationshipType</conditionId>
          <expression>entity.relationshipType = 'VENDOR'</expression>
          <customClass/>
          <description>is the relationship type is 'Vendor'</description>
        </element>
        <element position="system.xlsx,condition,201">
          <conditionId>isFactRelationshipType</conditionId>
          <expression>entity.relationshipType = 'FACTORY'</expression>
          <customClass/>
          <description>is the relationship type is 'Factory'</description>
        </element>
        <element position="system.xlsx,condition,202">
          <conditionId>isWithFactAccess</conditionId>
          <expression>entity.isFactAccess=true</expression>
          <customClass/>
          <description>True means Factory doc with factory access</description>
        </element>
        <element position="system.xlsx,condition,203">
          <conditionId>isWithoutFactAccess</conditionId>
          <expression>entity.isFactAccess=false</expression>
          <customClass/>
          <description>True means Factory doc without factory access</description>
        </element>
        <element position="system.xlsx,condition,204">
          <conditionId>isModifiedByExternal</conditionId>
          <expression>isModifiedByExternal(entity)</expression>
          <customClass/>
          <description>True means document last modified by external</description>
        </element>
        <element position="system.xlsx,condition,205">
          <conditionId>isModifiedByInternal</conditionId>
          <expression>isModifiedByInternal(entity)</expression>
          <customClass/>
          <description>True means document last modified by internal</description>
        </element>
        <element position="system.xlsx,condition,206">
          <conditionId>isModifiedByVendor</conditionId>
          <expression>isModifiedByVendor(entity)</expression>
          <customClass/>
          <description>True means document last modified by vendor</description>
        </element>
        <element position="system.xlsx,condition,207">
          <conditionId>isModifiedByFactory</conditionId>
          <expression>isModifiedByFactory(entity)</expression>
          <customClass/>
          <description>True means document last modified by factory</description>
        </element>
        <element position="system.xlsx,condition,208">
          <conditionId>isUserCompanyReadOnly</conditionId>
          <expression>entity.isNewEntity() = false &amp;&amp; entity.accountType.code = 'VENDOR'</expression>
          <customClass/>
          <description>True means the user company should be readonly</description>
        </element>
        <element position="system.xlsx,condition,209">
          <conditionId>isVendorAccountType</conditionId>
          <expression>entity.accountType.code = 'VENDOR'</expression>
          <customClass/>
          <description>True means the user account type is Vendor Portal</description>
        </element>
        <element position="system.xlsx,condition,210">
          <conditionId>isVendorOrFactDomain</conditionId>
          <expression>isExternalDomain()</expression>
          <customClass/>
          <description>True means is Vendor Portal</description>
        </element>
        <element position="system.xlsx,condition,211">
          <conditionId>isInspectionFormatByItem</conditionId>
          <expression>entity.inspectFormat?.code='ITEM'</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,212">
          <conditionId>isInspectionFormatByLot</conditionId>
          <expression>entity.inspectFormat?.code='LOT'</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,213">
          <conditionId>statusReleasedToVendorOrOfficial</conditionId>
          <expression>entity.status = "official" || entity.status = "releasedToVendor"</expression>
          <customClass/>
          <description>True means document is in Released To Vendor or Official. False means document is not in Released To Vendor or Official.</description>
        </element>
        <element position="system.xlsx,condition,214">
          <conditionId>statusVendorChangeProposed</conditionId>
          <expression>entity.status = 'vendorChangeProposed'</expression>
          <customClass/>
          <description>True means document is in Vendo rChange Proposed. False means document is not in Vendor Change Proposed.</description>
        </element>
        <element position="system.xlsx,condition,215">
          <conditionId>statusBuyerConfirmed</conditionId>
          <expression>entity.status = 'buyerConfirmed'</expression>
          <customClass/>
          <description>True means document is in Buyer Confirmed status. False means document is not in Buyer Confirmed status.</description>
        </element>
        <element position="system.xlsx,condition,216">
          <conditionId>statusBuyerRejected</conditionId>
          <expression>entity.status = 'buyerRejected'</expression>
          <customClass/>
          <description>True means document is in Buyer Rejected status. False means document is not in Buyer Rejected status.</description>
        </element>
        <element position="system.xlsx,condition,217">
          <conditionId>isUserNotOneOfAuditors</conditionId>
          <expression>!isUserOneOfAuditors(entity)</expression>
          <customClass/>
          <description>True means docunment auditors not contains current user</description>
        </element>
        <element position="system.xlsx,condition,218">
          <conditionId>isCapAuditorNotElevate</conditionId>
          <expression>entity.factAudit.auditor.code != 'ELEVATE'</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,219">
          <conditionId>isVendorCountryChina</conditionId>
          <expression>entity.country.code='CN'</expression>
          <customClass/>
          <description>True means the country of vendor is China</description>
        </element>
        <element position="system.xlsx,condition,220">
          <conditionId>isConcurrentEditMode</conditionId>
          <expression>isConcurrentEditMode(entity)</expression>
          <customClass/>
          <description>ConcurrentEdit mode enable in current Entity</description>
        </element>
        <element position="system.xlsx,condition,221">
          <conditionId>isNotConcurrentEditMode</conditionId>
          <expression>!isConcurrentEditMode(entity)</expression>
          <customClass/>
          <description>ConcurrentEdit mode disable in current Entity</description>
        </element>
        <element position="system.xlsx,condition,222">
          <conditionId>isConcurrentEditModeAndNewDoc</conditionId>
          <expression>isConcurrentEditModeAndNewDoc(entity)</expression>
          <customClass/>
          <description>ConcurrentEdit mode enable and NewDoc entity</description>
        </element>
        <element position="system.xlsx,condition,223">
          <conditionId>isConcurrentEditModeAndExistDocument</conditionId>
          <expression>isConcurrentEditModeAndExistDocument(entity)</expression>
          <customClass/>
          <description>ConcurrentEdit mode enable and exist entity</description>
        </element>
        <element position="system.xlsx,condition,224">
          <conditionId>isShipmentSubscriptionActivated</conditionId>
          <expression>isShipmentSubscriptionActivated()</expression>
          <customClass/>
          <description>use to control if want to show Shipment status field when enable shipment Subscription</description>
        </element>
        <element position="system.xlsx,condition,225">
          <conditionId>isShipmentSubscriptionInactivated</conditionId>
          <expression>!isShipmentSubscriptionActivated()</expression>
          <customClass/>
          <description>use to control if want to show Shipment status field when disable shipment Subscription</description>
        </element>
        <element position="system.xlsx,condition,226">
          <conditionId>isAllVpoItemLatest</conditionId>
          <expression>isAllVpoItemLatest(entity)</expression>
          <customClass/>
          <description>True means all the vpo items are latest</description>
        </element>
        <element position="system.xlsx,condition,227">
          <conditionId>notIsRFSOrSRAndisTrueSRKLRequired</conditionId>
          <expression>!(entity.?lhkSRKLRequired = true &amp;&amp; (entity.?lhkRequestMode.code = 'RFS' || entity.?lhkRequestMode.code = 'SRHG' || entity.?lhkRequestMode.code = 'SRTX'))</expression>
          <customClass/>
          <description>not request mode is (RFS or SR) and Sample Request KL required istrue</description>
        </element>
        <element position="system.xlsx,condition,228">
          <conditionId>notIsNullGermanyRFSNoAndIsRFSOrSR</conditionId>
          <expression>!(entity.?lhkGerRFSNo != null &amp;&amp; (entity.?lhkRequestMode.code = 'RFS' || entity.?lhkRequestMode.code = 'SRHG' || entity.?lhkRequestMode.code = 'SRTX'))</expression>
          <customClass/>
          <description>not request mode is RFS or request mode is SR and “Germany RFS No:“ is not empty</description>
        </element>
        <element position="system.xlsx,condition,229">
          <conditionId>isSR</conditionId>
          <expression>entity.?lhkRequestMode.code = 'SRHG' || entity.?lhkRequestMode.code = 'SRTX'</expression>
          <customClass/>
          <description>Request Mode is SRHG OR SRTX</description>
        </element>
        <element position="system.xlsx,condition,230">
          <conditionId>isNotSR</conditionId>
          <expression>entity.?lhkRequestMode.code != 'SRHG' &amp;&amp; entity.?lhkRequestMode.code != 'SRTX'</expression>
          <customClass/>
          <description>Request Mode is NOT SRHG AND SRTX</description>
        </element>
        <element position="system.xlsx,condition,231">
          <conditionId>proposalTypeIsRFSAndLastItemNotRFSOrSR</conditionId>
          <expression>(entity.lhkType = 'RFSHG' || entity.lhkType = 'RFSTX') &amp;&amp; (getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'RFS' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRHG' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRTX')</expression>
          <customClass/>
          <description>The proposal doc type is like RFSHG or RFSTX.and Item requestMode is RFS or SR</description>
        </element>
        <element position="system.xlsx,condition,232">
          <conditionId>proposalTypeIsSRAndLastItemNotRFSOrSR</conditionId>
          <expression>(entity.lhkType = 'SRHG' || entity.lhkType = 'SRTX') &amp;&amp; (getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'RFS' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRHG' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRTX')</expression>
          <customClass/>
          <description>The proposal doc type is like SRHG or SRTX.and Item requestMode is RFS or SR</description>
        </element>
        <element position="system.xlsx,condition,233">
          <conditionId>feedbackTypeIsRFSAndLastItemNotRFSOrSR</conditionId>
          <expression>(entity.vqRequestMode = 'RFSHG' || entity.vqRequestMode = 'RFSTX') &amp;&amp; (getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'RFS' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRHG' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRTX')</expression>
          <customClass/>
          <description>The vqRequestMode is like RFSHG or RFSTX.and Item requestMode is RFS or SR</description>
        </element>
        <element position="system.xlsx,condition,234">
          <conditionId>feedbackTypeIsSRAndLastItemNotRFSOrSR</conditionId>
          <expression>(entity.vqRequestMode = 'SRHG' || entity.vqRequestMode = 'SRTX') &amp;&amp; (getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'RFS' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRHG' &amp;&amp; getLatestItemEntityCodelistFieldValue(entity, "lhkRequestMode") != 'SRTX')</expression>
          <customClass/>
          <description>The vqRequestMode is like SRHG or SRTX.and Item requestMode is RFS or SR</description>
        </element>
        <element position="system.xlsx,condition,235">
          <conditionId>isNewAndIsNotSR</conditionId>
          <expression>entity.status = 'customStatus11' &amp;&amp; entity.lhkType != 'SRTX'</expression>
          <customClass/>
          <description>status is new and lhk_Type is not SRTX</description>
        </element>
        <element position="system.xlsx,condition,236">
          <conditionId>proposalIsNotFullSR</conditionId>
          <expression>(entity.lhkType = 'SRHG' || entity.lhkType = 'SRTX') &amp;&amp; entity.lhkFullSR = false</expression>
          <customClass/>
          <description>Document is not full SR</description>
        </element>
        <element position="system.xlsx,condition,237">
          <conditionId>proposalIsSR</conditionId>
          <expression>entity.lhkType = 'SRHG' || entity.lhkType = 'SRTX'</expression>
          <customClass/>
          <description>Proposal Type is SR</description>
        </element>
        <element position="system.xlsx,condition,238">
          <conditionId>proposalIsNotSR</conditionId>
          <expression>entity.lhkType != 'SRHG' &amp;&amp; entity.lhkType != 'SRTX'</expression>
          <customClass/>
          <description>Proposal Type is not SR</description>
        </element>
        <element position="system.xlsx,condition,239">
          <conditionId>proposalIsFullSRAndRetailPackagingIsNot47</conditionId>
          <expression>entity.lhkFullSR = true &amp;&amp; entity.?ldlProductPackagingKL.code != 'RETAIL_PACKAGING_47'</expression>
          <customClass/>
          <description>Proposal Type full SR and ProductPackagingKL is not   "RETAIL_PACKAGING_47"</description>
        </element>
        <element position="system.xlsx,condition,240">
          <conditionId>proposalIsFullSR</conditionId>
          <expression>entity.lhkFullSR = true</expression>
          <customClass/>
          <description>Document is fullSR</description>
        </element>
        <element position="system.xlsx,condition,241">
          <conditionId>proposalProductPkgKLIs47</conditionId>
          <expression>entity.?ldlProductPackagingKL.code = 'RETAIL_PACKAGING_47'</expression>
          <customClass/>
          <description>ProductPackaging is not   "RETAIL_PACKAGING_47"</description>
        </element>
        <element position="system.xlsx,condition,242">
          <conditionId>proposalIsRFSHG</conditionId>
          <expression>entity.lhkType = 'RFSHG'</expression>
          <customClass/>
          <description>Proposal Type is RFSHG and Product Category belongs to HG</description>
        </element>
        <element position="system.xlsx,condition,243">
          <conditionId>isSelectionLt2304</conditionId>
          <expression>entity.?ldlLotNo &lt; 2304 || entity.?itemLotNo &lt; 2304</expression>
          <customClass/>
          <description>Selection &lt; 2304</description>
        </element>
        <element position="system.xlsx,condition,244">
          <conditionId>feedbackProposalIsNewAndIsNotSR</conditionId>
          <expression>entity.?vq.status = 'customStatus11' &amp;&amp; entity.?vq.lhkType != 'SRTX'</expression>
          <customClass/>
          <description>status is new and lhk_Type is not SRTX</description>
        </element>
        <element position="system.xlsx,condition,245">
          <conditionId>isAutomatedTestResultType</conditionId>
          <expression>entity.testResultType != null &amp;&amp; entity.testResultType.code = 'AUTOMATED'</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,246">
          <conditionId>isFactoryCompanyType</conditionId>
          <expression>entity.companyType = 'Factory'</expression>
          <customClass/>
          <description>True means company type is Factory. False means company type is not Factory.</description>
        </element>
        <element position="system.xlsx,condition,247">
          <conditionId>isVendorCompanyType</conditionId>
          <expression>entity.companyType = 'Vendor'</expression>
          <customClass/>
          <description>True means company type is Vendor. False means company type is not Vendor.</description>
        </element>
        <element position="system.xlsx,condition,248">
          <conditionId>is3rdPartyTesters</conditionId>
          <expression>isUserContainsGroup('3rd Party Testers')</expression>
          <customClass/>
          <description/>
        </element>
        <element position="system.xlsx,condition,249">
          <conditionId>statusSampleSubmitted</conditionId>
          <expression>entity.status = 'sampleSubmitted'</expression>
          <customClass/>
          <description>True means document is in Sample Submitted status. False means document is not in Sample Submitted status.</description>
        </element>
        <element position="system.xlsx,condition,250">
          <conditionId>statusSampleReceived</conditionId>
          <expression>entity.status = 'sampleReceived'</expression>
          <customClass/>
          <description>True means document is in Sample Received status. False means document is not in Sample Received status.</description>
        </element>
        <element position="system.xlsx,condition,251">
          <conditionId>statusTestCompleted</conditionId>
          <expression>entity.status = 'testCompleted'</expression>
          <customClass/>
          <description>True means document is in Test Completed status. False means document is not in Test Completed status.</description>
        </element>
        <element position="system.xlsx,condition,252">
          <conditionId>statusReviewed</conditionId>
          <expression>entity.status = 'reviewed'</expression>
          <customClass/>
          <description>True means document is in Reviewed status. False means document is not in Reviewed status.</description>
        </element>
      </elements>
    </Conditions>
  </sheet>
  <sheet id="role" position="system.xlsx,role">
    <Role position="system.xlsx,role,1">
      <elements id="default">
        <element position="system.xlsx,role,4">
          <roleId>$DEFAULT_ROLE</roleId>
          <description>Default Role</description>
        </element>
        <element position="system.xlsx,role,5">
          <roleId>SuperAdministratorRole</roleId>
          <description>Super Administrator Role</description>
        </element>
        <element position="system.xlsx,role,6">
          <roleId>GeneralAdministratorRole</roleId>
          <description>General Administrator Role</description>
        </element>
        <element position="system.xlsx,role,7">
          <roleId>ClientAdministratorRole</roleId>
          <description>Client Administrator Role</description>
        </element>
        <element position="system.xlsx,role,8">
          <roleId>vendors</roleId>
          <description>Vendor (for external access)</description>
        </element>
        <element position="system.xlsx,role,9">
          <roleId>facts</roleId>
          <description>Factory (for external access)</description>
        </element>
        <element position="system.xlsx,role,10">
          <roleId>custs</roleId>
          <description>Customer (for external access)</description>
        </element>
        <element position="system.xlsx,role,11">
          <roleId>forwarders</roleId>
          <description>Forwarder (for external access)</description>
        </element>
        <element position="system.xlsx,role,12">
          <roleId>CommonFunctionsRole</roleId>
          <description>Role for accessing common system functions</description>
        </element>
        <element position="system.xlsx,role,13">
          <roleId>cpmAdmin</roleId>
          <description>Role with administrative rights for all CPM milestones</description>
        </element>
      </elements>
    </Role>
  </sheet>
  <sheet id="role.bak" position="system.xlsx,role.bak">
    <Role position="system.xlsx,role.bak,1">
      <elements id="default">
        <element position="system.xlsx,role.bak,4">
          <roleId>$DEFAULT_ROLE</roleId>
          <description>Default Role</description>
        </element>
        <element position="system.xlsx,role.bak,5">
          <roleId>ADMIN</roleId>
          <description>Administrator</description>
        </element>
        <element position="system.xlsx,role.bak,6">
          <roleId>MERCHANT</roleId>
          <description>Merchant</description>
        </element>
        <element position="system.xlsx,role.bak,7">
          <roleId>VENDOR</roleId>
          <description>Vendor</description>
        </element>
        <element position="system.xlsx,role.bak,8">
          <roleId>QA</roleId>
          <description>QA</description>
        </element>
        <element position="system.xlsx,role.bak,9">
          <roleId>vendors</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,10">
          <roleId>facts</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,11">
          <roleId>custs</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,12">
          <roleId>forwarders</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,13">
          <roleId>BUYER</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,14">
          <roleId>PLANNER</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,15">
          <roleId>PRODEV</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,16">
          <roleId>DESIG</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,17">
          <roleId>TDESIG</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,18">
          <roleId>ADESIG</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,19">
          <roleId>SMER</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,20">
          <roleId>MER</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,21">
          <roleId>INSP</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,22">
          <roleId>FIN</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,23">
          <roleId>SYS</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,24">
          <roleId>DOMS</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,25">
          <roleId>DOMA</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,26">
          <roleId>LOGIS</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,27">
          <roleId>MD</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,28">
          <roleId>CATEGORY_MANAGER</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,29">
          <roleId>KEY_ACCOUNTANT</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,30">
          <roleId>Administrators</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,31">
          <roleId>Managers</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,32">
          <roleId>Users</roleId>
          <description/>
        </element>
        <element position="system.xlsx,role.bak,33">
          <roleId>READONLY_ROLE</roleId>
          <description>READONLY_ROLE</description>
        </element>
      </elements>
    </Role>
  </sheet>
  <sheet id="group" position="system.xlsx,group">
    <Group position="system.xlsx,group,1">
      <elements id="default">
        <element position="system.xlsx,group,4">
          <group>$DEFAULT_GROUP</group>
          <groupDescription>Deafult Group</groupDescription>
        </element>
        <element position="system.xlsx,group,5">
          <group>CommonFunctionsGroup</group>
          <groupDescription>Group for accessing common system functions</groupDescription>
        </element>
        <element position="system.xlsx,group,6">
          <group>SuperAdministratorGroup</group>
          <groupDescription>Super Administrator Group</groupDescription>
        </element>
        <element position="system.xlsx,group,7">
          <group>GeneralAdministratorGroup</group>
          <groupDescription>General Administrator Group</groupDescription>
        </element>
        <element position="system.xlsx,group,8">
          <group>ClientAdministratorGroup</group>
          <groupDescription>Client Administrator Group</groupDescription>
        </element>
        <element position="system.xlsx,group,9">
          <group>Buyer</group>
          <groupDescription>Buyers</groupDescription>
        </element>
        <element position="system.xlsx,group,10">
          <group>Planner</group>
          <groupDescription>Planners</groupDescription>
        </element>
        <element position="system.xlsx,group,11">
          <group>ProductDeveloper</group>
          <groupDescription>Product Developers</groupDescription>
        </element>
        <element position="system.xlsx,group,12">
          <group>Merchandiser</group>
          <groupDescription>Merchandisers</groupDescription>
        </element>
        <element position="system.xlsx,group,13">
          <group>SeniorMerchandiser</group>
          <groupDescription>Senior Merchandisers</groupDescription>
        </element>
        <element position="system.xlsx,group,14">
          <group>CategoryManager</group>
          <groupDescription>Category Managers</groupDescription>
        </element>
        <element position="system.xlsx,group,15">
          <group>DesignStylist</group>
          <groupDescription>Design Stylists</groupDescription>
        </element>
        <element position="system.xlsx,group,16">
          <group>TechnicalDesigner</group>
          <groupDescription>Technical Designers</groupDescription>
        </element>
        <element position="system.xlsx,group,17">
          <group>ArtworkDesigner</group>
          <groupDescription>Artwork Designers</groupDescription>
        </element>
        <element position="system.xlsx,group,18">
          <group>QA</group>
          <groupDescription>Quality Assurance</groupDescription>
        </element>
        <element position="system.xlsx,group,19">
          <group>Inspector</group>
          <groupDescription>Inspectors</groupDescription>
        </element>
        <element position="system.xlsx,group,20">
          <group>Finance</group>
          <groupDescription>Finance Team</groupDescription>
        </element>
        <element position="system.xlsx,group,21">
          <group>Logistics</group>
          <groupDescription>Logistics Team</groupDescription>
        </element>
        <element position="system.xlsx,group,22">
          <group>Manager</group>
          <groupDescription>Managers</groupDescription>
        </element>
        <element position="system.xlsx,group,23">
          <group>KeyAccount</group>
          <groupDescription>Key Accountants</groupDescription>
        </element>
        <element position="system.xlsx,group,24">
          <group>vendors</group>
          <groupDescription>Vendor (external access only)</groupDescription>
        </element>
        <element position="system.xlsx,group,25">
          <group>facts</group>
          <groupDescription>Factory (external access only)</groupDescription>
        </element>
        <element position="system.xlsx,group,26">
          <group>custs</group>
          <groupDescription>Customer (external access only)</groupDescription>
        </element>
        <element position="system.xlsx,group,27">
          <group>forwarders</group>
          <groupDescription>Vendor (external access only)</groupDescription>
        </element>
        <element position="system.xlsx,group,28">
          <group>EDITOR</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group,29">
          <group>READER</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group,30">
          <group>Inspectors</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group,31">
          <group>Inspection Notify Group</group>
          <groupDescription>Inspection Notify Group</groupDescription>
        </element>
        <element position="system.xlsx,group,32">
          <group>Auditors</group>
          <groupDescription>Auditors</groupDescription>
        </element>
      </elements>
    </Group>
  </sheet>
  <sheet id="group.bak" position="system.xlsx,group.bak">
    <Group position="system.xlsx,group.bak,1">
      <elements id="default">
        <element position="system.xlsx,group.bak,4">
          <group>$DEFAULT_GROUP</group>
          <groupDescription>Deafult Group</groupDescription>
        </element>
        <element position="system.xlsx,group.bak,5">
          <group>ADMINISTRATORS</group>
          <groupDescription>Admin Group</groupDescription>
        </element>
        <element position="system.xlsx,group.bak,6">
          <group>MANAGERS</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,7">
          <group>USERS</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,8">
          <group>BUYER_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,9">
          <group>PLANNER_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,10">
          <group>PRODEV_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,11">
          <group>DESIG_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,12">
          <group>TDESIG_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,13">
          <group>ADESIG_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,14">
          <group>QA_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,15">
          <group>SMER_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,16">
          <group>MER_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,17">
          <group>INSP_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,18">
          <group>FIN_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,19">
          <group>SYS_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,20">
          <group>DOMS_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,21">
          <group>DOMA_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,22">
          <group>LOGIS_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,23">
          <group>MD_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,24">
          <group>GAL_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,25">
          <group>MAL_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,26">
          <group>CATEGORY_MANAGER_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,27">
          <group>KEY_ACCOUNTANT_GRP</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,28">
          <group>vendors</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,29">
          <group>facts</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,30">
          <group>custs</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,31">
          <group>forwarders</group>
          <groupDescription/>
        </element>
        <element position="system.xlsx,group.bak,32">
          <group>READONLY_GRP</group>
          <groupDescription>READONLY_GRP</groupDescription>
        </element>
      </elements>
    </Group>
  </sheet>
  <sheet id="auditTemplate" position="system.xlsx,auditTemplate">
    <Action position="system.xlsx,auditTemplate,1">
      <elements id="default"/>
    </Action>
  </sheet>
  <sheet id="ui_action" position="system.xlsx,ui_action">
    <UIAction position="system.xlsx,ui_action,1">
      <elements id="default"/>
    </UIAction>
  </sheet>
  <sheet id="navi" position="system.xlsx,navi">
    <NaviModule position="system.xlsx,navi,1">
      <elements id="default">
        <element position="system.xlsx,navi,4">
          <id>home</id>
          <label>Home</label>
          <action/>
          <actionParams/>
          <isDefault>TRUE</isDefault>
          <dashboardLabel/>
          <showInDashboard>0</showInDashboard>
          <modules>all,cpm,aprvProfile,notification,forum,externalDocument,shareFile</modules>
        </element>
        <element position="system.xlsx,navi,5">
          <id>plan</id>
          <label>Plans</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Plans</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>program,project,offersheet</modules>
        </element>
        <element position="system.xlsx,navi,6">
          <id>design</id>
          <label>Design</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Design</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>item,labelProfile,spec,color,component,project,colorPalette,patternPalette,materialPalette,packaging,packagingArtwork,pattern,artwork,artworkPalette</modules>
        </element>
        <element position="system.xlsx,navi,7">
          <id>product</id>
          <label>Products</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Products</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>item,catalog,project,color,component,labelProfile,artwork,artworkPalette,colorPalette,patternPalette,materialPalette,packaging,packagingArtwork,ingredient</modules>
        </element>
        <element position="system.xlsx,navi,8">
          <id>sourcing</id>
          <label>Sourcing</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Sourcing</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>vq,rfq,rfs,project,program,rfi,vq2,sourcingRecord,qq</modules>
        </element>
        <element position="system.xlsx,navi,9">
          <id>order</id>
          <label>Orders</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Orders</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>offersheet,mpo,cpo,cso,vpo,vpoAck,project,packingList</modules>
        </element>
        <element position="system.xlsx,navi,10">
          <id>compliance</id>
          <label>Compliance</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Compliance</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>vendorCompliance,factoryCompliance,productCompliance,shipmentCompliance</modules>
        </element>
        <element position="system.xlsx,navi,11">
          <id>quality</id>
          <label>Quality</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Quality</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>factAudit,inspectcheck,qc,sampleRequest,sampleTracker,sampleEvaluation,correctiveActionPlans,inspectBooking,inspectReport,testAccreditation,testReport</modules>
        </element>
        <element position="system.xlsx,navi,12">
          <id>logistics</id>
          <label>Logistics</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Logistics</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>shipmentBooking,shipmentAdvice</modules>
        </element>
        <element position="system.xlsx,navi,13">
          <id>finance</id>
          <label>Finance</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Finance</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>custInv,vendorInvoice,letterOfCredit,claim</modules>
        </element>
        <element position="system.xlsx,navi,14">
          <id>master</id>
          <label>Masters</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Masters</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>fact,vendor,qcchecklisttemplate,cust,cpmTempl,factoryAuditTemplate,aprvTempl,reqTempl,costTempl,sizeTemplate,forwarder,measurementTemplate,materialRequestTemplate,sampleRequestTemplate,documentRequestTemplate,evaluationTemplate,vendorChargesTemplate,materialAttributeTemplate,compTempl,agreementTemplate,contractFile,inspectReportTemplate,complianceTemplate,openCostingTemplate</modules>
        </element>
        <element position="system.xlsx,navi,15">
          <id>setup</id>
          <label>Admin</label>
          <action/>
          <actionParams/>
          <isDefault/>
          <dashboardLabel>Admin</dashboardLabel>
          <showInDashboard>1</showInDashboard>
          <modules>accessObject,hcl,cpmTaskTempl,hclType,trigger,domain,cbxSample,user,condition,notificationProfile,custFieldDef,dataListType,lookup,codelist,role,group,defaultProfile,systemFile,personalizeView,printFormExportTemplate,viewAdministration,externalAccessRequest,partyTemplate,entityConfig,validationConfig,invitationRequest,homepageTemplate,dataAbstractConfig</modules>
        </element>
      </elements>
    </NaviModule>
    <NaviEntry position="system.xlsx,navi,18">
      <elements id="default">
        <element position="system.xlsx,navi,21">
          <id>itemGroup</id>
          <label>Items</label>
          <naviModuleId>product</naviModuleId>
          <type>EntryGroup</type>
          <entryGroup/>
          <action/>
          <actionParams/>
          <isDefault/>
          <viewName/>
          <dashboardLabel/>
          <showInDashboard>0</showInDashboard>
          <dashboardSeqNo>0</dashboardSeqNo>
          <defaultForModule/>
          <internalSeqNo>2</internalSeqNo>
        </element>
        <element position="system.xlsx,navi,22">
          <id>itemConcept</id>
          <label>Concept</label>
          <naviModuleId>product</naviModuleId>
          <type>Entry</type>
          <entryGroup>itemGroup</entryGroup>
          <action/>
          <actionParams/>
          <isDefault>TRUE</isDefault>
          <viewName>itemConceptView</viewName>
          <dashboardLabel/>
          <showInDashboard>0</showInDashboard>
          <dashboardSeqNo>0</dashboardSeqNo>
          <defaultForModule/>
          <internalSeqNo>2</internalSeqNo>
        </element>
      </elements>
    </NaviEntry>
  </sheet>
  <sheet id="dashboard" position="system.xlsx,dashboard">
    <DashboardSection position="system.xlsx,dashboard,1">
      <elements id="default">
        <element position="system.xlsx,dashboard,4">
          <sectionId>alert</sectionId>
          <style>ent-purple</style>
        </element>
        <element position="system.xlsx,dashboard,5">
          <sectionId>marketPo</sectionId>
          <style>ent-gray</style>
        </element>
        <element position="system.xlsx,dashboard,6">
          <sectionId>dpo</sectionId>
          <style>ent-orange</style>
        </element>
        <element position="system.xlsx,dashboard,7">
          <sectionId>item</sectionId>
          <style>ent-blue</style>
        </element>
        <element position="system.xlsx,dashboard,8">
          <sectionId>vendor</sectionId>
          <style>ent-yellow</style>
        </element>
        <element position="system.xlsx,dashboard,9">
          <sectionId>invoice</sectionId>
          <style>ent-red</style>
        </element>
      </elements>
    </DashboardSection>
    <DashboardEntry position="system.xlsx,dashboard,12">
      <elements id="default">
        <element position="system.xlsx,dashboard,15">
          <entryId>alert1days</entryId>
          <sectionId>alert</sectionId>
          <counter>countAlertLate1</counter>
          <action>navigateSearchView</action>
          <actionParams>view=late&amp;source=dashboard&amp;days=1</actionParams>
        </element>
        <element position="system.xlsx,dashboard,16">
          <entryId>alert5days</entryId>
          <sectionId>alert</sectionId>
          <counter>countAlertLate5</counter>
          <action>navigateSearchView</action>
          <actionParams>view=late&amp;source=dashboard&amp;days=5</actionParams>
        </element>
        <element position="system.xlsx,dashboard,17">
          <entryId>alert10days</entryId>
          <sectionId>alert</sectionId>
          <counter>countAlertLate10</counter>
          <action>navigateSearchView</action>
          <actionParams>view=late&amp;source=dashboard&amp;days=10</actionParams>
        </element>
      </elements>
    </DashboardEntry>
  </sheet>
  <sheet id="fieldTypes" position="system.xlsx,fieldTypes">
    <FieldType position="system.xlsx,fieldTypes,1">
      <elements id="default">
        <element position="system.xlsx,fieldTypes,4">
          <entity_field_type>id</entity_field_type>
          <db_type_oracle>NVARCHAR2(32)</db_type_oracle>
          <db_type_pgsql>VARCHAR(32)</db_type_pgsql>
          <min_length>32</min_length>
          <max_length>32</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,5">
          <entity_field_type>string-s</entity_field_type>
          <db_type_oracle>NVARCHAR2(100)</db_type_oracle>
          <db_type_pgsql>VARCHAR(100)</db_type_pgsql>
          <min_length/>
          <max_length>100</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,6">
          <entity_field_type>string-m</entity_field_type>
          <db_type_oracle>NVARCHAR2(400)</db_type_oracle>
          <db_type_pgsql>VARCHAR(400)</db_type_pgsql>
          <min_length/>
          <max_length>400</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,7">
          <entity_field_type>string-l</entity_field_type>
          <db_type_oracle>NVARCHAR2(1000)</db_type_oracle>
          <db_type_pgsql>VARCHAR(1000)</db_type_pgsql>
          <min_length/>
          <max_length>1000</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,8">
          <entity_field_type>integer</entity_field_type>
          <db_type_oracle>NUMBER(20, 0)</db_type_oracle>
          <db_type_pgsql>NUMERIC(20, 0)</db_type_pgsql>
          <min_length/>
          <max_length>18</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,9">
          <entity_field_type>decimal-money</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,10">
          <entity_field_type>decimal-rate</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,11">
          <entity_field_type>decimal-cbm</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,12">
          <entity_field_type>decimal-dimension</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,13">
          <entity_field_type>decimal-weight</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,14">
          <entity_field_type>decimal-percentage</entity_field_type>
          <db_type_oracle>NUMBER(25, 5)</db_type_oracle>
          <db_type_pgsql>NUMERIC(25, 5)</db_type_pgsql>
          <min_length/>
          <max_length>20</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,15">
          <entity_field_type>boolean</entity_field_type>
          <db_type_oracle>NUMBER(1,0)</db_type_oracle>
          <db_type_pgsql>BOOLEAN</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,16">
          <entity_field_type>date</entity_field_type>
          <db_type_oracle>DATE</db_type_oracle>
          <db_type_pgsql>DATE</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,17">
          <entity_field_type>timestamp</entity_field_type>
          <db_type_oracle>TIMESTAMP</db_type_oracle>
          <db_type_pgsql>TIMESTAMP</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,18">
          <entity_field_type>blob</entity_field_type>
          <db_type_oracle>BLOB</db_type_oracle>
          <db_type_pgsql>BYTEA</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,19">
          <entity_field_type>clob</entity_field_type>
          <db_type_oracle>CLOB</db_type_oracle>
          <db_type_pgsql>TEXT</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,20">
          <entity_field_type>entity</entity_field_type>
          <db_type_oracle>NVARCHAR2(32)</db_type_oracle>
          <db_type_pgsql>VARCHAR(32)</db_type_pgsql>
          <min_length>32</min_length>
          <max_length>32</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,21">
          <entity_field_type>entity_60</entity_field_type>
          <db_type_oracle>NVARCHAR2(100)</db_type_oracle>
          <db_type_pgsql>VARCHAR(100)</db_type_pgsql>
          <min_length/>
          <max_length>100</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,22">
          <entity_field_type>entity-link</entity_field_type>
          <db_type_oracle>NVARCHAR2(32)</db_type_oracle>
          <db_type_pgsql>VARCHAR(32)</db_type_pgsql>
          <min_length>32</min_length>
          <max_length>32</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,23">
          <entity_field_type>entity-copied</entity_field_type>
          <db_type_oracle>NVARCHAR2(32)</db_type_oracle>
          <db_type_pgsql>VARCHAR(32)</db_type_pgsql>
          <min_length>32</min_length>
          <max_length>32</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,24">
          <entity_field_type>entity-dynamic</entity_field_type>
          <db_type_oracle>NVARCHAR2(32)</db_type_oracle>
          <db_type_pgsql>VARCHAR(32)</db_type_pgsql>
          <min_length>32</min_length>
          <max_length>32</max_length>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,25">
          <entity_field_type>array-text</entity_field_type>
          <db_type_oracle/>
          <db_type_pgsql>TEXT[]</db_type_pgsql>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,26">
          <entity_field_type>lookup</entity_field_type>
          <db_type_oracle/>
          <db_type_pgsql/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,27">
          <entity_field_type>collection</entity_field_type>
          <db_type_oracle/>
          <db_type_pgsql/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
        <element position="system.xlsx,fieldTypes,28">
          <entity_field_type>selection</entity_field_type>
          <db_type_oracle/>
          <db_type_pgsql/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
        </element>
      </elements>
    </FieldType>
  </sheet>
  <sheet id="seqDef" position="system.xlsx,seqDef">
    <SeqDef position="system.xlsx,seqDef,1">
      <elements id="default">
        <element position="system.xlsx,seqDef,4">
          <seqId>CBX_SEQ_FACT_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle/>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,5">
          <seqId>CBX_SEQ_CUST_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle/>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,6">
          <seqId>CBX_SEQ_VENDOR_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle/>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,7">
          <seqId>CBX_SEQ_CPO_CPO_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,8">
          <seqId>CBX_SEQ_VPO_VPO_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,9">
          <seqId>CBX_SEQ_CSO_CSO_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,10">
          <seqId>CBX_SEQ_MPO_MPO_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,11">
          <seqId>CBX_SEQ_OFFER_SHEET_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,12">
          <seqId>CBX_SEQ_COMMITMENT_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,13">
          <seqId>CBX_SEQ_RFQ_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,14">
          <seqId>CBX_SEQ_RFI_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,15">
          <seqId>CBX_SEQ_RFS_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,16">
          <seqId>CBX_SEQ_VQ_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,17">
          <seqId>CBX_SEQ_SOURCING_RECORD_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,18">
          <seqId>CBX_SEQ_FACT_AUDIT_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,19">
          <seqId>CBX_SEQ_IC_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,20">
          <seqId>CBX_SEQ_QC_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,21">
          <seqId>CBX_SEQ_CI_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,22">
          <seqId>CBX_SEQ_SHIP_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,23">
          <seqId>CBX_SEQ_SHIPMENT_ADVICE_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,24">
          <seqId>CBX_SEQ_SHIPMENT_BOOKING_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,25">
          <seqId>CBX_SEQ_CBX_SAMPLE_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,26">
          <seqId>CBX_SEQ_ITEM_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,27">
          <seqId>CBX_SEQ_CS_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,28">
          <seqId>CBX_SEQ_PROGRAM_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,29">
          <seqId>CBX_SEQ_CATALOG_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,30">
          <seqId>CBX_SEQ_COMPONENT_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,31">
          <seqId>CBX_SEQ_FORW_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle/>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,32">
          <seqId>CBX_SEQ_EAR_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,33">
          <seqId>CBX_SEQ_PROJECT_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,34">
          <seqId>CBX_SEQ_COLOR_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,35">
          <seqId>CBX_SEQ_LABEL_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,36">
          <seqId>CBX_SEQ_SPEC_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,37">
          <seqId>CBX_SEQ_EXTERNAL_DOC_TITLE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,38">
          <seqId>CBX_SEQ_APRV_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,39">
          <seqId>CBX_SEQ_OPC_TMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,40">
          <seqId>CBX_SEQ_CPM_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,41">
          <seqId>CBX_SEQ_FACT_AUDIT_TMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,42">
          <seqId>CBX_SEQ_QC_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,43">
          <seqId>CBX_SEQ_MEASURE_TMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,44">
          <seqId>CBX_SEQ_SIZE_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,45">
          <seqId>CBX_SEQ_VENDOR_CHA_TMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,46">
          <seqId>CBX_SEQ_CONDITION_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,47">
          <seqId>CBX_SEQ_CPM_TASK_TMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,48">
          <seqId>CBX_SEQ_CUST_FIELD_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,49">
          <seqId>CBX_SEQ_DATA_LIST_TYPE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,50">
          <seqId>CBX_SEQ_DOMAIN_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,51">
          <seqId>CBX_SEQ_EVENT_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,52">
          <seqId>CBX_SEQ_GROUP_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,53">
          <seqId>CBX_SEQ_HCL_TYPE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,54">
          <seqId>CBX_SEQ_HCL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,55">
          <seqId>CBX_SEQ_LOOKUP_BOOK_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,56">
          <seqId>CBX_SEQ_NOTIFICATION_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,57">
          <seqId>CBX_SEQ_ROLE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,58">
          <seqId>CBX_SEQ_USER_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,59">
          <seqId>CBX_SEQ_CLAIM_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,60">
          <seqId>CBX_SEQ_LC_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,61">
          <seqId>CBX_SEQ_VI_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,62">
          <seqId>CBX_SEQ_COST_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,63">
          <seqId>CBX_SEQ_CODE_LIST_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,64">
          <seqId>CBX_SEQ_VPO_ACK_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,65">
          <seqId>CBX_SEQ_ACCESS_OBJECT_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,66">
          <seqId>CBX_SEQ_DEFAULT_PROFILE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,67">
          <seqId>CBX_SEQ_SYSTEM_FILE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,68">
          <seqId>CBX_SEQ_PACKAING_ARTWORK_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,69">
          <seqId>CBX_SEQ_PACKAGING_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,70">
          <seqId>CBX_SEQ_MATERIAL_ATTR_TEMPL</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,71">
          <seqId>CBX_SEQ_ARTWORK_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,72">
          <seqId>CBX_SEQ_COLOR_PALETTE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,73">
          <seqId>CBX_SEQ_MATERIAL_PALEETE_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,74">
          <seqId>CBX_SEQ_ARTWORK_PALEETE_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,75">
          <seqId>CBX_SEQ_CNT_VIEW_ADMIN_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,76">
          <seqId>CBX_SEQ_PATTERN_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,77">
          <seqId>CBX_SEQ_PRT_FORM_EXPT_TMPL_REF</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,78">
          <seqId>CBX_SEQ_SAMPLE_REQUEST_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,79">
          <seqId>CBX_SEQ_TRACKER_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,80">
          <seqId>CBX_SEQ_SAMPLE_REQ_TMPL_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,81">
          <seqId>CBX_SEQ_MATERIAL_REQ_TMPL_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,82">
          <seqId>CBX_SEQ_DOCUMENT_REQ_TMPL_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,83">
          <seqId>CBX_SEQ_SAMPLE_DETAIL_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,84">
          <seqId>CBX_SEQ_MATERIAL_DETAIL_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,85">
          <seqId>CBX_SEQ_DOCUMENT_DETAIL_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,86">
          <seqId>CBX_SEQ_EVALUATION_TEMPLATE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,87">
          <seqId>CBX_SEQ_PARTY_TEMPLATE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,88">
          <seqId>CBX_SEQ_SAMPLE_EVALUATION</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,89">
          <seqId>CBX_SEQ_ACC_REF_KEY</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,90">
          <seqId>CBX_SEQ_MES_REF_KEY</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,91">
          <seqId>CBX_SEQ_GDR_REF_KEY</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,92">
          <seqId>CBX_SEQ_INVITATION_REQUEST_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,93">
          <seqId>CBX_SEQ_INVITED_VENDORS_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,94">
          <seqId>CBX_SEQ_AGRM_TEMPL_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,95">
          <seqId>CBX_SEQ_CONTRACT_FILE_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,96">
          <seqId>CBX_SEQ_DOMAIN_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,97">
          <seqId>CBX_SEQ_INSPECT_RPT_TMPL_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,98">
          <seqId>CBX_SEQ_IB_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,99">
          <seqId>CBX_SEQ_IR_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,100">
          <seqId>CBX_SEQ_PROJECT_NEW_ITEM</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,101">
          <seqId>CBX_SEQ_VQ2_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,102">
          <seqId>CBX_SEQ_QQ_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,103">
          <seqId>CBX_SEQ_VC_REF_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,104">
          <seqId>CBX_SEQ_FC_REF_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,105">
          <seqId>CBX_SEQ_PC_REF_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,106">
          <seqId>CBX_SEQ_VC_ACTIVITY_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,107">
          <seqId>CBX_SEQ_FC_ACTIVITY_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,108">
          <seqId>CBX_SEQ_PC_ACTIVITY_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>D</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,109">
          <seqId>CBX_SEQ_COPTMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,110">
          <seqId>CBX_SEQ_SHIPMENT_COMPLIANCE_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,111">
          <seqId>CBX_SEQ_SHARE_FILE_FILE_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,112">
          <seqId>CBX_SEQ_SPECIFICATION_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,113">
          <seqId>CBX_SEQ_INGREDIENT_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,114">
          <seqId>CBX_SEQ_PACKING_LIST_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,115">
          <seqId>CBX_SEQ_TEST_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle/>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,116">
          <seqId>CBX_SEQ_TEST_TEMPL_REF_NO</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>Y</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,117">
          <seqId>CBX_SEQ_TEST_ACCREDITATION_CODE</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
        <element position="system.xlsx,seqDef,118">
          <seqId>CBX_SEQ_REQUEST_ID</seqId>
          <startWith>1</startWith>
          <maxValue>999999</maxValue>
          <incrementBy/>
          <cycle>M</cycle>
          <cacheSize/>
        </element>
      </elements>
    </SeqDef>
  </sheet>
  <sheet id="seqDef_extend" position="system.xlsx,seqDef_extend">
    <SeqDef position="system.xlsx,seqDef_extend,1">
      <elements id="default"/>
    </SeqDef>
  </sheet>
  <sheet id="commonFields" position="system.xlsx,commonFields">
    <CommonField position="system.xlsx,commonFields,1">
      <elements id="all">
        <element position="system.xlsx,commonFields,4">
          <entity_field_id>id</entity_field_id>
          <entity_field_type>id</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator>com.core.cbx.data.generator.UUIDGenerator</generator>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,5">
          <entity_field_id>revision</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,6">
          <entity_field_id>entityVersion</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,7">
          <entity_field_id>domainId</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,8">
          <entity_field_id>hubDomainId</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,9">
          <entity_field_id>isForReference</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
      </elements>
      <elements id="main">
        <element position="system.xlsx,commonFields,14">
          <entity_field_id>version</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,15">
          <entity_field_id>status</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,16">
          <entity_field_id>docStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,17">
          <entity_field_id>editingStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,18">
          <entity_field_id>createUser</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,19">
          <entity_field_id>createUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,20">
          <entity_field_id>updateUser</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,21">
          <entity_field_id>updateUserName</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,22">
          <entity_field_id>createdOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory>1</mandatory>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,23">
          <entity_field_id>updatedOn</entity_field_id>
          <entity_field_type>timestamp</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,24">
          <entity_field_id>integrationSource</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,25">
          <entity_field_id>integrationStatus</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,26">
          <entity_field_id>integrationNote</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,27">
          <entity_field_id>isCpmInitialized</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>Y</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
        <element position="system.xlsx,commonFields,28">
          <entity_field_id>isLatest</entity_field_id>
          <entity_field_type>boolean</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack/>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name/>
        </element>
      </elements>
      <elements id="versionTracking"/>
      <elements id="inner">
        <element position="system.xlsx,commonFields,37">
          <entity_field_id>internalSeqNo</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>N</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name>n/a</report_column_name>
        </element>
        <element position="system.xlsx,commonFields,38">
          <entity_field_id>duid</entity_field_id>
          <entity_field_type>string-s</entity_field_type>
          <entity_lookup/>
          <mandatory/>
          <unique/>
          <needTrack>N</needTrack>
          <min_length/>
          <max_length/>
          <min_value/>
          <max_value/>
          <min_date/>
          <max_date/>
          <constant/>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name>DUID</report_column_name>
        </element>
      </elements>
    </CommonField>
  </sheet>
  <sheet id="customFields" position="system.xlsx,customFields">
    <CustomField position="system.xlsx,customFields,1">
      <elements id="custom">
        <element position="system.xlsx,customFields,4">
          <custom_field_type>Text</custom_field_type>
          <entity_field_id>custText</entity_field_id>
          <entity_field_type>string-l</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_TEXT</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,5">
          <custom_field_type>Date</custom_field_type>
          <entity_field_id>custDate</entity_field_id>
          <entity_field_type>date</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_DATE</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,6">
          <custom_field_type>Number</custom_field_type>
          <entity_field_id>custNumber</entity_field_id>
          <entity_field_type>integer</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_NUMBER</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,7">
          <custom_field_type>Decimal</custom_field_type>
          <entity_field_id>custDecimal</entity_field_id>
          <entity_field_type>decimal-rate</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_DECIMAL</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,8">
          <custom_field_type>Codelist</custom_field_type>
          <entity_field_id>custCodelist</entity_field_id>
          <entity_field_type>string-m</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType>codelist</dataType>
          <report_column_name_pattern>CUSTOM_FIELD_CODE_LIST_CODE</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,9">
          <custom_field_type>MemoText</custom_field_type>
          <entity_field_id>custMemoText</entity_field_id>
          <entity_field_type>string-xl</entity_field_type>
          <entity_lookup/>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_MEMO_TEXT</report_column_name_pattern>
          <default_count>7</default_count>
        </element>
        <element position="system.xlsx,customFields,10">
          <custom_field_type>Hcl</custom_field_type>
          <entity_field_id>custHcl</entity_field_id>
          <entity_field_type>entity</entity_field_type>
          <entity_lookup>HclNode.id</entity_lookup>
          <needTrack>Y</needTrack>
          <description/>
          <generator/>
          <dataType/>
          <report_column_name_pattern>CUSTOM_FIELD_HCL_CODE_SYS_ID</report_column_name_pattern>
          <default_count>5</default_count>
        </element>
      </elements>
    </CustomField>
  </sheet>
</system>
